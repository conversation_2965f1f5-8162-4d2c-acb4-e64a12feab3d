const colors = require('colors');
const jwt = require('jsonwebtoken');
const dataModel = require('../models/mysql-real-model');

// JWT 密钥 (与 authRoutes.js 保持一致)
const JWT_SECRET = 'xiya_signature_server_secret_key_2024';

// 鉴权中间件 - 支持API Key和JWT Bearer Token
async function authenticate(req, res, next) {
    const ip = req.ip || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'] || '';

    try {
        // 检查是否是JWT Bearer Token
        const authHeader = req.headers['authorization'];
        if (authHeader && authHeader.startsWith('Bearer ')) {
            const token = authHeader.split(' ')[1];
            
            try {
                // 验证JWT token
                const decoded = jwt.verify(token, JWT_SECRET);
                req.user = decoded;
                console.log(`✅ JWT认证成功 - 用户: ${decoded.username}, IP: ${ip}`.green);
                return next();
            } catch (jwtError) {
                // JWT验证失败，记录未授权访问
                const requestBody = JSON.stringify(req.body) || req.rawBody || '';

                dataModel.logUnauthorizedAccess({
                    ip,
                    endpoint: req.path,
                    method: req.method,
                    user_agent: userAgent,
                    auth_key: token,
                    reason: 'JWT Token无效或已过期',
                    request_data: requestBody
                }).catch(err => console.error('记录未授权访问失败:'.red, err));

                console.log(`🚫 JWT认证失败，来源IP: ${ip}，原因: ${jwtError.message}`.red.bold);

                return res.status(403).json({
                    code: 403,
                    msg: "JWT Token无效或已过期"
                });
            }
        }

        // 如果没有JWT Token，则认证失败
        const requestBody = JSON.stringify(req.body) || req.rawBody || '';

        dataModel.logUnauthorizedAccess({
            ip,
            endpoint: req.path,
            method: req.method,
            user_agent: userAgent,
            auth_key: authHeader || 'none',
            reason: '缺少JWT Token',
            request_data: requestBody
        }).catch(err => console.error('记录未授权访问失败:'.red, err));

        console.log(`🚫 认证失败，来源IP: ${ip}，原因: 缺少JWT Token`.red.bold);

        return res.status(401).json({
            code: 401,
            msg: "需要登录，请提供有效的JWT Token"
        });

    } catch (error) {
        console.error('鉴权中间件错误:', error);
        res.status(500).json({
            code: 500,
            msg: "服务器内部错误"
        });
    }
}

module.exports = {
    authenticate
};
