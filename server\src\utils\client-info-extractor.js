const geoip = require('geoip-lite');
const UAParser = require('ua-parser-js');

/**
 * 客户端信息提取器
 * 用于提取和分析客户端的详细信息
 */
class ClientInfoExtractor {
    
    /**
     * 提取完整的客户端信息
     * @param {Object} req - Express请求对象
     * @returns {Object} 客户端信息
     */
    static extractFullClientInfo(req) {
        const ip = this.extractIPAddress(req);
        const userAgent = req.get('User-Agent') || '';
        const headers = this.extractHeaders(req);
        const geoInfo = this.getGeoLocation(ip);
        const deviceInfo = this.parseUserAgent(userAgent);
        const networkInfo = this.extractNetworkInfo(req);
        const securityInfo = this.extractSecurityInfo(req);
        
        return {
            // 基础信息
            ip_address: ip,
            user_agent: userAgent,
            timestamp: new Date().toISOString(),
            
            // 地理位置信息
            geo: geoInfo,
            
            // 设备信息
            device: deviceInfo,
            
            // 网络信息
            network: networkInfo,
            
            // 请求头信息
            headers: headers,
            
            // 安全相关信息
            security: securityInfo,
            
            // 请求特征
            request_signature: this.generateRequestSignature(req)
        };
    }
    
    /**
     * 提取真实IP地址
     * @param {Object} req - Express请求对象
     * @returns {string} IP地址
     */
    static extractIPAddress(req) {
        // 按优先级检查各种IP头
        const ipHeaders = [
            'x-forwarded-for',
            'x-real-ip',
            'x-client-ip',
            'cf-connecting-ip', // Cloudflare
            'x-forwarded',
            'forwarded-for',
            'forwarded'
        ];
        
        for (const header of ipHeaders) {
            const value = req.get(header);
            if (value) {
                // 处理多个IP的情况，取第一个
                const ip = value.split(',')[0].trim();
                if (this.isValidIP(ip)) {
                    return ip;
                }
            }
        }
        
        // 回退到连接IP
        return req.connection?.remoteAddress || 
               req.socket?.remoteAddress || 
               req.ip || 
               'unknown';
    }
    
    /**
     * 验证IP地址格式
     * @param {string} ip - IP地址
     * @returns {boolean} 是否有效
     */
    static isValidIP(ip) {
        const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
        const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
        return ipv4Regex.test(ip) || ipv6Regex.test(ip);
    }
    
    /**
     * 获取地理位置信息
     * @param {string} ip - IP地址
     * @returns {Object} 地理位置信息
     */
    static getGeoLocation(ip) {
        try {
            if (ip === 'unknown' || ip.startsWith('127.') || ip.startsWith('192.168.') || ip.startsWith('10.')) {
                return {
                    country: 'Local',
                    region: 'Local',
                    city: 'Local',
                    timezone: 'Local',
                    isp: 'Local Network'
                };
            }
            
            const geo = geoip.lookup(ip);
            if (geo) {
                return {
                    country: geo.country || 'Unknown',
                    region: geo.region || 'Unknown',
                    city: geo.city || 'Unknown',
                    timezone: geo.timezone || 'Unknown',
                    coordinates: geo.ll ? { lat: geo.ll[0], lon: geo.ll[1] } : null,
                    isp: 'Unknown'
                };
            }
        } catch (error) {
            console.error('获取地理位置信息失败:', error);
        }
        
        return {
            country: 'Unknown',
            region: 'Unknown', 
            city: 'Unknown',
            timezone: 'Unknown',
            isp: 'Unknown'
        };
    }
    
    /**
     * 解析User-Agent
     * @param {string} userAgent - User-Agent字符串
     * @returns {Object} 设备信息
     */
    static parseUserAgent(userAgent) {
        try {
            const parser = new UAParser(userAgent);
            const result = parser.getResult();
            
            return {
                browser: {
                    name: result.browser.name || 'Unknown',
                    version: result.browser.version || 'Unknown'
                },
                os: {
                    name: result.os.name || 'Unknown',
                    version: result.os.version || 'Unknown'
                },
                device: {
                    type: result.device.type || 'desktop',
                    vendor: result.device.vendor || 'Unknown',
                    model: result.device.model || 'Unknown'
                },
                engine: {
                    name: result.engine.name || 'Unknown',
                    version: result.engine.version || 'Unknown'
                },
                cpu: {
                    architecture: result.cpu.architecture || 'Unknown'
                }
            };
        } catch (error) {
            console.error('解析User-Agent失败:', error);
            return {
                browser: { name: 'Unknown', version: 'Unknown' },
                os: { name: 'Unknown', version: 'Unknown' },
                device: { type: 'Unknown', vendor: 'Unknown', model: 'Unknown' },
                engine: { name: 'Unknown', version: 'Unknown' },
                cpu: { architecture: 'Unknown' }
            };
        }
    }
    
    /**
     * 提取网络信息
     * @param {Object} req - Express请求对象
     * @returns {Object} 网络信息
     */
    static extractNetworkInfo(req) {
        return {
            protocol: req.protocol || 'http',
            method: req.method || 'GET',
            url: req.originalUrl || req.url || '/',
            host: req.get('host') || 'unknown',
            origin: req.get('origin') || null,
            referer: req.get('referer') || null,
            connection: req.get('connection') || 'unknown',
            accept_language: req.get('accept-language') || 'unknown',
            accept_encoding: req.get('accept-encoding') || 'unknown',
            content_type: req.get('content-type') || null,
            content_length: parseInt(req.get('content-length') || '0')
        };
    }
    
    /**
     * 提取关键请求头
     * @param {Object} req - Express请求对象
     * @returns {Object} 请求头信息
     */
    static extractHeaders(req) {
        const importantHeaders = [
            'user-agent', 'accept', 'accept-language', 'accept-encoding',
            'connection', 'upgrade-insecure-requests', 'sec-fetch-dest',
            'sec-fetch-mode', 'sec-fetch-site', 'sec-fetch-user',
            'cache-control', 'pragma', 'dnt', 'authorization'
        ];
        
        const headers = {};
        importantHeaders.forEach(header => {
            const value = req.get(header);
            if (value) {
                headers[header] = value;
            }
        });
        
        return headers;
    }
    
    /**
     * 提取安全相关信息
     * @param {Object} req - Express请求对象
     * @returns {Object} 安全信息
     */
    static extractSecurityInfo(req) {
        return {
            is_https: req.secure || req.get('x-forwarded-proto') === 'https',
            has_auth: !!req.get('authorization'),
            proxy_detected: !!(req.get('x-forwarded-for') || req.get('x-real-ip')),
            suspicious_headers: this.detectSuspiciousHeaders(req),
            fingerprint: this.generateFingerprint(req)
        };
    }
    
    /**
     * 检测可疑请求头
     * @param {Object} req - Express请求对象
     * @returns {Array} 可疑请求头列表
     */
    static detectSuspiciousHeaders(req) {
        const suspicious = [];
        const headers = req.headers;
        
        // 检测自动化工具特征
        const automationSignatures = [
            'curl', 'wget', 'python', 'java', 'go-http', 'node-fetch',
            'axios', 'requests', 'urllib', 'okhttp', 'apache-httpclient'
        ];
        
        const userAgent = (headers['user-agent'] || '').toLowerCase();
        automationSignatures.forEach(signature => {
            if (userAgent.includes(signature)) {
                suspicious.push(`automation_tool:${signature}`);
            }
        });
        
        // 检测缺少常见浏览器头
        const commonBrowserHeaders = ['accept', 'accept-language', 'accept-encoding'];
        const missingHeaders = commonBrowserHeaders.filter(header => !headers[header]);
        if (missingHeaders.length >= 2) {
            suspicious.push(`missing_browser_headers:${missingHeaders.join(',')}`);
        }
        
        return suspicious;
    }
    
    /**
     * 生成请求指纹
     * @param {Object} req - Express请求对象
     * @returns {string} 请求指纹
     */
    static generateFingerprint(req) {
        const crypto = require('crypto');
        
        const components = [
            req.get('user-agent') || '',
            req.get('accept') || '',
            req.get('accept-language') || '',
            req.get('accept-encoding') || '',
            req.method || '',
            req.get('connection') || ''
        ];
        
        const fingerprint = components.join('|');
        return crypto.createHash('md5').update(fingerprint).digest('hex');
    }
    
    /**
     * 生成请求签名
     * @param {Object} req - Express请求对象
     * @returns {string} 请求签名
     */
    static generateRequestSignature(req) {
        const crypto = require('crypto');
        
        const signature = {
            method: req.method,
            url: req.originalUrl || req.url,
            headers_count: Object.keys(req.headers).length,
            has_body: !!(req.body && Object.keys(req.body).length > 0),
            timestamp: Date.now()
        };
        
        return crypto.createHash('sha256')
            .update(JSON.stringify(signature))
            .digest('hex')
            .substring(0, 16);
    }
}

module.exports = ClientInfoExtractor;
