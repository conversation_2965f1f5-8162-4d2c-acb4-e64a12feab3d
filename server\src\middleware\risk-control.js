const mysqlModel = require('../models/mysql-real-model');
const colors = require('colors');

// 临时禁用增强功能，避免依赖问题
const ClientInfoExtractor = null;
const RequestDataProcessor = null;

/**
 * 简化的请求数据处理函数
 */
function processRequestData(req) {
  try {
    const requestData = {
      // 基础信息
      method: req.method || 'GET',
      url: req.originalUrl || req.url || '/',
      protocol: req.protocol || 'http',

      // 请求头（过滤敏感信息）
      headers: sanitizeHeaders(req.headers || {}),

      // 查询参数
      query: req.query || {},

      // 请求体（解析后的格式）
      body: req.body || null,

      // 原始请求体（字符串格式，用于展示完整的请求内容）
      raw_body: req.rawBody || null,

      // 请求体类型
      body_type: req.get('content-type') || null,

      // 请求统计
      stats: {
        content_length: parseInt(req.get('content-length') || '0'),
        param_count: Object.keys(req.query || {}).length,
        body_size: calculateBodySize(req.body),
        has_body: !!(req.body && Object.keys(req.body).length > 0)
      },

      // 时间戳
      timestamp: new Date().toISOString()
    };

    return requestData;
  } catch (error) {
    console.error('处理请求数据失败:', error);
    return {
      error: 'Failed to process request data',
      method: req.method,
      url: req.url,
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * 清理请求头，移除敏感信息
 */
function sanitizeHeaders(headers) {
  const sanitized = {};
  const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];

  Object.keys(headers).forEach(key => {
    const lowerKey = key.toLowerCase();
    if (sensitiveHeaders.includes(lowerKey)) {
      sanitized[key] = maskSensitiveValue(headers[key]);
    } else {
      sanitized[key] = headers[key];
    }
  });

  return sanitized;
}

/**
 * 掩码敏感值
 */
function maskSensitiveValue(value) {
  if (!value) return value;

  const str = String(value);
  if (str.length <= 4) {
    return '***';
  } else if (str.length <= 8) {
    return str.substring(0, 2) + '***';
  } else {
    return str.substring(0, 4) + '***' + str.substring(str.length - 2);
  }
}

/**
 * 计算请求体大小
 */
function calculateBodySize(body) {
  if (!body) return 0;

  try {
    if (typeof body === 'string') {
      return Buffer.byteLength(body, 'utf8');
    }
    return Buffer.byteLength(JSON.stringify(body), 'utf8');
  } catch (error) {
    return 0;
  }
}

// 请求验证失败计数器（内存缓存）
const validationFailureCounter = new Map();
const VALIDATION_FAILURE_WINDOW = 5 * 60 * 1000; // 5分钟窗口
const MAX_VALIDATION_FAILURES = 10; // 5分钟内最多10次验证失败

// 频繁错误请求检测
const errorRequestCounter = new Map();
const ERROR_REQUEST_WINDOW = 10 * 60 * 1000; // 10分钟窗口
const MAX_ERROR_REQUESTS = 20; // 10分钟内最多20次错误请求

/**
 * 检测验证失败频率
 */
function checkValidationFailureRate(clientInfo) {
  const key = `${clientInfo.ip_address}_${clientInfo.uin || 'unknown'}`;
  const now = Date.now();

  // 获取或创建计数器
  if (!validationFailureCounter.has(key)) {
    validationFailureCounter.set(key, []);
  }

  const failures = validationFailureCounter.get(key);

  // 清理过期记录
  const validFailures = failures.filter(timestamp => now - timestamp < VALIDATION_FAILURE_WINDOW);
  validationFailureCounter.set(key, validFailures);

  // 添加当前失败记录
  validFailures.push(now);

  // 检查是否超过阈值
  if (validFailures.length > MAX_VALIDATION_FAILURES) {
    return {
      isExceeded: true,
      count: validFailures.length,
      window: VALIDATION_FAILURE_WINDOW / 1000 / 60 // 转换为分钟
    };
  }

  return {
    isExceeded: false,
    count: validFailures.length,
    window: VALIDATION_FAILURE_WINDOW / 1000 / 60
  };
}

/**
 * 检测错误请求频率
 */
function checkErrorRequestRate(clientInfo) {
  const key = `${clientInfo.ip_address}_${clientInfo.uin || 'unknown'}`;
  const now = Date.now();

  // 获取或创建计数器
  if (!errorRequestCounter.has(key)) {
    errorRequestCounter.set(key, []);
  }

  const errors = errorRequestCounter.get(key);

  // 清理过期记录
  const validErrors = errors.filter(timestamp => now - timestamp < ERROR_REQUEST_WINDOW);
  errorRequestCounter.set(key, validErrors);

  // 添加当前错误记录
  validErrors.push(now);

  // 检查是否超过阈值
  if (validErrors.length > MAX_ERROR_REQUESTS) {
    return {
      isExceeded: true,
      count: validErrors.length,
      window: ERROR_REQUEST_WINDOW / 1000 / 60 // 转换为分钟
    };
  }

  return {
    isExceeded: false,
    count: validErrors.length,
    window: ERROR_REQUEST_WINDOW / 1000 / 60
  };
}

/**
 * 检测异常请求模式
 */
function detectAbnormalRequestPatterns(req, clientInfo) {
  const abnormalPatterns = [];

  // 检测1: 请求体过大
  const contentLength = parseInt(req.get('content-length') || '0');
  if (contentLength > 100 * 1024) { // 100KB
    abnormalPatterns.push({
      type: 'oversized_request',
      description: `请求体过大: ${contentLength} bytes`,
      severity: 'high'
    });
  }

  // 检测2: 异常User-Agent
  const userAgent = req.get('User-Agent') || '';
  const suspiciousUAPatterns = [
    /curl/i, /wget/i, /python/i, /java/i, /go-http/i,
    /scanner/i, /bot/i, /crawler/i, /spider/i
  ];

  for (const pattern of suspiciousUAPatterns) {
    if (pattern.test(userAgent)) {
      abnormalPatterns.push({
        type: 'suspicious_user_agent',
        description: `可疑User-Agent: ${userAgent}`,
        severity: 'medium'
      });
      break;
    }
  }

  // 检测3: 缺少常见请求头
  const commonHeaders = ['accept', 'accept-language', 'accept-encoding'];
  const missingHeaders = commonHeaders.filter(header => !req.get(header));
  if (missingHeaders.length >= 2) {
    abnormalPatterns.push({
      type: 'missing_common_headers',
      description: `缺少常见请求头: ${missingHeaders.join(', ')}`,
      severity: 'low'
    });
  }

  // 检测4: 异常请求频率检测已移除（避免正常快速请求被误判）

  return abnormalPatterns;
}

/**
 * 风控检测中间件
 * 在请求处理前进行风控检测
 */
const riskControlMiddleware = async (req, res, next) => {
  try {
    // 提取客户端信息（简化版本）
    const clientInfo = {
      // 基础信息
      ip_address: req.ip || req.connection?.remoteAddress || req.headers['x-forwarded-for']?.split(',')[0] || 'unknown',
      user_agent: req.get('User-Agent') || '',
      timestamp: new Date().toISOString(),

      // 业务信息（不校验格式，只检查是否存在）
      uin: req.body?.uin || req.query?.uin || null,
      device_id: req.body?.androidId || req.query?.androidId || req.body?.device_id || req.query?.device_id || null,
      username: req.body?.username || req.query?.username || null,
      session_id: req.body?.session_id || req.query?.session_id || null,
      app_version: req.body?.app_version || req.query?.app_version || req.body?.version || req.query?.version || null,
      platform: req.body?.platform || req.query?.platform || null,

      // 请求基础信息
      method: req.method,
      url: req.originalUrl || req.url,

      // 重要请求头
      headers: {
        'accept': req.get('accept'),
        'accept-language': req.get('accept-language'),
        'accept-encoding': req.get('accept-encoding'),
        'connection': req.get('connection'),
        'host': req.get('host'),
        'origin': req.get('origin'),
        'referer': req.get('referer'),
        'content-type': req.get('content-type'),
        'x-xiya-authkey': req.get('x-xiya-authkey') // 添加自定义认证头
      }
    };

    // 检查是否启用风控
    const isRiskControlEnabled = await mysqlModel.getSetting('enable_risk_control', true);
    if (!isRiskControlEnabled) {
      return next();
    }

    // 检查白名单
    const isWhitelisted = await mysqlModel.isWhitelisted(clientInfo);
    if (isWhitelisted) {
      console.log(`✅ 白名单用户通过: ${clientInfo.ip_address}`.green);
      return next();
    }

    // 检查黑名单
    const blacklistResult = await mysqlModel.isBlacklisted(clientInfo);
    if (blacklistResult.isBlacklisted) {
      console.log(`🚫 黑名单用户拦截: ${clientInfo.ip_address}`.red);

      // 处理请求数据
      const processedRequestData = processRequestData(req);

      // 记录风控日志
      await mysqlModel.recordRiskData({
        username: clientInfo.username,
        device_id: clientInfo.device_id,
        ip_address: clientInfo.ip_address,
        uin: clientInfo.uin,
        risk_content: '黑名单用户访问',
        matched_keywords: [],
        risk_score: 100,
        risk_level: 'critical',
        auto_blocked: true,
        client_info: clientInfo,
        request_data: JSON.stringify(processedRequestData)
      });

      return res.status(403).json({
        code: 403,
        msg: '访问被拒绝',
        error: '您的账户已被列入黑名单',
        ban_info: blacklistResult.record
      });
    }

    // 检测异常请求模式
    const abnormalPatterns = detectAbnormalRequestPatterns(req, clientInfo);
    if (abnormalPatterns.length > 0) {
      const highSeverityPatterns = abnormalPatterns.filter(p => p.severity === 'high');

      if (highSeverityPatterns.length > 0) {
        console.log(`🚨 检测到高风险异常模式: ${clientInfo.ip_address}`.red);

        // 处理请求数据
        const processedRequestData = processRequestData(req);
        processedRequestData.risk_patterns = abnormalPatterns; // 添加风险模式信息

        await mysqlModel.recordRiskData({
          username: clientInfo.username,
          device_id: clientInfo.device_id,
          ip_address: clientInfo.ip_address,
          uin: clientInfo.uin,
          risk_content: '异常请求模式检测',
          matched_keywords: highSeverityPatterns.map(p => p.description),
          risk_score: 85,
          risk_level: 'high',
          auto_blocked: true,
          client_info: clientInfo,
          request_data: JSON.stringify(processedRequestData)
        });

        return res.status(403).json({
          code: 403,
          msg: '请求被拒绝',
          error: '检测到异常请求模式',
          patterns: highSeverityPatterns.map(p => p.type)
        });
      } else {
        // 记录中低风险模式但不阻止
        console.log(`⚠️  检测到可疑请求模式: ${clientInfo.ip_address} - ${abnormalPatterns.map(p => p.type).join(', ')}`.yellow);
      }
    }

    // 构建检测内容
    const detectContent = buildDetectContent(req);
    
    if (detectContent) {
      // 进行风控内容检测
      const riskResult = await mysqlModel.checkRiskContent(detectContent, clientInfo);
      
      if (riskResult.isRisk) {
        console.log(`⚠️  风险检测: ${clientInfo.ip_address} - 评分: ${riskResult.riskScore}`.yellow);
        
        // 处理请求数据
        const processedRequestData = processRequestData(req);

        // 记录风控数据
        const recordId = await mysqlModel.recordRiskData({
          username: clientInfo.username,
          device_id: clientInfo.device_id,
          ip_address: clientInfo.ip_address,
          uin: clientInfo.uin,
          risk_content: detectContent,
          matched_keywords: riskResult.matchedKeywords,
          risk_score: riskResult.riskScore,
          risk_level: riskResult.riskLevel,
          auto_blocked: riskResult.autoBlock,
          client_info: clientInfo,
          request_data: JSON.stringify(processedRequestData)
        });

        // 如果需要自动拦截
        if (riskResult.autoBlock) {
          console.log(`🛑 自动拦截高风险请求: ${clientInfo.ip_address}`.red);
          
          // 可选：自动加入黑名单
          const autoBlacklistEnabled = await mysqlModel.getSetting('auto_blacklist_enabled', false);
          if (autoBlacklistEnabled) {
            await mysqlModel.addToBlacklist({
              username: clientInfo.username,
              device_id: clientInfo.device_id,
              ip_address: clientInfo.ip_address,
              uin: clientInfo.uin,
              risk_content: detectContent,
              ban_reason: '风控系统自动封禁 - 高风险评分',
              ban_type: 'temporary',
              ban_duration: 24, // 24小时
              creator_id: null
            });
            console.log(`🚫 自动加入黑名单: ${clientInfo.ip_address}`.red);
          }

          return res.status(403).json({
            code: 403,
            msg: '请求被拒绝',
            error: '检测到高风险内容',
            risk_info: {
              riskScore: riskResult.riskScore,
              riskLevel: riskResult.riskLevel,
              matchedKeywords: riskResult.matchedKeywords.map(kw => kw.keyword),
              recordId: recordId
            }
          });
        }

        // 中等风险：记录但允许通过
        if (riskResult.riskScore > 50) {
          console.log(`⚠️  中等风险通过: ${clientInfo.ip_address} - 评分: ${riskResult.riskScore}`.yellow);
          
          // 在响应头中添加风险信息
          res.set('X-Risk-Score', riskResult.riskScore.toString());
          res.set('X-Risk-Level', riskResult.riskLevel);
          res.set('X-Risk-Record-Id', recordId.toString());
        }
      }
    }

    // 继续处理请求
    next();

  } catch (error) {
    console.error('风控检测中间件错误:'.red, error);
    
    // 风控系统出错时，根据配置决定是否继续
    const continueOnError = await mysqlModel.getSetting('risk_continue_on_error', true);
    if (continueOnError) {
      console.log('⚠️  风控系统错误，继续处理请求'.yellow);
      next();
    } else {
      res.status(500).json({
        code: 500,
        msg: '风控系统错误',
        error: 'Risk control system error'
      });
    }
  }
};

/**
 * 构建用于检测的内容
 */
function buildDetectContent(req) {
  const parts = [];
  
  // URL路径
  if (req.path) {
    parts.push(req.path);
  }
  
  // 查询参数
  if (req.query && Object.keys(req.query).length > 0) {
    parts.push(JSON.stringify(req.query));
  }
  
  // POST数据
  if (req.body && Object.keys(req.body).length > 0) {
    // 排除一些敏感字段
    const filteredBody = { ...req.body };
    delete filteredBody.password;
    delete filteredBody.token;
    delete filteredBody.key;
    
    parts.push(JSON.stringify(filteredBody));
  }
  
  // User-Agent
  const userAgent = req.get('User-Agent');
  if (userAgent) {
    parts.push(userAgent);
  }
  
  return parts.join(' ');
}

/**
 * 风控检测API中间件（仅用于特定路由）
 */
const riskCheckAPI = async (req, res, next) => {
  try {
    const { content, clientInfo = {} } = req.body;

    if (!content) {
      return res.status(400).json({
        code: 400,
        message: '检测内容不能为空'
      });
    }

    // 合并客户端信息
    const fullClientInfo = {
      ...clientInfo,
      ip_address: clientInfo.ip_address || req.ip || req.connection.remoteAddress,
      user_agent: clientInfo.user_agent || req.get('User-Agent')
    };

    // 检查白名单
    const isWhitelisted = await mysqlModel.isWhitelisted(fullClientInfo);
    if (isWhitelisted) {
      return res.json({
        code: 200,
        message: '检测完成',
        data: {
          isRisk: false,
          isWhitelisted: true,
          action: 'allow',
          message: '白名单用户，允许通过'
        }
      });
    }

    // 检查黑名单
    const blacklistResult = await mysqlModel.isBlacklisted(fullClientInfo);
    if (blacklistResult.isBlacklisted) {
      return res.json({
        code: 200,
        message: '检测完成',
        data: {
          isRisk: true,
          isBlacklisted: true,
          action: 'block',
          message: '黑名单用户，禁止访问',
          banInfo: blacklistResult.record
        }
      });
    }

    // 风控内容检测
    const riskResult = await mysqlModel.checkRiskContent(content, fullClientInfo);
    
    // 记录风控数据
    if (riskResult.isRisk) {
      await mysqlModel.recordRiskData({
        username: fullClientInfo.username,
        device_id: fullClientInfo.device_id,
        ip_address: fullClientInfo.ip_address,
        uin: fullClientInfo.uin,
        risk_content: content,
        matched_keywords: riskResult.matchedKeywords,
        risk_score: riskResult.riskScore,
        risk_level: riskResult.riskLevel,
        auto_blocked: riskResult.autoBlock,
        client_info: fullClientInfo,
        request_data: JSON.stringify(req.body)
      });
    }

    res.json({
      code: 200,
      message: '检测完成',
      data: {
        isRisk: riskResult.isRisk,
        riskScore: riskResult.riskScore,
        riskLevel: riskResult.riskLevel,
        matchedKeywords: riskResult.matchedKeywords,
        action: riskResult.autoBlock ? 'block' : 'allow',
        message: riskResult.autoBlock ? '检测到高风险内容，建议拦截' : 
                 riskResult.isRisk ? '检测到风险内容，请人工审核' : '内容安全'
      }
    });

  } catch (error) {
    console.error('风控检测API错误:', error);
    res.status(500).json({
      code: 500,
      message: '检测失败',
      error: error.message
    });
  }
};

/**
 * IP频率限制中间件
 */
const ipRateLimitMiddleware = (() => {
  const requestCounts = new Map();
  const blockedIPs = new Map();

  // 清理过期记录
  setInterval(() => {
    const now = Date.now();
    const expireTime = 60 * 1000; // 1分钟
    
    for (const [ip, data] of requestCounts.entries()) {
      if (now - data.firstRequest > expireTime) {
        requestCounts.delete(ip);
      }
    }
    
    for (const [ip, expireTime] of blockedIPs.entries()) {
      if (now > expireTime) {
        blockedIPs.delete(ip);
      }
    }
  }, 30 * 1000);

  return async (req, res, next) => {
    try {
      const ip = req.ip || req.connection.remoteAddress;
      const now = Date.now();
      
      // 检查IP是否被临时封禁
      if (blockedIPs.has(ip)) {
        const expireTime = blockedIPs.get(ip);
        if (now < expireTime) {
          return res.status(429).json({
            code: 429,
            msg: '请求过于频繁',
            error: '您的IP已被临时限制',
            unblockTime: new Date(expireTime).toISOString()
          });
        } else {
          blockedIPs.delete(ip);
        }
      }

      // 获取配置
      const maxRequestsPerMinute = await mysqlModel.getSetting('max_requests_per_minute', 60);
      const blockDuration = await mysqlModel.getSetting('ip_block_duration', 5 * 60 * 1000); // 5分钟
      
      // 检查请求频率
      const requestData = requestCounts.get(ip);
      if (!requestData) {
        requestCounts.set(ip, {
          count: 1,
          firstRequest: now
        });
      } else {
        const timeWindow = 60 * 1000; // 1分钟窗口
        if (now - requestData.firstRequest > timeWindow) {
          // 重置计数器
          requestCounts.set(ip, {
            count: 1,
            firstRequest: now
          });
        } else {
          requestData.count++;
          
          if (requestData.count > maxRequestsPerMinute) {
            // 频率超限，临时封禁IP
            const expireTime = now + blockDuration;
            blockedIPs.set(ip, expireTime);
            
            console.log(`⚠️  IP频率限制: ${ip} 被临时封禁`.yellow);
            
            // 记录到风控系统
            await mysqlModel.recordRiskData({
              ip_address: ip,
              risk_content: `IP频率超限: ${requestData.count}次/分钟`,
              matched_keywords: [],
              risk_score: 60,
              risk_level: 'medium',
              auto_blocked: true,
              client_info: { ip_address: ip },
              request_data: JSON.stringify({
                url: req.url,
                method: req.method,
                requestCount: requestData.count
              })
            });

            return res.status(429).json({
              code: 429,
              msg: '请求过于频繁',
              error: `1分钟内请求超过${maxRequestsPerMinute}次，已被临时限制`,
              unblockTime: new Date(expireTime).toISOString()
            });
          }
        }
      }

      next();
    } catch (error) {
      console.error('IP频率限制中间件错误:', error);
      next(); // 出错时继续处理请求
    }
  };
})();

/**
 * 处理验证失败的风控检测
 * 专门用于请求验证失败后的风控处理
 */
const handleValidationFailure = async (req, validationErrors, riskLevel = 'medium') => {
  try {
    const clientInfo = {
      ip_address: req.ip || req.connection.remoteAddress || null,
      user_agent: req.get('User-Agent') || null,
      uin: req.body?.uin || req.query?.uin || null,
      device_id: req.body?.androidId || req.query?.androidId || null,
      username: req.body?.username || req.query?.username || null
    };

    // 检查验证失败频率
    const failureRate = checkValidationFailureRate(clientInfo);
    if (failureRate.isExceeded) {
      console.log(`🚨 验证失败频率过高: ${clientInfo.ip_address} - ${failureRate.count}次/${failureRate.window}分钟`.red);

      // 记录高频验证失败
      await mysqlModel.recordRiskData({
        username: clientInfo.username,
        device_id: clientInfo.device_id,
        ip_address: clientInfo.ip_address,
        uin: clientInfo.uin,
        risk_content: '高频验证失败',
        matched_keywords: [`${failureRate.count}次失败`, `${failureRate.window}分钟内`],
        risk_score: 90,
        risk_level: 'high',
        auto_blocked: true,
        client_info: clientInfo,
        request_data: JSON.stringify({
          url: req.url,
          method: req.method,
          validation_errors: validationErrors,
          failure_count: failureRate.count
        })
      });

      // 考虑自动加入临时黑名单
      const autoBlacklistEnabled = await mysqlModel.getSetting('auto_blacklist_validation_failures', false);
      if (autoBlacklistEnabled) {
        await mysqlModel.addToBlacklist({
          username: clientInfo.username,
          device_id: clientInfo.device_id,
          ip_address: clientInfo.ip_address,
          uin: clientInfo.uin,
          risk_content: `高频验证失败: ${failureRate.count}次`,
          ban_reason: '频繁发送无效请求',
          ban_type: 'temporary',
          ban_duration: 1, // 1小时
          creator_id: null
        });
        console.log(`🚫 自动临时封禁: ${clientInfo.ip_address} - 1小时`.red);
      }

      return {
        shouldBlock: true,
        reason: 'validation_failure_rate_exceeded',
        details: failureRate
      };
    }

    // 记录普通验证失败
    await mysqlModel.recordRiskData({
      username: clientInfo.username,
      device_id: clientInfo.device_id,
      ip_address: clientInfo.ip_address,
      uin: clientInfo.uin,
      risk_content: '请求验证失败',
      matched_keywords: validationErrors,
      risk_score: riskLevel === 'high' ? 70 : riskLevel === 'medium' ? 40 : 20,
      risk_level: riskLevel,
      auto_blocked: false,
      client_info: clientInfo,
      request_data: JSON.stringify({
        url: req.url,
        method: req.method,
        validation_errors: validationErrors
      })
    });

    return {
      shouldBlock: false,
      reason: 'validation_failure_recorded',
      details: failureRate
    };

  } catch (error) {
    console.error('处理验证失败风控时出错:', error);
    return {
      shouldBlock: false,
      reason: 'error',
      error: error.message
    };
  }
};

/**
 * 处理错误请求的风控检测
 */
const handleErrorRequest = async (req, errorInfo) => {
  try {
    const clientInfo = {
      ip_address: req.ip || req.connection.remoteAddress || null,
      user_agent: req.get('User-Agent') || null,
      uin: req.body?.uin || req.query?.uin || null,
      device_id: req.body?.androidId || req.query?.androidId || null,
      username: req.body?.username || req.query?.username || null
    };

    // 检查错误请求频率
    const errorRate = checkErrorRequestRate(clientInfo);
    if (errorRate.isExceeded) {
      console.log(`🚨 错误请求频率过高: ${clientInfo.ip_address} - ${errorRate.count}次/${errorRate.window}分钟`.red);

      await mysqlModel.recordRiskData({
        username: clientInfo.username,
        device_id: clientInfo.device_id,
        ip_address: clientInfo.ip_address,
        uin: clientInfo.uin,
        risk_content: '高频错误请求',
        matched_keywords: [`${errorRate.count}次错误`, `${errorRate.window}分钟内`],
        risk_score: 80,
        risk_level: 'high',
        auto_blocked: true,
        client_info: clientInfo,
        request_data: JSON.stringify({
          url: req.url,
          method: req.method,
          error_info: errorInfo,
          error_count: errorRate.count
        })
      });

      return {
        shouldBlock: true,
        reason: 'error_request_rate_exceeded',
        details: errorRate
      };
    }

    return {
      shouldBlock: false,
      reason: 'error_request_recorded',
      details: errorRate
    };

  } catch (error) {
    console.error('处理错误请求风控时出错:', error);
    return {
      shouldBlock: false,
      reason: 'error',
      error: error.message
    };
  }
};

module.exports = {
  riskControlMiddleware,
  riskCheckAPI,
  ipRateLimitMiddleware,
  handleValidationFailure,
  handleErrorRequest,
  checkValidationFailureRate,
  checkErrorRequestRate,
  detectAbnormalRequestPatterns
};
