const express = require('express');
const router = express.Router();
const mysqlModel = require('../models/mysql-real-model');
const { nodeSelector } = require('../middleware/nodeSelector');

// 获取所有节点配置
router.get('/nodes', async (req, res) => {
  try {
    const configs = await mysqlModel.getAllApiConfigs();
    res.success(configs, '获取成功');
  } catch (error) {
    res.error('获取失败', 500, { error: error.message });
  }
});

// 保存/更新节点配置（简化版，只需要类型和地址）
router.put('/nodes/:clientType', async (req, res) => {
  try {
    const { clientType } = req.params;
    const { base_url } = req.body;

    if (!base_url) {
      return res.error('节点地址不能为空', 400);
    }

    // 检查是否已存在配置
    const existingConfig = await mysqlModel.getApiConfig(clientType);

    if (existingConfig) {
      // 更新现有配置，保持其他字段不变
      await mysqlModel.setApiConfig(
        clientType,
        base_url,
        existingConfig.timeout || 10000,
        existingConfig.retry_count || 3,
        existingConfig.description || '',
        existingConfig.auth_key || '',
        existingConfig.request_limit || 0
      );
    } else {
      // 创建新配置，使用默认值
      await mysqlModel.setApiConfig(
        clientType,
        base_url,
        10000, // 默认超时
        3,     // 默认重试次数
        '',    // 默认描述
        '',    // 默认无auth_key
        0      // 默认无限制
      );
    }

    res.success(null, '保存成功');
  } catch (error) {
    res.error('保存失败', 500, { error: error.message });
  }
});

// 创建新节点
router.post('/nodes', async (req, res) => {
  try {
    const { client_type, base_url, node_name } = req.body;

    if (!client_type || !base_url) {
      return res.error('节点类型和地址不能为空', 400);
    }

    // 验证节点类型是否为允许的类型
    const allowedTypes = ['QQ', 'TIM', 'QQlite', '企点QQ'];
    if (!allowedTypes.includes(client_type)) {
      return res.error('无效的节点类型，只允许：' + allowedTypes.join('、'), 400);
    }

    // 生成唯一的节点标识符
    // 如果提供了节点名称，使用 类型_名称 格式
    // 如果没有提供，使用 类型_时间戳 格式
    let nodeIdentifier;
    if (node_name && node_name.trim()) {
      nodeIdentifier = `${client_type}_${node_name.trim()}`;
    } else {
      const timestamp = Date.now();
      nodeIdentifier = `${client_type}_${timestamp}`;
    }

    // 检查节点标识符是否已存在
    const existingConfig = await mysqlModel.getApiConfig(nodeIdentifier);
    if (existingConfig) {
      return res.error(`节点标识 ${nodeIdentifier} 已存在，请使用不同的节点名称`, 400);
    }

    // 创建新节点配置
    await mysqlModel.setApiConfig(
      nodeIdentifier,
      base_url,
      10000, // 默认超时
      3,     // 默认重试次数
      `${client_type}签名节点${node_name ? ` - ${node_name}` : ''}`, // 默认描述
      '',    // 默认无auth_key
      0      // 默认无限制
    );

    res.success({ nodeIdentifier }, `${client_type} 节点创建成功`);
  } catch (error) {
    res.error('创建失败', 500, { error: error.message });
  }
});

// 删除节点
router.delete('/nodes/:clientType', async (req, res) => {
  try {
    const { clientType } = req.params;

    // 检查节点是否存在
    const config = await mysqlModel.getApiConfig(clientType);
    if (!config) {
      return res.error('节点不存在', 404);
    }

    // 检查节点是否有关联的token（可选检查，可以通过查询参数强制删除）
    const forceDelete = req.query.force === 'true';
    if (!forceDelete && config.auth_key && config.auth_key.length === 128) {
      return res.error('该节点已关联Token，请先在Token管理中删除相关Token，或使用强制删除', 400);
    }

    const success = await mysqlModel.deleteApiConfig(clientType);
    if (success) {
      res.success(null, '删除成功');
    } else {
      res.error('删除失败，节点可能不存在', 404);
    }
  } catch (error) {
    console.error('删除节点失败:', error);
    res.error('删除失败', 500, { error: error.message });
  }
});

// 获取节点状态（简化版）
router.get('/nodes/status', async (req, res) => {
  try {
    const configs = await mysqlModel.getAllApiConfigs();
    const status = configs.map(config => ({
      client_type: config.client_type,
      base_url: config.base_url,
      hasToken: !!(config.auth_key && config.auth_key.length === 128),
      status: 'unknown' // 简化状态检查
    }));
    res.success(status, '获取成功');
  } catch (error) {
    res.error('获取失败', 500, { error: error.message });
  }
});

// 获取所有token-节点映射
router.get('/token-mappings', async (req, res) => {
  try {
    const mappings = await nodeSelector.getAllTokenMappings();
    res.success(mappings, '获取成功');
  } catch (error) {
    res.error('获取失败', 500, { error: error.message });
  }
});

// 刷新节点选择器缓存
router.post('/refresh-cache', async (req, res) => {
  try {
    await nodeSelector.refreshCache();
    const stats = nodeSelector.getCacheStats();
    res.success(stats, '缓存刷新成功');
  } catch (error) {
    res.error('刷新失败', 500, { error: error.message });
  }
});

// 获取节点选择器缓存统计
router.get('/cache-stats', (req, res) => {
  try {
    const stats = nodeSelector.getCacheStats();
    res.success(stats, '获取成功');
  } catch (error) {
    res.error('获取失败', 500, { error: error.message });
  }
});

// 服务状态监控（简单探活）
router.get('/nodes/status', async (req, res) => {
  try {
    const configs = await mysqlModel.getAllApiConfigs();
    const axios = require('axios');
    const results = await Promise.all(configs.map(async node => {
      let status = 'unknown';
      try {
        const resp = await axios.get(node.base_url + '/health', { timeout: 2000 });
        status = resp.status === 200 ? 'online' : 'offline';
      } catch {
        status = 'offline';
      }
      return { client_type: node.client_type, status };
    }));
    res.success(results, '获取成功');
  } catch (error) {
    res.error('获取失败', 500, { error: error.message });
  }
});

module.exports = router; 