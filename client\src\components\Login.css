/* 登录页面二次元萌系风格 - 与控制台保持一致 */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

/* 背景动画 */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* 飘浮的爱心 */
.floating-hearts {
  position: absolute;
  width: 100%;
  height: 100%;
}

.heart {
  position: absolute;
  color: rgba(255, 182, 193, 0.6) !important;
  font-size: 18px;
  animation: float 6s ease-in-out infinite;
  opacity: 0.7;
}

.heart-0 { left: 10%; animation-delay: 0s; }
.heart-1 { left: 20%; animation-delay: 1s; }
.heart-2 { left: 30%; animation-delay: 2s; }
.heart-3 { left: 40%; animation-delay: 3s; }
.heart-4 { left: 50%; animation-delay: 4s; }
.heart-5 { left: 60%; animation-delay: 5s; }
.heart-6 { left: 70%; animation-delay: 0.5s; }
.heart-7 { left: 80%; animation-delay: 1.5s; }
.heart-8 { left: 90%; animation-delay: 2.5s; }
.heart-9 { left: 15%; animation-delay: 3.5s; }
.heart-10 { left: 25%; animation-delay: 4.5s; }
.heart-11 { left: 35%; animation-delay: 5.5s; }

/* 樱花花瓣 */
.sakura-petals {
  position: absolute;
  width: 100%;
  height: 100%;
}

.petal {
  position: absolute;
  font-size: 14px;
  animation: fall 8s linear infinite;
  opacity: 0.6;
  color: #ff9a9e;
}

.petal-0 { left: 5%; animation-delay: 0s; }
.petal-1 { left: 15%; animation-delay: 1s; }
.petal-2 { left: 25%; animation-delay: 2s; }
.petal-3 { left: 35%; animation-delay: 3s; }
.petal-4 { left: 45%; animation-delay: 4s; }
.petal-5 { left: 55%; animation-delay: 5s; }
.petal-6 { left: 65%; animation-delay: 6s; }
.petal-7 { left: 75%; animation-delay: 7s; }

/* 登录内容 */
.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 420px;
  padding: 20px;
}

/* 登录卡片 - 与控制台样式保持一致 */
.login-card {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 20px !important;
  box-shadow: 0 8px 32px rgba(255, 107, 157, 0.3) !important;
  border: 2px solid rgba(255, 182, 193, 0.3) !important;
  padding: 40px 30px !important;
  position: relative !important;
  overflow: hidden !important;
}

.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 50%, #a8edea 100%);
  z-index: 1;
}
  box-shadow: 0 8px 32px rgba(255, 182, 185, 0.12) !important;
  border-radius: 20px !important;
  overflow: hidden;
  animation: cardFloat 3s ease-in-out infinite;
}

.login-header {
  text-align: center;
  padding: 20px 0;
  background: linear-gradient(135deg, #ffeef8 0%, #f0e6ff 100%);
  margin: -24px -24px 24px -24px;
}

.avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 20px rgba(255, 154, 158, 0.3);
  animation: bounce 2s ease-in-out infinite;
}

.avatar-emoji {
  font-size: 40px;
}

.welcome-text h2 {
  margin: 0;
  color: #6b46c1;
  font-weight: 600;
  font-size: 24px;
}

.welcome-text p {
  margin: 5px 0 0 0;
  color: #8b5cf6;
  font-size: 14px;
}

/* 可爱的输入框 */
.cute-input {
  border-radius: 15px !important;
  border: 2px solid #ffb6b9 !important;
  background: #fffafc !important;
  color: #ffb6b9 !important;
  transition: all 0.3s ease !important;
}

.cute-input:hover {
  border-color: #f687b3 !important;
  box-shadow: 0 0 15px rgba(246, 135, 179, 0.3) !important;
}

.cute-input:focus {
  border-color: #ffb6b9 !important;
  box-shadow: 0 0 8px #ffe4e1 !important;
}

.input-icon {
  color: #ed64a6 !important;
}

/* 登录按钮 */
.login-button {
  border-radius: 15px !important;
  height: 45px !important;
  background: linear-gradient(135deg, #ffe4e1 0%, #ffb6b9 100%) !important;
  color: #fff !important;
  border: none !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 16px rgba(255, 182, 185, 0.18) !important;
}

.login-button:hover, .login-button:focus {
  background: linear-gradient(135deg, #ffb6b9 0%, #ffe4e1 100%) !important;
  color: #fff !important;
  box-shadow: 0 6px 24px rgba(255, 182, 185, 0.22) !important;
}

/* 禁用状态的按钮 */
.login-button.disabled,
.login-button:disabled {
  background: linear-gradient(135deg, #e0e6ed 0%, #c3cfe2 100%) !important;
  color: #999 !important;
  cursor: not-allowed !important;
  box-shadow: none !important;
  transform: none !important;
}

.login-button.disabled:hover,
.login-button:disabled:hover {
  transform: none !important;
  box-shadow: none !important;
}

.login-footer {
  text-align: center;
  margin-top: 20px;
  color: #8b5cf6;
  font-size: 14px;
}

/* 动画效果 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg);
    opacity: 0;
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

@keyframes cardFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    max-width: 350px;
    padding: 15px;
  }
  
  .login-card {
    margin: 20px;
  }
  
  .avatar {
    width: 60px;
    height: 60px;
  }
  
  .avatar-emoji {
    font-size: 30px;
  }
  
  .welcome-text h2 {
    font-size: 20px;
  }
}

/* 验证码模态框样式 */
.captcha-modal .ant-modal-content {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.captcha-modal .ant-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  padding: 20px 24px 16px;
  border-radius: 20px 20px 0 0;
}

.captcha-modal .ant-modal-title {
  color: white !important;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.captcha-modal .ant-modal-close {
  top: 16px;
  right: 16px;
}

.captcha-modal .ant-modal-close .ant-modal-close-x {
  color: white;
  font-size: 18px;
  width: 32px;
  height: 32px;
  line-height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.captcha-modal .ant-modal-close .ant-modal-close-x:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.captcha-modal .ant-modal-body {
  padding: 24px;
}

/* 模态框进入动画 */
.captcha-modal.ant-modal {
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 触摸设备优化 */
@media (pointer: coarse) {
  .login-button {
    height: 48px;
    font-size: 16px;
  }
  
  .cute-input {
    height: 44px;
    font-size: 16px;
  }
}

/* 强制跳转帮助 */
.redirect-helper {
  margin-top: 20px;
  padding: 10px;
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  border-radius: 4px;
  text-align: center;
}

.redirect-message {
  color: #d48806;
  margin-bottom: 8px;
  font-size: 14px;
}

.force-redirect-button {
  margin-top: 8px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

.modern-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 18px;
}

.modern-logo {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
}

.modern-logo-circle {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, #ffe4e1 0%, #ffb6b9 100%) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 12px rgba(255, 182, 185, 0.18) !important;
}

.welcome-text {
  text-align: center;
}

.peach-bg {
  background: linear-gradient(135deg, #ffe4e1 0%, #ffb6b9 100%) !important;
}
.peach-card {
  background: rgba(255, 245, 247, 0.98) !important;
  border: 2px solid #ffe4e1 !important;
  box-shadow: 0 8px 32px rgba(255, 182, 185, 0.12) !important;
}
.peach-logo-circle {
  background: linear-gradient(135deg, #ffe4e1 0%, #ffb6b9 100%) !important;
  box-shadow: 0 2px 12px rgba(255, 182, 185, 0.18) !important;
}
.peach-input {
  border: 2px solid #ffb6b9 !important;
  background: #fffafc !important;
  color: #ffb6b9 !important;
}
.peach-input:focus {
  border-color: #ffb6b9 !important;
  box-shadow: 0 0 8px #ffe4e1 !important;
}
.peach-btn {
  background: linear-gradient(135deg, #ffe4e1 0%, #ffb6b9 100%) !important;
  color: #fff !important;
  border: none !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 16px rgba(255, 182, 185, 0.18) !important;
}
.peach-btn:hover, .peach-btn:focus {
  background: linear-gradient(135deg, #ffb6b9 0%, #ffe4e1 100%) !important;
  color: #fff !important;
  box-shadow: 0 6px 24px rgba(255, 182, 185, 0.22) !important;
}
.login-card {
  border-radius: 20px !important;
}

.blue-bg {
  background: linear-gradient(135deg, #e3f0ff 0%, #6ca0dc 100%) !important;
}
.blue-card {
  background: rgba(243, 249, 255, 0.98) !important;
  border: 2px solid #e3f0ff !important;
  box-shadow: 0 8px 32px rgba(108,160,220,0.10) !important;
}
.blue-logo-circle {
  background: linear-gradient(135deg, #e3f0ff 0%, #6ca0dc 100%) !important;
  box-shadow: 0 2px 12px rgba(108,160,220,0.12) !important;
}
.blue-input {
  border: 2px solid #6ca0dc !important;
  background: #fafdff !important;
  color: #6ca0dc !important;
}
.blue-input:focus {
  border-color: #6ca0dc !important;
  box-shadow: 0 0 8px #e3f0ff !important;
}
.blue-btn {
  background: linear-gradient(135deg, #e3f0ff 0%, #6ca0dc 100%) !important;
  color: #fff !important;
  border: none !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 16px rgba(108,160,220,0.12) !important;
}
.blue-btn:hover, .blue-btn:focus {
  background: linear-gradient(135deg, #6ca0dc 0%, #e3f0ff 100%) !important;
  color: #fff !important;
  box-shadow: 0 6px 24px rgba(108,160,220,0.18) !important;
}
.blue-2fa-modal .ant-modal-content {
  background: linear-gradient(135deg, #fafdff 0%, #e3f0ff 100%) !important;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(108,160,220,0.10) !important;
}
.blue-2fa-modal .ant-modal-header {
  background: linear-gradient(135deg, #e3f0ff 0%, #6ca0dc 100%) !important;
  border-bottom: none;
  border-radius: 20px 20px 0 0;
}
.blue-2fa-modal .ant-modal-title {
  color: #6ca0dc !important;
  font-weight: 700;
}
.heart {
  color: #6ca0dc !important;
  opacity: 0.5;
}
.petal {
  opacity: 0.7;
}
