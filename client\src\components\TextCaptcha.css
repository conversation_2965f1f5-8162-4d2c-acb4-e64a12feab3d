/* 文本验证码组件样式 */
.text-captcha-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  border: 1px solid #e1e5e9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.captcha-display {
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.captcha-canvas {
  border: 2px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.captcha-canvas:hover {
  border-color: #40a9ff;
  box-shadow: 0 0 8px rgba(64, 169, 255, 0.3);
  transform: scale(1.02);
}

.refresh-button {
  font-size: 14px;
  color: #666;
  transition: all 0.3s ease;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.refresh-button:hover {
  color: #1890ff;
  background-color: #f0f7ff;
  transform: rotate(180deg);
}

.captcha-input-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  max-width: 220px;
}

.captcha-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  font-family: 'Courier New', monospace;
  text-align: center;
  letter-spacing: 2px;
  transition: all 0.3s ease;
  background: white;
}

.captcha-input:focus {
  outline: none;
  border-color: #40a9ff;
  box-shadow: 0 0 6px rgba(64, 169, 255, 0.3);
}

.captcha-input::placeholder {
  color: #bfbfbf;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  letter-spacing: normal;
}

.verify-button {
  border-radius: 6px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
  transition: all 0.3s ease;
}

.verify-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

.captcha-tips {
  font-size: 12px;
  color: #8c8c8c;
  text-align: center;
  margin-top: 4px;
}

.captcha-tips span {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

/* 动画效果 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.captcha-input.error {
  border-color: #ff4d4f;
  animation: shake 0.3s ease-in-out;
}

.captcha-input.success {
  border-color: #52c41a;
  background-color: #f6ffed;
}

/* 加载状态 */
.verify-button.ant-btn-loading {
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .text-captcha-container {
    padding: 12px;
    gap: 10px;
  }
  
  .captcha-input-container {
    max-width: 200px;
  }
  
  .captcha-canvas {
    width: 100px;
    height: 35px;
  }
  
  .captcha-input {
    font-size: 13px;
    padding: 6px 10px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .text-captcha-container {
    background: linear-gradient(135deg, #2f2f2f 0%, #1a1a1a 100%);
    border-color: #434343;
  }
  
  .captcha-canvas {
    border-color: #434343;
    background: #1f1f1f;
  }
  
  .captcha-input {
    background: #1f1f1f;
    border-color: #434343;
    color: #ffffff;
  }
  
  .captcha-tips span {
    background: rgba(255, 255, 255, 0.1);
    color: #bfbfbf;
  }
}
