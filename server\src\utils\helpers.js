const crypto = require('crypto');

// MD5 哈希函数
function md5sum(msg) {
    return crypto.createHash('md5').update(msg).digest('hex');
}

// 根据Type值返回对应的端口号
function determinePort(typeValue) {
    const portMap = {
        'QQ': 9511,
        'qidian': 9513,
        'qqlite': 9512,
        'tim': 9514
    };
    return portMap[typeValue] || null;
}

// 提取UIN从请求数据
function extractUin(data, endpoint) {
    try {
        if (endpoint === '/sign') {
            // 从sign请求中提取UIN
            const match = data.match(/uin=(\d+)/);
            return match ? match[1] : null;
        } else if (endpoint === '/Get0553') {
            // 从Get0553请求中提取UIN
            const match = data.match(/uin=(\d+)/);
            return match ? match[1] : null;
        }
        return null;
    } catch (error) {
        return null;
    }
}

// 提取CMD从请求数据
function extractCmd(data, endpoint) {
    try {
        if (endpoint === '/sign') {
            // 从sign请求中提取CMD
            const match = data.match(/cmd=([^&]+)/);
            return match ? match[1] : null;
        } else if (endpoint === '/Get0553') {
            // 从Get0553请求中提取CMD
            const match = data.match(/cmd=([^&]+)/);
            return match ? match[1] : null;
        } else if (endpoint === '/energy') {
            // 从energy请求中提取CMD
            const match = data.match(/data=([^_]+)/);
            return match ? match[1] : null;
        }
        return null;
    } catch (error) {
        return null;
    }
}

// 获取当前日期字符串 (YYYY-MM-DD)
function getCurrentDate() {
    return new Date().toISOString().split('T')[0];
}

// 格式化时间戳
function formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleString('zh-CN');
}

// 获取UTC+8时间戳（北京时间）
function getBeijingTimestamp() {
    const now = new Date();
    // 直接添加8小时到当前时间
    const beijing = new Date(now.getTime() + (8 * 60 * 60 * 1000));

    // 格式化为 YYYY-MM-DD HH:mm:ss 格式
    const year = beijing.getUTCFullYear();
    const month = String(beijing.getUTCMonth() + 1).padStart(2, '0');
    const day = String(beijing.getUTCDate()).padStart(2, '0');
    const hours = String(beijing.getUTCHours()).padStart(2, '0');
    const minutes = String(beijing.getUTCMinutes()).padStart(2, '0');
    const seconds = String(beijing.getUTCSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds} (UTC+8)`;
}

module.exports = {
    md5sum,
    determinePort,
    extractUin,
    extractCmd,
    getCurrentDate,
    formatTimestamp,
    getBeijingTimestamp
};
