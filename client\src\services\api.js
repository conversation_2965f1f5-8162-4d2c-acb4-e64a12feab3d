import axios from 'axios';

let configCache = null;

// 异步加载后端 /api/config
async function loadAppConfig() {
  if (configCache) return configCache;

  // 首先获取基础API URL
  const baseApiUrl = getBaseApiUrl();

  try {
    const resp = await fetch(`${baseApiUrl}/config`);
    if (resp.ok) {
      configCache = await resp.json();
      window.__APP_CONFIG__ = configCache;
      return configCache;
    }
  } catch (error) {
    console.warn('无法加载后端配置:', error.message);
  }
  return {};
}

// 获取基础API URL（不依赖后端配置）
function getBaseApiUrl() {
  // 优先环境变量
  if (process.env.REACT_APP_API_URL) {
    console.log('🌐 使用环境变量API URL:', process.env.REACT_APP_API_URL);
    return process.env.REACT_APP_API_URL;
  }
  // 其次localStorage
  let apiUrl = localStorage.getItem('api_url');
  if (apiUrl) {
    console.log('🌐 使用localStorage API URL:', apiUrl);
    return apiUrl;
  }
  // 最后默认
  console.log('🌐 使用默认API URL: http://localhost:12041/api');
  return 'http://localhost:12041/api';
}

// 获取API URL
export async function getApiUrl() {
  // 首先尝试从后端配置获取
  const config = await loadAppConfig();
  if (config.API_BASE_URL) return config.API_BASE_URL;

  // 如果后端配置不可用，使用基础配置
  return getBaseApiUrl();
}

// 创建axios实例（异步baseURL）
const api = axios.create({ timeout: 15000, retry: 3, retryDelay: 1000 });

// 动态设置baseURL
export async function initApiConfig() {
  const url = await getApiUrl();
  api.defaults.baseURL = url;
  window.__API_BASE_URL__ = url;
  console.log('🌐 API baseURL:', url);
}

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    let token = localStorage.getItem('auth_token');
    // 只在token为非空字符串且格式为JWT时加Authorization头
    if (token && typeof token === 'string' && token.split('.').length === 3) {
      config.headers.Authorization = `Bearer ${token}`;
    } else {
      // 防止带上无效token
      delete config.headers.Authorization;
    }
    config.retryCount = config.retryCount || 0;
    return config;
  },
  (error) => Promise.reject(error)
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 统一2FA弹窗处理
    try {
      const data = response.data;
      if ((data && (data.code === 1001 || data.code === '1001')) && !window.__SHOW_2FA_MODAL__) {
        // 触发全局事件，通知Login组件弹出2FA窗口
        const event = new CustomEvent('show-2fa-modal', { detail: data });
        window.dispatchEvent(event);
        window.__SHOW_2FA_MODAL__ = true; // 防止重复弹窗
      }
    } catch (e) { /* 忽略 */ }
    return response;
  },
  async (error) => {
    const config = error.config;
    if (config && config.retryCount < (config.retry || 3)) {
      config.retryCount += 1;
      if (error.code === 'ECONNREFUSED' || error.code === 'NETWORK_ERROR' || (error.response && [500,502,503,504].includes(error.response.status))) {
        await new Promise(resolve => setTimeout(resolve, config.retryDelay || 1000));
        return api.request(config);
      }
    }
    if (error.response && error.response.status === 401 && !config.url.includes('/auth/login')) {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_info');
      setTimeout(() => { if (!window.location.pathname.includes('/login')) window.location.reload(); }, 1000);
    }
    return Promise.reject(error);
  }
);

// 签名日志相关 API
export const signLogsAPI = {
  // 获取签名日志列表
  getSignLogs: (params) => api.get('/logs/sign', { params }),
  
  // 获取签名日志详情
  getSignLogDetail: (id) => api.get(`/logs/sign/${id}`),
};

// 未授权访问日志相关 API
export const unauthorizedLogsAPI = {
  // 获取未授权访问日志列表
  getUnauthorizedLogs: (params) => api.get('/logs/unauthorized', { params }),
};

// 统计相关 API
export const statsAPI = {
  // 获取总体统计
  getOverview: () => api.get('/stats/overview'),
  
  // 获取每日统计
  getDailyStats: (params) => api.get('/stats/daily', { params }),
  
  // 获取类型统计
  getTypeStats: () => api.get('/stats/types'),
  
  // 获取端点统计
  getEndpointStats: () => api.get('/stats/endpoints'),
  
  // 获取IP统计
  getIPStats: (params) => api.get('/stats/ips', { params }),
  
  // 获取UIN统计
  getUINStats: (params) => api.get('/stats/uins', { params }),
  
  // 获取小时统计
  getHourlyStats: () => api.get('/stats/hourly'),
};

// 认证相关 API
export const authAPI = {
  // 登录
  login: (credentials) => api.post('/auth/login', credentials),

  // 验证 token
  verify: () => api.get('/auth/verify'),

  // 刷新 token
  refreshToken: () => api.post('/auth/refresh'),

  // 登出
  logout: () => api.post('/auth/logout'),

  // 获取用户列表
  getUsers: () => api.get('/auth/users'),
};

// 设置相关 API
export const settingsAPI = {
  // 获取系统设置
  getSettings: () => api.get('/settings'),

  // 保存系统设置
  saveSettings: (settings) => api.post('/settings', settings),

  // 获取风控规则
  getRiskRules: () => api.get('/settings/risk-rules'),

  // 更新风控规则
  updateRiskRules: (rules) => api.put('/settings/risk-rules', rules),

  // 获取黑名单
  getBlacklist: (params) => api.get('/settings/blacklist', { params }),

  // 添加到黑名单
  addToBlacklist: (data) => api.post('/settings/blacklist', data),

  // 从黑名单移除
  removeFromBlacklist: (id) => api.delete(`/settings/blacklist/${id}`),

  // 获取管理员凭据
  getAdminCredentials: () => api.get('/settings/admin-credentials'),

  // 更新管理员凭据
  updateAdminCredentials: (credentials) => api.post('/settings/admin-credentials', credentials),
};

// 风控相关 API
export const riskAPI = {
  // 获取风险报告
  getRiskReports: (params) => api.get('/risk/reports', { params }),

  // 审核风险报告
  reviewRiskReport: (id, data) => api.put(`/risk/reports/${id}/review`, data),

  // 上报风险
  reportRisk: (data) => api.post('/risk/report', data),
};

// 健康检查
export const healthAPI = {
  check: async () => {
    try {
      // 从配置获取后端地址
      const apiUrl = await getApiUrl();
      const healthUrl = apiUrl.replace('/api', '/health');
      return axios.get(healthUrl);
    } catch (error) {
      // 如果获取配置失败，使用默认配置
      const serverPort = localStorage.getItem('server_port') || '12041';
      return axios.get(`http://localhost:${serverPort}/health`);
    }
  },
};

// QIMEI相关 API
export const qimeiAPI = {
  // 获取参数模板
  getTemplate: () => api.get('/qimei/template'),
  
  // 生成单个QIMEI
  generate: (params) => api.post('/qimei/generate', params),
  
  // 批量生成QIMEI
  batch: (data) => api.post('/qimei/batch', data),
};

// 配置管理相关 API
export const configAPI = {
  // 获取公开配置
  getPublicConfig: () => api.get('/config/public'),

  // 获取完整配置
  getConfig: () => api.get('/config'),

  // 更新服务器配置
  updateServerConfig: (config) => api.put('/config/server', config),

  // 更新客户端配置
  updateClientConfig: (config) => api.put('/config/client', config),

  // 获取API Base URLs
  getBaseUrls: () => api.get('/config/api/base-urls'),

  // 更新API Base URLs
  updateBaseUrls: (baseUrls) => api.put('/config/api/base-urls', { baseUrls }),

  // 更新单个Base URL
  updateSingleBaseUrl: (type, url) => api.put(`/config/api/base-urls/${type}`, { url }),

  // 重置配置
  resetConfig: () => api.post('/config/reset'),
};

// Token管理相关 API
export const tokenAPI = {
  // 获取token列表
  getTokens: (params) => api.get('/token-management/tokens', { params }),

  // 创建新token
  createToken: (data) => api.post('/token-management/tokens', data),

  // 更新token配置
  updateToken: (nodeType, data) => api.put(`/token-management/tokens/${nodeType}`, data),

  // 删除token
  deleteToken: (nodeType) => api.delete(`/token-management/tokens/${nodeType}`),

  // 重新生成token
  regenerateToken: (nodeType) => api.post(`/token-management/tokens/${nodeType}/regenerate`),

  // 获取可用节点
  getAvailableNodes: () => api.get('/token-management/available-nodes'),

  // 测试token连接
  testToken: (nodeType) => api.post(`/token-management/tokens/${nodeType}/test`),
};

export default api;
