const express = require('express');
const router = express.Router();
const configManager = require('../utils/config');
const { authenticate } = require('../middleware/auth');
const config = require('../../database/config.json');

// 获取公开配置（不需要认证，供前端初始化使用）
router.get('/public', async (req, res) => {
  try {
    const publicConfig = await configManager.getPublicConfig();
    res.json({
      success: true,
      data: publicConfig
    });
  } catch (error) {
    console.error('获取公开配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取配置失败'
    });
  }
});

// 获取完整配置（需要认证）
router.get('/', authenticate, async (req, res) => {
  try {
    const config = {
      server: await configManager.getServerConfig(),
      client: await configManager.getClientConfig(),
      api: await configManager.getApiConfig(),
      database: await configManager.getDatabaseConfig()
    };
    
    res.json({
      success: true,
      data: config
    });
  } catch (error) {
    console.error('获取配置失败:', error);
    res.status(500).json({
      success: false,
      message: '获取配置失败'
    });
  }
});

// 更新服务器配置
router.put('/server', authenticate, async (req, res) => {
  try {
    const { port, host } = req.body;
    
    if (port) {
      await configManager.set('server.port', parseInt(port));
    }
    if (host) {
      await configManager.set('server.host', host);
    }
    
    res.json({
      success: true,
      message: '服务器配置更新成功',
      data: await configManager.getServerConfig()
    });
  } catch (error) {
    console.error('更新服务器配置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新配置失败'
    });
  }
});

// 更新客户端配置
router.put('/client', authenticate, async (req, res) => {
  try {
    const { port, apiUrl } = req.body;
    
    if (port) {
      await configManager.set('client.port', parseInt(port));
    }
    if (apiUrl) {
      await configManager.set('client.apiUrl', apiUrl);
    }
    
    res.json({
      success: true,
      message: '客户端配置更新成功',
      data: await configManager.getClientConfig()
    });
  } catch (error) {
    console.error('更新客户端配置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新配置失败'
    });
  }
});

// 获取API Base URLs
router.get('/api/base-urls', authenticate, async (req, res) => {
  try {
    const baseUrls = await configManager.getAllBaseUrls();
    res.json({
      success: true,
      data: baseUrls
    });
  } catch (error) {
    console.error('获取Base URLs失败:', error);
    res.status(500).json({
      success: false,
      message: '获取Base URLs失败'
    });
  }
});

// 更新API Base URLs
router.put('/api/base-urls', authenticate, async (req, res) => {
  try {
    const { baseUrls } = req.body;
    
    await configManager.updateBaseUrls(baseUrls);
    
    res.json({
      success: true,
      message: 'Base URLs更新成功',
      data: await configManager.getAllBaseUrls()
    });
  } catch (error) {
    console.error('更新Base URLs失败:', error);
    res.status(500).json({
      success: false,
      message: '更新Base URLs失败'
    });
  }
});

// 更新单个Base URL
router.put('/api/base-urls/:type', authenticate, async (req, res) => {
  try {
    const { type } = req.params;
    const { url } = req.body;
    
    if (!url) {
      return res.status(400).json({
        success: false,
        message: 'URL不能为空'
      });
    } 
    await configManager.setBaseUrl(type, url);
    
    res.json({
      success: true,
      message: `${type} Base URL更新成功`,
      data: { [type]: url }
    });
  } catch (error) {
    console.error('更新Base URL失败:', error);
    res.status(500).json({
      success: false,
      message: '更新Base URL失败'
    });
  }
});

// 更新API配置
router.put('/api', authenticate, async (req, res) => {
  try {
    const { defaultAuthKey, timeout } = req.body;
    
    if (defaultAuthKey) {
      await configManager.set('api.defaultAuthKey', defaultAuthKey);
    }
    if (timeout) {
      await configManager.set('api.timeout', parseInt(timeout));
    }
    
    res.json({
      success: true,
      message: 'API配置更新成功',
      data: await configManager.getApiConfig()
    });
  } catch (error) {
    console.error('更新API配置失败:', error);
    res.status(500).json({
      success: false,
      message: '更新API配置失败'
    });
  }
});

// 重置配置为默认值
router.post('/reset', authenticate, async (req, res) => {
  try {
    await configManager.reset();
    res.json({
      success: true,
      message: '配置已重置为默认值'
    });
  } catch (error) {
    console.error('重置配置失败:', error);
    res.status(500).json({
      success: false,
      message: '重置配置失败'
    });
  }
});

// 新增：前端获取API配置
router.get('/config', (req, res) => {
  res.json({
    API_BASE_URL: config.client.apiUrl
  });
});

module.exports = router;
