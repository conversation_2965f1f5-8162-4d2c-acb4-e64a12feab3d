import React, { useState, useEffect } from 'react';
import { 
  Row, 
  Col, 
  Card, 
  Table, 
  Select, 
  Spin, 
  Tabs,
  Statistic
} from 'antd';
import {
  Line<PERSON>hart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts';
import { statsAPI } from '../services/api';
import NotificationHelper from '../components/NotificationHelper';
import { normalizeResponse, isResponseSuccess, getResponseData } from '../utils/responseHelper';

const { Option } = Select;
const { TabPane } = Tabs;

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const Statistics = () => {
  const [loading, setLoading] = useState(false);
  const [dailyStats, setDailyStats] = useState([]);
  const [typeStats, setTypeStats] = useState([]);
  const [endpointStats, setEndpointStats] = useState([]);
  const [ipStats, setIpStats] = useState([]);
  const [uinStats, setUinStats] = useState([]);
  const [hourlyStats, setHourlyStats] = useState([]);
  const [dayRange, setDayRange] = useState(7);

  useEffect(() => {
    fetchAllStats();
  }, [dayRange]);

  const fetchAllStats = async () => {
    setLoading(true);
    try {
      const [
        dailyResponse,
        typeResponse,
        endpointResponse,
        ipResponse,
        uinResponse,
        hourlyResponse
      ] = await Promise.all([
        statsAPI.getDailyStats({ days: dayRange }),
        statsAPI.getTypeStats(),
        statsAPI.getEndpointStats(),
        statsAPI.getIPStats({ limit: 10 }),
        statsAPI.getUINStats({ limit: 10 }),
        statsAPI.getHourlyStats()
      ]);

      // 使用统一的响应格式处理
      const responses = [
        normalizeResponse(dailyResponse),
        normalizeResponse(typeResponse),
        normalizeResponse(endpointResponse),
        normalizeResponse(ipResponse),
        normalizeResponse(uinResponse),
        normalizeResponse(hourlyResponse)
      ];

      if (isResponseSuccess(responses[0])) setDailyStats(getResponseData(responses[0]) || []);
      if (isResponseSuccess(responses[1])) setTypeStats(getResponseData(responses[1]) || []);
      if (isResponseSuccess(responses[2])) setEndpointStats(getResponseData(responses[2]) || []);
      if (isResponseSuccess(responses[3])) setIpStats(getResponseData(responses[3]) || []);
      if (isResponseSuccess(responses[4])) setUinStats(getResponseData(responses[4]) || []);
      if (isResponseSuccess(responses[5])) setHourlyStats(getResponseData(responses[5]) || []);
    } catch (error) {
      console.error('❌ Statistics - 获取统计数据失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  const ipColumns = [
    {
      title: '排名',
      key: 'rank',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
    },
    {
      title: '总请求数',
      dataIndex: 'total_requests',
      key: 'total_requests',
      sorter: (a, b) => a.total_requests - b.total_requests,
    },
    {
      title: '成功请求',
      dataIndex: 'successful_requests',
      key: 'successful_requests',
    },
    {
      title: '失败请求',
      dataIndex: 'failed_requests',
      key: 'failed_requests',
    },
    {
      title: '最后请求',
      dataIndex: 'last_request',
      key: 'last_request',
      render: (text) => new Date(text).toLocaleString(),
    },
  ];

  const uinColumns = [
    {
      title: '排名',
      key: 'rank',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'UIN',
      dataIndex: 'uin',
      key: 'uin',
    },
    {
      title: '总请求数',
      dataIndex: 'total_requests',
      key: 'total_requests',
      sorter: (a, b) => a.total_requests - b.total_requests,
    },
    {
      title: '成功请求',
      dataIndex: 'successful_requests',
      key: 'successful_requests',
    },
    {
      title: '失败请求',
      dataIndex: 'failed_requests',
      key: 'failed_requests',
    },
    {
      title: '最后请求',
      dataIndex: 'last_request',
      key: 'last_request',
      render: (text) => new Date(text).toLocaleString(),
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>加载统计数据中...</p>
      </div>
    );
  }

  return (
    <div>
      <Tabs defaultActiveKey="daily" type="card">
        <TabPane tab="每日趋势" key="daily">
          <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
            <Col span={24}>
              <Card 
                title="每日请求趋势" 
                extra={
                  <Select
                    value={dayRange}
                    onChange={setDayRange}
                    style={{ width: 120 }}
                  >
                    <Option value={7}>最近7天</Option>
                    <Option value={15}>最近15天</Option>
                    <Option value={30}>最近30天</Option>
                  </Select>
                }
              >
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={dailyStats}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="total_requests" 
                      stroke="#8884d8" 
                      name="总请求数"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="successful_requests" 
                      stroke="#82ca9d" 
                      name="成功请求"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="failed_requests" 
                      stroke="#ff7c7c" 
                      name="失败请求"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="今日小时分布">
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={hourlyStats}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="hour" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="total_requests" fill="#8884d8" name="总请求数" />
                    <Bar dataKey="successful_requests" fill="#82ca9d" name="成功请求" />
                  </BarChart>
                </ResponsiveContainer>
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="类型分析" key="types">
          <Row gutter={[16, 16]}>
            <Col xs={24} lg={12}>
              <Card title="请求类型分布">
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={typeStats}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ type, percent }) => `${type} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="total_requests"
                    >
                      {typeStats.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card title="端点请求分布">
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={endpointStats} layout="horizontal">
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis dataKey="endpoint" type="category" width={80} />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="total_requests" fill="#8884d8" name="总请求数" />
                    <Bar dataKey="successful_requests" fill="#82ca9d" name="成功请求" />
                  </BarChart>
                </ResponsiveContainer>
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            {typeStats.map((item, index) => (
              <Col xs={24} sm={12} lg={6} key={item.type}>
                <Card>
                  <Statistic
                    title={`${item.type} 类型`}
                    value={item.total_requests}
                    valueStyle={{ color: COLORS[index % COLORS.length] }}
                    suffix="次"
                  />
                  <div style={{ marginTop: 8, fontSize: 12, color: '#666' }}>
                    成功率: {((item.successful_requests / item.total_requests) * 100).toFixed(1)}%
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </TabPane>

        <TabPane tab="用户分析" key="users">
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Card title="TOP 10 活跃IP">
                <Table
                  columns={ipColumns}
                  dataSource={ipStats}
                  rowKey="ip"
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col span={24}>
              <Card title="TOP 10 活跃UIN">
                <Table
                  columns={uinColumns}
                  dataSource={uinStats}
                  rowKey="uin"
                  pagination={false}
                  size="small"
                />
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Statistics;
