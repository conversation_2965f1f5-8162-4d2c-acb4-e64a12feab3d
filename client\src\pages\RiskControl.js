import React, { useState, useEffect } from 'react';
import { Tabs, Card, Button, Table, Modal, Form, Input, Select, Upload, Space, Tag, Popconfirm, Statistic, Row, Col, Badge, Tooltip, message, Switch, InputNumber } from 'antd';
import { 
  PlusOutlined, 
  ImportOutlined, 
  ExportOutlined, 
  DeleteOutlined, 
  EditOutlined,
  SearchOutlined,
  ReloadOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  WifiOutlined,
  DisconnectOutlined
} from '@ant-design/icons';
import api from '../services/api';
import webSocketService from '../services/websocket';
import NotificationHelper from '../components/NotificationHelper';
import { normalizeResponse, isResponseSuccess, getResponseData, getResponseMessage } from '../utils/responseHelper';
import Swal from 'sweetalert2';

// 添加自定义样式
const customStyles = `
  .risk-detail-popup {
    font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  }
  .risk-detail-content {
    text-align: left !important;
  }
  .risk-detail-content h4 {
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 10px 0;
  }
  .risk-detail-content pre {
    white-space: pre-wrap;
    word-wrap: break-word;
  }
  .swal2-html-container {
    max-height: 70vh;
    overflow-y: auto;
  }
`;

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = customStyles;
  document.head.appendChild(styleElement);
}

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

const RiskControl = () => {
  const [activeTab, setActiveTab] = useState('keywords');
  const [stats, setStats] = useState({});
  
  // WebSocket 连接状态
  const [wsConnected, setWsConnected] = useState(false);
  const [connectedClients, setConnectedClients] = useState([]);
  
  // 关键词管理状态
  const [keywords, setKeywords] = useState([]);
  const [keywordLoading, setKeywordLoading] = useState(false);
  const [keywordModalVisible, setKeywordModalVisible] = useState(false);
  const [keywordForm] = Form.useForm();
  
  // 风控记录状态
  const [records, setRecords] = useState([]);
  const [recordLoading, setRecordLoading] = useState(false);
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [reviewForm] = Form.useForm();
  const [currentRecord, setCurrentRecord] = useState(null);
  
  // 黑名单状态
  const [blacklist, setBlacklist] = useState([]);
  const [blacklistLoading, setBlacklistLoading] = useState(false);
  const [blacklistModalVisible, setBlacklistModalVisible] = useState(false);
  const [blacklistForm] = Form.useForm();
  
  // 白名单状态
  const [whitelist, setWhitelist] = useState([]);
  const [whitelistLoading, setWhitelistLoading] = useState(false);
  const [whitelistModalVisible, setWhitelistModalVisible] = useState(false);
  const [whitelistForm] = Form.useForm();
  
  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  // 批量操作相关状态
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [batchLoading, setBatchLoading] = useState(false);

  // 风控配置相关状态
  const [riskSettings, setRiskSettings] = useState({});
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);
  // 初始化数据
  useEffect(() => {
    loadStats();
    loadData();
    loadRiskSettings();
    initWebSocket();
    
    // 清理函数
    return () => {
      webSocketService.disconnect();
    };
  }, [activeTab, pagination.current, pagination.pageSize]);

  // 初始化 WebSocket 连接
  const initWebSocket = async () => {
    try {
      // 连接 WebSocket
      await webSocketService.connect();
      setWsConnected(true);
      
      // 监听连接事件
      webSocketService.on('connected', () => {
        setWsConnected(true);
        NotificationHelper.operationSuccess('WebSocket 连接', 'WebSocket 连接成功');
      });
      
      webSocketService.on('disconnected', () => {
        setWsConnected(false);
        NotificationHelper.warning('WebSocket 连接断开', 'WebSocket 连接已断开');
      });
      
      webSocketService.on('error', (error) => {
        setWsConnected(false);
        console.error('WebSocket 错误:', error);
      });
      
      // 监听风控事件
      webSocketService.on('risk_detection', (data) => {
        NotificationHelper.warning('风险检测', `检测到风险活动：${data.risk_level} 级别`);
        // 刷新风控记录
        if (activeTab === 'records') {
          loadRecords();
        }
      });
      
      webSocketService.on('client_connected', (data) => {
        NotificationHelper.info('客户端连接', `新客户端连接：${data.client_id}`);
        loadConnectedClients();
      });
      
      webSocketService.on('client_disconnected', (data) => {
        NotificationHelper.info('客户端断开', `客户端断开：${data.client_id}`);
        loadConnectedClients();
      });
      
      // 加载连接的客户端
      loadConnectedClients();
      
    } catch (error) {
      console.error('WebSocket 连接失败:', error);
      setWsConnected(false);
    }
  };

  // 加载连接的客户端
  const loadConnectedClients = async () => {
    try {
      const clients = await webSocketService.getConnectedClients();
      setConnectedClients(clients || []);
    } catch (error) {
      console.error('获取客户端列表失败:', error);
    }
  };  // 加载统计数据
  const loadStats = async () => {
    try {
      const response = await api.get('/risk/stats');
      const normalizedResponse = normalizeResponse(response);
      
      if (isResponseSuccess(normalizedResponse)) {
        setStats(getResponseData(normalizedResponse));
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  };

  // 根据当前标签加载数据
  const loadData = () => {
    switch (activeTab) {
      case 'keywords':
        loadKeywords();
        break;
      case 'records':
        loadRecords();
        break;
      case 'blacklist':
        loadBlacklist();
        break;
      case 'whitelist':
        loadWhitelist();
        break;
    }
  };

  // 加载关键词列表
  const loadKeywords = async () => {
    setKeywordLoading(true);    try {
      const response = await api.get('/risk/keywords', {
        params: {
          page: pagination.current,
          limit: pagination.pageSize
        }
      });
      
      const normalizedResponse = normalizeResponse(response);
      if (isResponseSuccess(normalizedResponse)) {
        const data = getResponseData(normalizedResponse);
        setKeywords(data.keywords);
        setPagination(prev => ({
          ...prev,
          total: data.pagination.total
        }));
      } else {
        NotificationHelper.error('加载失败', getResponseMessage(normalizedResponse));
      }
    } catch (error) {
      console.error('加载关键词失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '加载关键词失败');
    } finally {
      setKeywordLoading(false);
    }
  };
  // 加载风控记录
  const loadRecords = async () => {
    setRecordLoading(true);
    try {
      const response = await api.get('/risk/records', {
        params: {
          page: pagination.current,
          limit: pagination.pageSize
        }
      });
      
      const normalizedResponse = normalizeResponse(response);
      if (isResponseSuccess(normalizedResponse)) {
        const data = getResponseData(normalizedResponse);
        setRecords(data.records);
        setPagination(prev => ({
          ...prev,
          total: data.pagination.total
        }));
      } else {
        NotificationHelper.error('加载失败', getResponseMessage(normalizedResponse));
      }
    } catch (error) {
      console.error('加载风控记录失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '加载风控记录失败');
    } finally {
      setRecordLoading(false);
    }
  };

  // 加载黑名单
  const loadBlacklist = async () => {
    setBlacklistLoading(true);
    try {
      const response = await api.get('/risk/blacklist', {
        params: {
          page: pagination.current,
          limit: pagination.pageSize
        }
      });      // 使用统一的响应格式处理
      const normalizedResponse = normalizeResponse(response);
      
      if (isResponseSuccess(normalizedResponse)) {
        const data = getResponseData(normalizedResponse);
        setBlacklist(data.blacklist);
        setPagination(prev => ({
          ...prev,
          total: data.pagination.total
        }));
      } else {
        NotificationHelper.error('加载失败', getResponseMessage(normalizedResponse));
      }} catch (error) {
      console.error('加载黑名单失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '加载黑名单失败');
    } finally {
      setBlacklistLoading(false);
    }
  };

  // 加载白名单
  const loadWhitelist = async () => {
    setWhitelistLoading(true);
    try {
      const response = await api.get('/risk/whitelist', {
        params: {
          page: pagination.current,
          limit: pagination.pageSize
        }
      });      // 使用统一的响应格式处理
      const normalizedResponse = normalizeResponse(response);
      
      if (isResponseSuccess(normalizedResponse)) {
        const data = getResponseData(normalizedResponse);
        setWhitelist(data.whitelist);
        setPagination(prev => ({
          ...prev,
          total: data.pagination.total
        }));
      } else {
        NotificationHelper.error('加载失败', getResponseMessage(normalizedResponse));
      }} catch (error) {
      console.error('加载白名单失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '加载白名单失败');
    } finally {
      setWhitelistLoading(false);
    }
  };

  // 获取默认风控配置
  const getDefaultRiskSettings = () => {
    return {
      enable_risk_detection: { value: true, type: 'boolean', description: '是否启用风控检测', category: 'detection' },
      enable_keyword_filter: { value: true, type: 'boolean', description: '是否启用关键词过滤', category: 'detection' },
      enable_ip_blacklist: { value: true, type: 'boolean', description: '是否启用IP黑名单', category: 'blacklist' },
      enable_user_blacklist: { value: true, type: 'boolean', description: '是否启用用户黑名单', category: 'blacklist' },
      enable_auto_block: { value: true, type: 'boolean', description: '是否启用自动拦截', category: 'action' },
      enable_whitelist_bypass: { value: true, type: 'boolean', description: '是否启用白名单绕过', category: 'whitelist' },
      max_risk_score: { value: 80, type: 'number', description: '最大风险评分阈值', category: 'threshold' },
      auto_block_threshold: { value: 70, type: 'number', description: '自动拦截阈值', category: 'threshold' },
      batch_operation_limit: { value: 100, type: 'number', description: '批量操作限制数量', category: 'batch' },
      enable_batch_review: { value: true, type: 'boolean', description: '是否启用批量审核', category: 'batch' },
      enable_batch_delete: { value: true, type: 'boolean', description: '是否启用批量删除', category: 'batch' },
      enable_batch_blacklist: { value: true, type: 'boolean', description: '是否启用批量加入黑名单', category: 'batch' }
    };
  };

  // 加载风控配置
  const loadRiskSettings = async () => {
    try {
      const response = await api.get('/risk/settings');
      const normalizedResponse = normalizeResponse(response);
      if (isResponseSuccess(normalizedResponse)) {
        const data = getResponseData(normalizedResponse);
        setRiskSettings(data);
      } else {
        console.warn('加载配置失败，使用默认配置:', getResponseMessage(normalizedResponse));
        setRiskSettings(getDefaultRiskSettings());
      }
    } catch (error) {
      console.error('加载风控配置失败，使用默认配置:', error);
      setRiskSettings(getDefaultRiskSettings());
    }
  };

  // 风险等级颜色映射
  const getRiskLevelColor = (level) => {
    const colors = {
      low: 'green',
      medium: 'orange',
      high: 'red',
      critical: 'purple'
    };
    return colors[level] || 'default';
  };

  // 风险等级标签
  const getRiskLevelTag = (level) => {
    const labels = {
      low: '低风险',
      medium: '中风险',
      high: '高风险',
      critical: '严重风险'
    };
    return (
      <Tag color={getRiskLevelColor(level)}>
        {labels[level] || level}
      </Tag>
    );
  };

  // 状态标签
  const getStatusTag = (status) => {
    const config = {
      pending: { color: 'orange', text: '待审核', icon: <InfoCircleOutlined /> },
      approved: { color: 'red', text: '已通过', icon: <CheckCircleOutlined /> },
      rejected: { color: 'green', text: '已拒绝', icon: <CloseCircleOutlined /> },
      ignored: { color: 'gray', text: '已忽略', icon: <WarningOutlined /> }
    };
    
    const cfg = config[status] || { color: 'default', text: status };
    return (
      <Tag color={cfg.color} icon={cfg.icon}>
        {cfg.text}
      </Tag>
    );
  };

  // 关键词表格列定义
  const keywordColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '关键词',
      dataIndex: 'keyword',
      key: 'keyword',
      render: (text) => <span style={{ fontWeight: 'bold' }}>{text}</span>
    },
    {
      title: '匹配类型',
      dataIndex: 'keyword_type',
      key: 'keyword_type',
      render: (type) => {
        const types = {
          strict: { color: 'blue', text: '严格匹配' },
          fuzzy: { color: 'orange', text: '模糊匹配' },
          regex: { color: 'purple', text: '正则匹配' }
        };
        const cfg = types[type] || { color: 'default', text: type };
        return <Tag color={cfg.color}>{cfg.text}</Tag>;
      }
    },
    {
      title: '风险等级',
      dataIndex: 'risk_level',
      key: 'risk_level',
      render: getRiskLevelTag
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_time',
      key: 'created_time',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            size="small"
            onClick={() => editKeyword(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个关键词吗？"
            onConfirm={() => deleteKeyword(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 黑名单表格列定义
  const blacklistColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username'
    },
    {
      title: '设备ID',
      dataIndex: 'device_id',
      key: 'device_id',
      ellipsis: true
    },
    {
      title: 'IP地址',
      dataIndex: 'ip_address',
      key: 'ip_address'
    },
    {
      title: 'UIN',
      dataIndex: 'uin',
      key: 'uin'
    },
    {
      title: '风险内容',
      dataIndex: 'risk_content',
      key: 'risk_content',
      ellipsis: true,
      render: (text) => (
        <span title={text}>
          {text && text.length > 30 ? text.substring(0, 30) + '...' : text}
        </span>
      )
    },
    {
      title: '封禁原因',
      dataIndex: 'ban_reason',
      key: 'ban_reason',
      ellipsis: true
    },
    {
      title: '封禁类型',
      dataIndex: 'ban_type',
      key: 'ban_type',
      render: (type) => (
        <Tag color={type === 'permanent' ? 'red' : 'orange'}>
          {type === 'permanent' ? '永久' : '临时'}
        </Tag>
      )
    },
    {
      title: '到期时间',
      dataIndex: 'ban_end_time',
      key: 'ban_end_time',
      render: (time, record) => {
        if (record.ban_type === 'permanent') return '永久';
        return time ? new Date(time).toLocaleString() : '-';
      }
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active) => (
        <Tag color={active ? 'red' : 'gray'}>
          {active ? '生效中' : '已失效'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_time',
      key: 'created_time',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            size="small"
            onClick={() => editBlacklist(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条黑名单记录吗？"
            onConfirm={() => deleteBlacklist(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 白名单表格列定义
  const whitelistColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username'
    },
    {
      title: '设备ID',
      dataIndex: 'device_id',
      key: 'device_id',
      ellipsis: true
    },
    {
      title: 'IP地址',
      dataIndex: 'ip_address',
      key: 'ip_address'
    },
    {
      title: 'UIN',
      dataIndex: 'uin',
      key: 'uin'
    },
    {
      title: '白名单类型',
      dataIndex: 'whitelist_type',
      key: 'whitelist_type',
      render: (type) => {
        const types = {
          username: { color: 'blue', text: '用户名' },
          device: { color: 'green', text: '设备' },
          ip: { color: 'orange', text: 'IP地址' },
          uin: { color: 'purple', text: 'UIN' },
          comprehensive: { color: 'cyan', text: '综合' }
        };
        const cfg = types[type] || { color: 'default', text: type };
        return <Tag color={cfg.color}>{cfg.text}</Tag>;
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active) => (
        <Tag color={active ? 'green' : 'gray'}>
          {active ? '生效中' : '已失效'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_time',
      key: 'created_time',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            size="small"
            onClick={() => editWhitelist(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这条白名单记录吗？"
            onConfirm={() => deleteWhitelist(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];
  const recordColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: 'IP地址',
      dataIndex: 'ip_address',
      key: 'ip_address'
    },
    {
      title: 'UIN',
      dataIndex: 'uin',
      key: 'uin'
    },
    {
      title: '风险内容',
      dataIndex: 'risk_content',
      key: 'risk_content',
      ellipsis: true,
      render: (text) => (
        <span title={text}>
          {text && text.length > 50 ? text.substring(0, 50) + '...' : text}
        </span>
      )
    },
    {
      title: '风险评分',
      dataIndex: 'risk_score',
      key: 'risk_score',
      render: (score) => (
        <span style={{ 
          color: score >= 80 ? '#ff4d4f' : score >= 50 ? '#fa8c16' : '#52c41a',
          fontWeight: 'bold'
        }}>
          {score}
        </span>
      )
    },
    {
      title: '风险等级',
      dataIndex: 'risk_level',
      key: 'risk_level',
      render: getRiskLevelTag
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: getStatusTag
    },
    {
      title: '自动拦截',
      dataIndex: 'auto_blocked',
      key: 'auto_blocked',
      render: (blocked) => (
        <Tag color={blocked ? 'red' : 'green'}>
          {blocked ? '是' : '否'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_time',
      key: 'created_time',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          {record.status === 'pending' && (
            <Button
              type="primary"
              size="small"
              onClick={() => reviewRecord(record)}
            >
              审核
            </Button>
          )}
          <Button
            type="link"
            size="small"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log('详情按钮被点击，记录ID:', record.id);
              console.log('记录数据:', record);
              try {
                viewRecordDetail(record);
              } catch (error) {
                console.error('调用 viewRecordDetail 失败:', error);
                message.error('显示详情失败');
              }
            }}
          >
            详情
          </Button>
        </Space>
      )
    }
  ];

  // 添加/编辑关键词
  const handleKeywordSubmit = async (values) => {
    try {      if (values.id) {
        // 编辑逻辑（如果需要）
        await api.put(`/risk/keywords/${values.id}`, values);
        NotificationHelper.operationSuccess('update', '关键词更新成功');
      } else {
        await api.post('/risk/keywords', values);
        NotificationHelper.operationSuccess('create', '关键词添加成功');
      }
      
      setKeywordModalVisible(false);
      keywordForm.resetFields();
      loadKeywords();    } catch (error) {
      console.error('关键词操作失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '操作失败');
    }
  };

  // 删除关键词
  const deleteKeyword = async (id) => {
    try {
      await api.delete(`/risk/keywords/${id}`);
      NotificationHelper.operationSuccess('delete', '关键词删除成功');
      loadKeywords();
    } catch (error) {
      console.error('删除关键词失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '删除失败');
    }
  };

  // 编辑关键词
  const editKeyword = (record) => {
    keywordForm.setFieldsValue(record);
    setKeywordModalVisible(true);
  };

  // 审核风控记录
  const reviewRecord = (record) => {
    setCurrentRecord(record);
    reviewForm.setFieldsValue({
      status: 'approved'
    });
    setReviewModalVisible(true);
  };

  // 处理审核提交
  const handleReviewSubmit = async (values) => {
    try {
      await api.put(`/risk/records/${currentRecord.id}/review`, values);
      NotificationHelper.operationSuccess('审核完成', '审核操作已完成');
      setReviewModalVisible(false);
      reviewForm.resetFields();
      loadRecords();
    } catch (error) {
      NotificationHelper.networkError(500, '审核失败');
    }
  };

  // 更新风控配置
  const updateRiskSetting = async (key, value, type = 'string') => {
    try {
      const response = await api.put(`/risk/settings/${key}`, { value, type });
      const normalizedResponse = normalizeResponse(response);

      if (isResponseSuccess(normalizedResponse)) {
        // 更新本地状态
        setRiskSettings(prev => ({
          ...prev,
          [key]: { ...prev[key], value }
        }));

        NotificationHelper.operationSuccess('配置更新', '配置已成功更新');
      } else {
        NotificationHelper.error('配置更新失败', getResponseMessage(normalizedResponse));
      }
    } catch (error) {
      console.error('更新风控配置失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '更新配置失败');
    }
  };

  // 批量审核记录
  const handleBatchReview = async (status, reason = '') => {
    if (selectedRowKeys.length === 0) {
      NotificationHelper.warning('请选择记录', '请先选择要审核的记录');
      return;
    }

    setBatchLoading(true);
    try {
      const response = await api.post('/risk/records/batch-review', {
        record_ids: selectedRowKeys,
        status,
        review_reason: reason
      });

      const normalizedResponse = normalizeResponse(response);
      if (isResponseSuccess(normalizedResponse)) {
        NotificationHelper.operationSuccess('批量审核', `成功审核 ${selectedRowKeys.length} 条记录`);
        setSelectedRowKeys([]);
        loadRecords();
      } else {
        NotificationHelper.error('批量审核失败', getResponseMessage(normalizedResponse));
      }
    } catch (error) {
      console.error('批量审核失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '批量审核失败');
    } finally {
      setBatchLoading(false);
    }
  };

  // 批量删除记录
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      NotificationHelper.warning('请选择记录', '请先选择要删除的记录');
      return;
    }

    const result = await Swal.fire({
      title: '确认批量删除',
      text: `确定要删除选中的 ${selectedRowKeys.length} 条记录吗？此操作不可撤销！`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ff4d4f',
      cancelButtonColor: '#d9d9d9',
      confirmButtonText: '确定删除',
      cancelButtonText: '取消'
    });

    if (!result.isConfirmed) return;

    setBatchLoading(true);
    try {
      const response = await api.delete('/risk/records/batch-delete', {
        data: { record_ids: selectedRowKeys }
      });

      const normalizedResponse = normalizeResponse(response);
      if (isResponseSuccess(normalizedResponse)) {
        NotificationHelper.operationSuccess('批量删除', `成功删除 ${selectedRowKeys.length} 条记录`);
        setSelectedRowKeys([]);
        loadRecords();
      } else {
        NotificationHelper.error('批量删除失败', getResponseMessage(normalizedResponse));
      }
    } catch (error) {
      console.error('批量删除失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '批量删除失败');
    } finally {
      setBatchLoading(false);
    }
  };

  // 批量加入黑名单
  const handleBatchBlacklist = async () => {
    if (selectedRowKeys.length === 0) {
      NotificationHelper.warning('请选择记录', '请先选择要加入黑名单的记录');
      return;
    }

    const { value: reason } = await Swal.fire({
      title: '批量加入黑名单',
      text: `确定要将选中的 ${selectedRowKeys.length} 条记录加入黑名单吗？`,
      input: 'textarea',
      inputLabel: '封禁原因',
      inputPlaceholder: '请输入封禁原因...',
      inputValue: '批量风控处理',
      showCancelButton: true,
      confirmButtonColor: '#ff4d4f',
      cancelButtonColor: '#d9d9d9',
      confirmButtonText: '确定加入',
      cancelButtonText: '取消',
      inputValidator: (value) => {
        if (!value.trim()) {
          return '请输入封禁原因';
        }
      }
    });

    if (!reason) return;

    setBatchLoading(true);
    try {
      const response = await api.post('/risk/records/batch-blacklist', {
        record_ids: selectedRowKeys,
        ban_reason: reason,
        ban_type: 'permanent'
      });

      const normalizedResponse = normalizeResponse(response);
      if (isResponseSuccess(normalizedResponse)) {
        const data = getResponseData(normalizedResponse);
        NotificationHelper.operationSuccess('批量加入黑名单',
          `成功 ${data.successCount} 条，失败 ${data.failCount} 条`);
        setSelectedRowKeys([]);
        loadRecords();
        loadBlacklist();
      } else {
        NotificationHelper.error('批量加入黑名单失败', getResponseMessage(normalizedResponse));
      }
    } catch (error) {
      console.error('批量加入黑名单失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '批量加入黑名单失败');
    } finally {
      setBatchLoading(false);
    }
  };

  // 查看记录详情
  const viewRecordDetail = async (record) => {
    console.log('viewRecordDetail 被调用，记录ID:', record.id);

    try {
      // 显示加载状态
      Swal.fire({
        title: '加载中...',
        text: '正在获取记录详情...',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        didOpen: () => {
          Swal.showLoading();
        }
      });

      // 调用API获取完整的记录详情
      const response = await api.get(`/risk/records/${record.id}`);
      const normalizedResponse = normalizeResponse(response);

      if (!isResponseSuccess(normalizedResponse)) {
        throw new Error(getResponseMessage(normalizedResponse));
      }

      const detailRecord = getResponseData(normalizedResponse);
      console.log('获取到的详细记录:', detailRecord);

      // 生成风险等级颜色
      const getRiskLevelColor = (level) => {
        const colors = {
          low: '#52c41a',
          medium: '#faad14',
          high: '#ff4d4f',
          critical: '#722ed1'
        };
        return colors[level] || '#d9d9d9';
      };

      // 生成状态标签
      const getStatusBadge = (status) => {
        const statusMap = {
          pending: { text: '待审核', color: '#faad14' },
          approved: { text: '已通过', color: '#52c41a' },
          rejected: { text: '已拒绝', color: '#ff4d4f' },
          ignored: { text: '已忽略', color: '#d9d9d9' }
        };
        const statusInfo = statusMap[status] || { text: status || '未知', color: '#d9d9d9' };
        return `<span style="background-color: ${statusInfo.color}; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">${statusInfo.text}</span>`;
      };

      // 生成匹配关键词标签
      const generateKeywordTags = (keywords) => {
        if (!keywords || !Array.isArray(keywords) || keywords.length === 0) {
          return '<span style="color: #999;">无匹配关键词</span>';
        }

        return keywords.map(kw => {
          const keyword = kw.keyword || kw;
          const level = kw.risk_level || 'medium';
          const color = getRiskLevelColor(level);
          return `<span style="background-color: ${color}; color: white; padding: 2px 6px; border-radius: 3px; margin: 2px; display: inline-block; font-size: 11px;">${keyword} (${level})</span>`;
        }).join('');
      };

      // 关闭加载状态并显示详情
      Swal.fire({
        title: `🔍 风控记录详情 - ID: ${detailRecord.id}`,
        html: `
          <div style="text-align: left; max-height: 600px; overflow-y: auto; padding: 10px;">
            <!-- 基础信息 -->
            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <h4 style="margin: 0 0 10px 0; color: #333; border-bottom: 2px solid #1890ff; padding-bottom: 5px;">📋 基础信息</h4>
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 13px;">
                <div><strong>记录ID:</strong> ${detailRecord.id || 'N/A'}</div>
                <div><strong>状态:</strong> ${getStatusBadge(detailRecord.status)}</div>
                <div><strong>IP地址:</strong> ${detailRecord.ip_address || 'N/A'}</div>
                <div><strong>UIN:</strong> ${detailRecord.uin || 'N/A'}</div>
                <div><strong>用户名:</strong> ${detailRecord.username || 'N/A'}</div>
                <div><strong>设备ID:</strong> ${detailRecord.device_id || 'N/A'}</div>
                <div><strong>风险评分:</strong> <span style="color: ${getRiskLevelColor(detailRecord.risk_level)}; font-weight: bold;">${detailRecord.risk_score || 'N/A'}</span></div>
                <div><strong>风险等级:</strong> <span style="background-color: ${getRiskLevelColor(detailRecord.risk_level)}; color: white; padding: 2px 8px; border-radius: 4px; font-size: 12px;">${detailRecord.risk_level || 'N/A'}</span></div>
                <div><strong>自动拦截:</strong> ${detailRecord.auto_blocked ? '✅ 是' : '❌ 否'}</div>
                <div><strong>创建时间:</strong> ${detailRecord.created_time ? new Date(detailRecord.created_time).toLocaleString() : 'N/A'}</div>
              </div>
            </div>

            <!-- 风险内容 -->
            <div style="background: #fff2e8; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <h4 style="margin: 0 0 10px 0; color: #333; border-bottom: 2px solid #fa8c16; padding-bottom: 5px;">⚠️ 风险内容</h4>
              <div style="background: white; border: 1px solid #d9d9d9; border-radius: 4px; padding: 10px; max-height: 120px; overflow-y: auto; font-family: monospace; font-size: 12px; line-height: 1.4;">
                ${(detailRecord.risk_content || 'N/A').replace(/\n/g, '<br>')}
              </div>
            </div>

            <!-- 请求详情 -->
            ${detailRecord.request_data ? `
            <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <h4 style="margin: 0 0 10px 0; color: #333; border-bottom: 2px solid #1890ff; padding-bottom: 5px;">📡 请求详情</h4>
              ${(() => {
                try {
                  if (!detailRecord.request_data) {
                    return '<div style="color: #999; font-style: italic;">无请求数据</div>';
                  }

                  let requestData;
                  if (typeof detailRecord.request_data === 'string') {
                    requestData = JSON.parse(detailRecord.request_data);
                  } else {
                    requestData = detailRecord.request_data;
                  }

                  return `
                    <div style="margin-bottom: 12px;">
                      <strong>请求方法:</strong> <span style="background: #e6f7ff; padding: 2px 6px; border-radius: 3px; font-family: monospace;">${requestData.method || 'N/A'}</span>
                      <strong style="margin-left: 15px;">URL:</strong> <span style="background: #e6f7ff; padding: 2px 6px; border-radius: 3px; font-family: monospace;">${requestData.url || 'N/A'}</span>
                    </div>

                    ${requestData.headers ? `
                    <div style="margin-bottom: 12px;">
                      <strong>请求头:</strong>
                      <div style="background: white; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px; margin-top: 5px; max-height: 100px; overflow-y: auto;">
                        <pre style="margin: 0; font-size: 11px; line-height: 1.3;">${Object.entries(requestData.headers).map(([key, value]) => `${key}: ${value || 'N/A'}`).join('\n')}</pre>
                      </div>
                    </div>
                    ` : ''}

                    ${requestData.query && Object.keys(requestData.query).length > 0 ? `
                    <div style="margin-bottom: 12px;">
                      <strong>查询参数:</strong>
                      <div style="background: white; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px; margin-top: 5px;">
                        <pre style="margin: 0; font-size: 11px; line-height: 1.3;">${JSON.stringify(requestData.query, null, 2)}</pre>
                      </div>
                    </div>
                    ` : ''}

                    ${requestData.body || requestData.raw_body ? `
                    <div style="margin-bottom: 12px;">
                      <strong>请求体:</strong>
                      ${requestData.body_type ? `<span style="margin-left: 10px; font-size: 11px; color: #666;">(${requestData.body_type})</span>` : ''}

                      ${requestData.raw_body ? `
                      <div style="margin-top: 5px;">
                        <div style="font-size: 11px; color: #666; margin-bottom: 3px;">原始请求体:</div>
                        <div style="background: white; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px; max-height: 120px; overflow-y: auto;">
                          <pre style="margin: 0; font-size: 11px; line-height: 1.3; font-family: 'Courier New', monospace;">${requestData.raw_body}</pre>
                        </div>
                      </div>
                      ` : ''}

                      ${requestData.body && requestData.body !== requestData.raw_body ? `
                      <div style="margin-top: 8px;">
                        <div style="font-size: 11px; color: #666; margin-bottom: 3px;">解析后的请求体:</div>
                        <div style="background: white; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px; max-height: 120px; overflow-y: auto;">
                          <pre style="margin: 0; font-size: 11px; line-height: 1.3;">${typeof requestData.body === 'string' ? requestData.body : JSON.stringify(requestData.body, null, 2)}</pre>
                        </div>
                      </div>
                      ` : ''}
                    </div>
                    ` : ''}

                    ${requestData.stats ? `
                    <div style="margin-bottom: 8px;">
                      <strong>请求统计:</strong>
                      <span style="margin-left: 10px; font-size: 12px;">
                        大小: ${requestData.stats.content_length || 0}B |
                        参数: ${requestData.stats.param_count || 0}个 |
                        有请求体: ${requestData.stats.has_body ? '是' : '否'}
                      </span>
                    </div>
                    ` : ''}
                  `;
                } catch (e) {
                  console.error('解析请求数据失败:', e, detailRecord.request_data);
                  return `
                    <div style="color: #ff4d4f; font-style: italic; margin-bottom: 10px;">请求数据解析失败: ${e.message}</div>
                    <div style="margin-bottom: 8px;"><strong>原始数据:</strong></div>
                    <div style="background: white; border: 1px solid #d9d9d9; border-radius: 4px; padding: 8px; max-height: 120px; overflow-y: auto;">
                      <pre style="margin: 0; font-size: 11px; line-height: 1.3; font-family: 'Courier New', monospace;">${detailRecord.request_data || 'N/A'}</pre>
                    </div>
                  `;
                }
              })()}
            </div>
            ` : ''}

            <!-- 匹配关键词 -->
            <div style="background: #f6ffed; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <h4 style="margin: 0 0 10px 0; color: #333; border-bottom: 2px solid #52c41a; padding-bottom: 5px;">🏷️ 匹配关键词</h4>
              <div style="line-height: 1.8;">
                ${generateKeywordTags(detailRecord.matched_keywords)}
              </div>
            </div>

            ${detailRecord.client_info ? `
            <!-- 客户端信息 -->
            <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
              <h4 style="margin: 0 0 10px 0; color: #333; border-bottom: 2px solid #1890ff; padding-bottom: 5px;">💻 客户端信息</h4>
              <pre style="background: white; border: 1px solid #d9d9d9; border-radius: 4px; padding: 10px; max-height: 120px; overflow-y: auto; font-size: 11px; line-height: 1.3; margin: 0;">${JSON.stringify(detailRecord.client_info, null, 2)}</pre>
            </div>
            ` : ''}

            ${detailRecord.review_reason || detailRecord.review_time ? `
            <!-- 审核信息 -->
            <div style="background: #f9f0ff; padding: 15px; border-radius: 8px;">
              <h4 style="margin: 0 0 10px 0; color: #333; border-bottom: 2px solid #722ed1; padding-bottom: 5px;">👨‍💼 审核信息</h4>
              ${detailRecord.review_reason ? `<div style="margin-bottom: 8px; font-size: 13px;"><strong>审核原因:</strong> ${detailRecord.review_reason}</div>` : ''}
              ${detailRecord.review_time ? `<div style="font-size: 13px;"><strong>审核时间:</strong> ${new Date(detailRecord.review_time).toLocaleString()}</div>` : ''}
            </div>
            ` : ''}
          </div>
        `,
        width: 900,
        confirmButtonText: '关闭',
        confirmButtonColor: '#1890ff',
        allowOutsideClick: true,
        allowEscapeKey: true,
        customClass: {
          popup: 'risk-detail-popup',
          htmlContainer: 'risk-detail-content'
        },
        didOpen: () => {
          console.log('SweetAlert2 风控详情模态框已打开');
        },
        willClose: () => {
          console.log('SweetAlert2 风控详情模态框即将关闭');
        }
      });

      console.log('详情模态框显示成功');
    } catch (error) {
      console.error('获取记录详情失败:', error);
      Swal.fire({
        title: '获取详情失败',
        text: error.message || '网络错误，请稍后重试',
        icon: 'error',
        confirmButtonText: '知道了',
        confirmButtonColor: '#ff4d4f'
      });
    }
  };

  // 文件上传配置
  const uploadProps = {
    name: 'file',
    action: `${api.defaults.baseURL}/risk/keywords/import`,
    accept: '.txt,.csv',
    showUploadList: false,
    headers: {},
    onChange(info) {
      if (info.file.status === 'done') {
        NotificationHelper.operationSuccess('导入完成', '关键词导入成功');
          loadKeywords();
      } else if (info.file.status === 'error') {
        NotificationHelper.networkError(500, '文件上传失败');
      }
    },
  };

  // 添加黑名单
  const handleBlacklistAdd = () => {
    blacklistForm.resetFields();
    setBlacklistModalVisible(true);
  };

  // 添加/编辑黑名单
  const handleBlacklistSubmit = async (values) => {
    try {
      const payload = { ...values }; // 直接传所有表单字段
      if (values.id) {
        await api.put(`/risk/blacklist/${values.id}`, payload);
        NotificationHelper.operationSuccess('黑名单更新', '黑名单更新成功');
      } else {
        await api.post('/risk/blacklist', payload);
        NotificationHelper.operationSuccess('黑名单添加', '黑名单添加成功');
      }
        setBlacklistModalVisible(false);
      blacklistForm.resetFields();
      loadBlacklist();
    } catch (error) {
      NotificationHelper.networkError(500, '黑名单操作失败');
    }
  };

  // 编辑黑名单
  const editBlacklist = async (record) => {
    try {
      const response = await api.get(`/risk/blacklist/${record.id}`);
      const normalizedResponse = normalizeResponse(response);
      if (isResponseSuccess(normalizedResponse)) {
        blacklistForm.setFieldsValue(getResponseData(normalizedResponse));
    setBlacklistModalVisible(true);
      } else {
        NotificationHelper.error('获取详情失败', getResponseMessage(normalizedResponse));
      }
    } catch (error) {
      NotificationHelper.networkError(500, '获取黑名单详情失败');
    }
  };

  // 删除黑名单
  const deleteBlacklist = async (id) => {
    try {
      await api.delete(`/risk/blacklist/${id}`);
      NotificationHelper.operationSuccess('删除完成', '黑名单删除成功');
      loadBlacklist();
    } catch (error) {
      NotificationHelper.networkError(500, '黑名单删除失败');
    }
  };

  // 添加白名单
  const handleWhitelistAdd = () => {
    whitelistForm.resetFields();
    setWhitelistModalVisible(true);
  };

  // 添加/编辑白名单
  const handleWhitelistSubmit = async (values) => {
    try {
      if (values.id) {
        await api.put(`/risk/whitelist/${values.id}`, values);
        NotificationHelper.operationSuccess('白名单更新', '白名单更新成功');
      } else {
        await api.post('/risk/whitelist', values);
        NotificationHelper.operationSuccess('白名单添加', '白名单添加成功');
      }
        setWhitelistModalVisible(false);
      whitelistForm.resetFields();
      loadWhitelist();
    } catch (error) {
      NotificationHelper.networkError(500, '白名单操作失败');
    }
  };

  // 编辑白名单
  const editWhitelist = (record) => {
    whitelistForm.setFieldsValue(record);
    setWhitelistModalVisible(true);
  };
  // 删除白名单
  const deleteWhitelist = async (id) => {
    try {
      await api.delete(`/risk/whitelist/${id}`);
      NotificationHelper.operationSuccess('删除完成', '白名单删除成功');
      loadWhitelist();
    } catch (error) {
      NotificationHelper.networkError(500, '白名单删除失败');
    }
  };const exportKeywords = async (format = 'csv') => {
    try {
      message.loading('正在生成导出文件...', 1);
      
      // 使用 fetch 直接下载文件
      const response = await fetch(`${api.defaults.baseURL}/risk/keywords/export?format=${format}`);
      
      if (!response.ok) {
        throw new Error('导出失败');
      }
      
      // 获取文件名
      const contentDisposition = response.headers.get('content-disposition');
      const filenameMatch = contentDisposition && contentDisposition.match(/filename="(.+)"/);
      const filename = filenameMatch ? filenameMatch[1] : `risk_keywords_${new Date().toISOString().split('T')[0]}.${format}`;
      
      // 创建下载链接
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      
      // 清理
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      
      NotificationHelper.operationSuccess('导出完成', '导出成功！');
    } catch (error) {
      console.error('导出失败:', error);
      NotificationHelper.networkError(500, '导出失败: ' + error.message);
    }
  };
  // 统计卡片
  const StatsCards = () => (
    <Row gutter={16} style={{ marginBottom: 16 }}>
      <Col span={5}>
        <Card>
          <Statistic
            title="关键词总数"
            value={stats.keywords?.total_keywords || 0}
            prefix={<SearchOutlined />}
          />
        </Card>
      </Col>
      <Col span={5}>
        <Card>
          <Statistic
            title="待审核记录"
            value={stats.records?.pending_records || 0}
            prefix={<InfoCircleOutlined />}
            valueStyle={{ color: '#fa8c16' }}
          />
        </Card>
      </Col>
      <Col span={5}>
        <Card>
          <Statistic
            title="黑名单数量"
            value={stats.blacklist?.active_blacklist || 0}
            prefix={<CloseCircleOutlined />}
            valueStyle={{ color: '#ff4d4f' }}
          />
        </Card>
      </Col>
      <Col span={5}>
        <Card>
          <Statistic
            title="白名单数量"
            value={stats.whitelist?.active_whitelist || 0}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic
            title={
              <Tooltip title={wsConnected ? 'WebSocket 已连接' : 'WebSocket 未连接'}>
                <Space>
                  连接状态
                  <Badge 
                    status={wsConnected ? 'success' : 'error'} 
                    icon={wsConnected ? <WifiOutlined /> : <DisconnectOutlined />}
                  />
                </Space>
              </Tooltip>
            }
            value={connectedClients.length}
            suffix="个客户端"
            valueStyle={{ 
              color: wsConnected ? '#52c41a' : '#ff4d4f',
              fontSize: '16px'
            }}
          />
        </Card>
      </Col>
    </Row>
  );
  return (
    <div style={{ padding: '24px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
        <h1>风控管理</h1>
        <Space>
          <Tooltip title="实时风控检测">
            <Button 
              type="primary" 
              icon={wsConnected ? <WifiOutlined /> : <DisconnectOutlined />}
              onClick={() => setActiveTab('realtime')}
              style={{ 
                backgroundColor: wsConnected ? '#52c41a' : '#ff4d4f',
                borderColor: wsConnected ? '#52c41a' : '#ff4d4f'
              }}
            >
              {wsConnected ? '已连接' : '未连接'} ({connectedClients.length})
            </Button>
          </Tooltip>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={() => {
              loadStats();
              loadData();
            }}
          >
            刷新数据
          </Button>
        </Space>
      </div>
      
      <StatsCards />
      
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="实时监控" key="realtime">
            <RealTimeMonitor
              wsConnected={wsConnected}
              connectedClients={connectedClients}
              webSocketService={webSocketService}
            />
          </TabPane>

          <TabPane tab="系统配置" key="settings">
            <Card title="风控系统配置">
              <Row gutter={[24, 16]}>
                <Col span={12}>
                  <Card size="small" title="检测功能">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>启用风控检测</span>
                        <Switch
                          checked={riskSettings.enable_risk_detection?.value || false}
                          onChange={(checked) => updateRiskSetting('enable_risk_detection', checked, 'boolean')}
                        />
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>启用关键词过滤</span>
                        <Switch
                          checked={riskSettings.enable_keyword_filter?.value || false}
                          onChange={(checked) => updateRiskSetting('enable_keyword_filter', checked, 'boolean')}
                        />
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>启用自动拦截</span>
                        <Switch
                          checked={riskSettings.enable_auto_block?.value || false}
                          onChange={(checked) => updateRiskSetting('enable_auto_block', checked, 'boolean')}
                        />
                      </div>
                    </Space>
                  </Card>
                </Col>

                <Col span={12}>
                  <Card size="small" title="黑白名单">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>启用IP黑名单</span>
                        <Switch
                          checked={riskSettings.enable_ip_blacklist?.value || false}
                          onChange={(checked) => updateRiskSetting('enable_ip_blacklist', checked, 'boolean')}
                        />
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>启用用户黑名单</span>
                        <Switch
                          checked={riskSettings.enable_user_blacklist?.value || false}
                          onChange={(checked) => updateRiskSetting('enable_user_blacklist', checked, 'boolean')}
                        />
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>启用白名单绕过</span>
                        <Switch
                          checked={riskSettings.enable_whitelist_bypass?.value || false}
                          onChange={(checked) => updateRiskSetting('enable_whitelist_bypass', checked, 'boolean')}
                        />
                      </div>
                    </Space>
                  </Card>
                </Col>

                <Col span={12}>
                  <Card size="small" title="批量操作">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>启用批量审核</span>
                        <Switch
                          checked={riskSettings.enable_batch_review?.value || false}
                          onChange={(checked) => updateRiskSetting('enable_batch_review', checked, 'boolean')}
                        />
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>启用批量删除</span>
                        <Switch
                          checked={riskSettings.enable_batch_delete?.value || false}
                          onChange={(checked) => updateRiskSetting('enable_batch_delete', checked, 'boolean')}
                        />
                      </div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <span>启用批量加入黑名单</span>
                        <Switch
                          checked={riskSettings.enable_batch_blacklist?.value || false}
                          onChange={(checked) => updateRiskSetting('enable_batch_blacklist', checked, 'boolean')}
                        />
                      </div>
                    </Space>
                  </Card>
                </Col>

                <Col span={12}>
                  <Card size="small" title="阈值设置">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div>
                        <span>最大风险评分阈值</span>
                        <InputNumber
                          style={{ width: '100%', marginTop: 8 }}
                          min={0}
                          max={100}
                          value={riskSettings.max_risk_score?.value || 80}
                          onChange={(value) => updateRiskSetting('max_risk_score', value, 'number')}
                        />
                      </div>
                      <div>
                        <span>自动拦截阈值</span>
                        <InputNumber
                          style={{ width: '100%', marginTop: 8 }}
                          min={0}
                          max={100}
                          value={riskSettings.auto_block_threshold?.value || 70}
                          onChange={(value) => updateRiskSetting('auto_block_threshold', value, 'number')}
                        />
                      </div>
                      <div>
                        <span>批量操作限制数量</span>
                        <InputNumber
                          style={{ width: '100%', marginTop: 8 }}
                          min={1}
                          max={1000}
                          value={riskSettings.batch_operation_limit?.value || 100}
                          onChange={(value) => updateRiskSetting('batch_operation_limit', value, 'number')}
                        />
                      </div>
                    </Space>
                  </Card>
                </Col>
              </Row>
            </Card>
          </TabPane>
          <TabPane tab="关键词管理" key="keywords">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    keywordForm.resetFields();
                    setKeywordModalVisible(true);
                  }}
                >
                  添加关键词
                </Button>
                <Upload {...uploadProps}>
                  <Button icon={<ImportOutlined />}>
                    批量导入
                  </Button>
                </Upload>
                <Button
                  icon={<ExportOutlined />}
                  onClick={() => exportKeywords('csv')}
                >
                  导出CSV
                </Button>
                <Button
                  icon={<ExportOutlined />}
                  onClick={() => exportKeywords('txt')}
                >
                  导出TXT
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadKeywords}
                >
                  刷新
                </Button>
              </Space>
            </div>
            
            <Table
              columns={keywordColumns}
              dataSource={keywords}
              loading={keywordLoading}
              rowKey="id"
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, size) => {
                  setPagination(prev => ({
                    ...prev,
                    current: page,
                    pageSize: size
                  }));
                }
              }}
            />
          </TabPane>
          
          <TabPane tab="风控记录" key="records">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={loadRecords}
                >
                  刷新
                </Button>
              </Space>
            </div>

            {/* 批量操作工具栏 */}
            {selectedRowKeys.length > 0 && (
              <div style={{
                marginBottom: 16,
                padding: '12px 16px',
                background: '#e6f7ff',
                border: '1px solid #91d5ff',
                borderRadius: '6px',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <span style={{ color: '#1890ff', fontWeight: 500 }}>
                  已选择 {selectedRowKeys.length} 条记录
                </span>
                <Space>
                  <Button
                    type="primary"
                    size="small"
                    loading={batchLoading}
                    onClick={() => handleBatchReview('approved', '批量通过')}
                    disabled={!riskSettings.enable_batch_review?.value}
                  >
                    批量通过
                  </Button>
                  <Button
                    size="small"
                    loading={batchLoading}
                    onClick={() => handleBatchReview('rejected', '批量拒绝')}
                    disabled={!riskSettings.enable_batch_review?.value}
                  >
                    批量拒绝
                  </Button>
                  <Button
                    danger
                    size="small"
                    loading={batchLoading}
                    onClick={handleBatchBlacklist}
                    disabled={!riskSettings.enable_batch_blacklist?.value}
                  >
                    加入黑名单
                  </Button>
                  <Button
                    danger
                    size="small"
                    loading={batchLoading}
                    onClick={handleBatchDelete}
                    disabled={!riskSettings.enable_batch_delete?.value}
                  >
                    批量删除
                  </Button>
                  <Button
                    size="small"
                    onClick={() => setSelectedRowKeys([])}
                  >
                    取消选择
                  </Button>
                </Space>
              </div>
            )}

            <Table
              columns={recordColumns}
              dataSource={records}
              loading={recordLoading}
              rowKey="id"
              rowSelection={{
                selectedRowKeys,
                onChange: setSelectedRowKeys,
                getCheckboxProps: (record) => ({
                  disabled: record.status !== 'pending', // 只有待审核的记录可以批量操作
                }),
              }}
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, size) => {
                  setPagination(prev => ({
                    ...prev,
                    current: page,
                    pageSize: size
                  }));
                }
              }}
            />
          </TabPane>
            <TabPane tab="黑名单管理" key="blacklist">
            <Card>
              <div style={{ marginBottom: 16 }}>
                <Space>
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />} 
                    onClick={() => handleBlacklistAdd()}
                  >
                    添加黑名单
                  </Button>
                  <Button 
                    icon={<ReloadOutlined />} 
                    onClick={() => loadBlacklist()}
                  >
                    刷新
                  </Button>
                </Space>
              </div>
              
              <Table
                columns={blacklistColumns}
                dataSource={blacklist}
                rowKey="id"
                loading={blacklistLoading}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                  onChange: (page, pageSize) => {
                    setPagination(prev => ({ ...prev, current: page, pageSize }));
                  }
                }}
              />
            </Card>
          </TabPane>
          
          <TabPane tab="白名单管理" key="whitelist">
            <Card>
              <div style={{ marginBottom: 16 }}>
                <Space>
                  <Button 
                    type="primary" 
                    icon={<PlusOutlined />} 
                    onClick={() => handleWhitelistAdd()}
                  >
                    添加白名单
                  </Button>
                  <Button 
                    icon={<ReloadOutlined />} 
                    onClick={() => loadWhitelist()}
                  >
                    刷新
                  </Button>
                </Space>
              </div>
              
              <Table
                columns={whitelistColumns}
                dataSource={whitelist}
                rowKey="id"
                loading={whitelistLoading}
                pagination={{
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                  total: pagination.total,
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                  onChange: (page, pageSize) => {
                    setPagination(prev => ({ ...prev, current: page, pageSize }));
                  }
                }}
              />
            </Card>
          </TabPane>
        </Tabs>
      </Card>

      {/* 关键词添加/编辑模态框 */}
      <Modal
        title="添加关键词"
        open={keywordModalVisible}
        onCancel={() => setKeywordModalVisible(false)}
        footer={null}
      >
        <Form
          form={keywordForm}
          layout="vertical"
          onFinish={handleKeywordSubmit}
        >
          <Form.Item
            name="keyword"
            label="关键词"
            rules={[{ required: true, message: '请输入关键词' }]}
          >
            <Input placeholder="输入风控关键词" />
          </Form.Item>
          
          <Form.Item
            name="keyword_type"
            label="匹配类型"
            initialValue="strict"
          >
            <Select>
              <Option value="strict">严格匹配</Option>
              <Option value="fuzzy">模糊匹配</Option>
              <Option value="regex">正则匹配</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="risk_level"
            label="风险等级"
            initialValue="medium"
          >
            <Select>
              <Option value="low">低风险</Option>
              <Option value="medium">中风险</Option>
              <Option value="high">高风险</Option>
              <Option value="critical">严重风险</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={3} placeholder="描述该关键词的用途" />
          </Form.Item>
          
          <Form.Item
            name="is_active"
            label="状态"
            initialValue={true}
          >
            <Select>
              <Option value={true}>启用</Option>
              <Option value={false}>禁用</Option>
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
              <Button onClick={() => setKeywordModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>      {/* 审核模态框 */}
      <Modal
        title="审核风控记录"
        open={reviewModalVisible}
        onCancel={() => setReviewModalVisible(false)}
        footer={null}
      >
        <Form
          form={reviewForm}
          layout="vertical"
          onFinish={handleReviewSubmit}
        >
          <Form.Item
            name="status"
            label="审核结果"
            rules={[{ required: true, message: '请选择审核结果' }]}
          >
            <Select>
              <Option value="approved">通过（加入黑名单）</Option>
              <Option value="rejected">拒绝（误报）</Option>
              <Option value="ignored">忽略</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="review_reason"
            label="审核原因"
          >
            <TextArea rows={3} placeholder="请说明审核原因" />
          </Form.Item>
          
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => 
              prevValues.status !== currentValues.status
            }
          >
            {({ getFieldValue }) =>
              getFieldValue('status') === 'approved' ? (
                <>
                  <Form.Item
                    name="ban_type"
                    label="封禁类型"
                    initialValue="permanent"
                  >
                    <Select>
                      <Option value="permanent">永久封禁</Option>
                      <Option value="temporary">临时封禁</Option>
                    </Select>
                  </Form.Item>
                  
                  {getFieldValue('ban_type') === 'temporary' && (
                    <Form.Item
                      name="ban_duration"
                      label="封禁时长（小时）"
                      rules={[{ required: true, message: '请输入封禁时长' }]}
                    >
                      <Input type="number" placeholder="输入封禁小时数" />
                    </Form.Item>
                  )}
                </>
              ) : null
            }
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交审核
              </Button>
              <Button onClick={() => setReviewModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 黑名单添加/编辑模态框 */}
      <Modal
        title="黑名单管理"
        open={blacklistModalVisible}
        onCancel={() => setBlacklistModalVisible(false)}
        footer={null}
      >
        <Form
          form={blacklistForm}
          layout="vertical"
          onFinish={handleBlacklistSubmit}
        >
          <Form.Item
            name="username"
            label="用户名"
          >
            <Input placeholder="输入用户名" />
          </Form.Item>
          
          <Form.Item
            name="device_id"
            label="设备ID"
          >
            <Input placeholder="输入设备ID" />
          </Form.Item>
          
          <Form.Item
            name="ip_address"
            label="IP地址"
          >
            <Input placeholder="输入IP地址" />
          </Form.Item>
          
          <Form.Item
            name="uin"
            label="UIN"
          >
            <Input placeholder="输入UIN" />
          </Form.Item>
          
          <Form.Item
            name="risk_content"
            label="风险内容"
          >
            <TextArea rows={3} placeholder="输入风险内容" />
          </Form.Item>
          
          <Form.Item
            name="ban_reason"
            label="封禁原因"
            rules={[{ required: true, message: '请输入封禁原因' }]}
          >
            <TextArea rows={3} placeholder="输入封禁原因" />
          </Form.Item>
          
          <Form.Item
            name="ban_type"
            label="封禁类型"
            initialValue="permanent"
          >
            <Select>
              <Option value="permanent">永久封禁</Option>
              <Option value="temporary">临时封禁</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => 
              prevValues.ban_type !== currentValues.ban_type
            }
          >
            {({ getFieldValue }) =>
              getFieldValue('ban_type') === 'temporary' ? (
                <Form.Item
                  name="ban_duration"
                  label="封禁时长（小时）"
                  rules={[{ required: true, message: '请输入封禁时长' }]}
                >
                  <Input type="number" placeholder="输入封禁小时数" />
                </Form.Item>
              ) : null
            }
          </Form.Item>
          
          <Form.Item
            name="is_active"
            label="状态"
            initialValue={true}
          >
            <Select>
              <Option value={true}>生效</Option>
              <Option value={false}>禁用</Option>
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
              <Button onClick={() => setBlacklistModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 白名单添加/编辑模态框 */}
      <Modal
        title="白名单管理"
        open={whitelistModalVisible}
        onCancel={() => setWhitelistModalVisible(false)}
        footer={null}
      >
        <Form
          form={whitelistForm}
          layout="vertical"
          onFinish={handleWhitelistSubmit}
        >
          <Form.Item
            name="username"
            label="用户名"
          >
            <Input placeholder="输入用户名" />
          </Form.Item>
          
          <Form.Item
            name="device_id"
            label="设备ID"
          >
            <Input placeholder="输入设备ID" />
          </Form.Item>
          
          <Form.Item
            name="ip_address"
            label="IP地址"
          >
            <Input placeholder="输入IP地址" />
          </Form.Item>
          
          <Form.Item
            name="uin"
            label="UIN"
          >
            <Input placeholder="输入UIN" />
          </Form.Item>
          
          <Form.Item
            name="whitelist_type"
            label="白名单类型"
            initialValue="comprehensive"
          >
            <Select>
              <Option value="username">用户名</Option>
              <Option value="device">设备</Option>
              <Option value="ip">IP地址</Option>
              <Option value="uin">UIN</Option>
              <Option value="comprehensive">综合</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={3} placeholder="输入白名单描述" />
          </Form.Item>
          
          <Form.Item
            name="is_active"
            label="状态"
            initialValue={true}
          >
            <Select>
              <Option value={true}>生效</Option>
              <Option value={false}>禁用</Option>
            </Select>
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
              <Button onClick={() => setWhitelistModalVisible(false)}>
                取消
              </Button>            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

// 实时监控组件
const RealTimeMonitor = ({ wsConnected, connectedClients, webSocketService }) => {
  const [testContent, setTestContent] = useState('');
  const [testResult, setTestResult] = useState(null);
  const [testing, setTesting] = useState(false);

  // 风险等级颜色映射函数
  const getRiskLevelColor = (level) => {
    const colors = {
      low: 'green',
      medium: 'orange',
      high: 'red',
      critical: 'purple'
    };
    return colors[level] || 'default';
  };

  // 测试风控检测
  const handleTestRisk = async () => {
    if (!wsConnected) {
      NotificationHelper.warning('WebSocket 未连接', 'WebSocket 未连接，无法进行实时检测');
      return;
    }

    if (!testContent.trim()) {
      NotificationHelper.warning('输入内容为空', '请输入要检测的内容');
      return;
    }

    setTesting(true);
    try {
      const result = await webSocketService.detectRisk(testContent, {
        ip: 'test_ip',
        user_agent: navigator.userAgent
      });
      setTestResult(result);
      
      if (result.isRisk) {
        NotificationHelper.warning('风险检测', `检测到风险：${result.riskLevel} 级别，评分 ${result.riskScore}`);
      } else {
        NotificationHelper.operationSuccess('检测完成', '内容安全，未检测到风险');
      }
    } catch (error) {
      NotificationHelper.networkError(500, '检测失败：' + error.message);
    } finally {
      setTesting(false);
    }
  };

  const clientColumns = [
    {
      title: '客户端ID',
      dataIndex: 'client_id',
      key: 'client_id'
    },
    {
      title: '连接时间',
      dataIndex: 'connected_time',
      key: 'connected_time',
      render: (time) => new Date(time).toLocaleString()
    },
    {
      title: 'IP地址',
      dataIndex: 'ip_address',
      key: 'ip_address'
    },
    {
      title: '用户代理',
      dataIndex: 'user_agent',
      key: 'user_agent',
      ellipsis: true
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'orange'}>
          {status === 'active' ? '活跃' : '空闲'}
        </Tag>
      )
    }
  ];

  return (
    <div>
      <Row gutter={16}>
        <Col span={12}>
          <Card title="实时风控检测" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <TextArea
                rows={4}
                placeholder="请输入要检测的内容..."
                value={testContent}
                onChange={(e) => setTestContent(e.target.value)}
              />
              <Button
                type="primary"
                loading={testing}
                onClick={handleTestRisk}
                disabled={!wsConnected}
                block
              >
                {wsConnected ? '开始检测' : 'WebSocket 未连接'}
              </Button>
              
              {testResult && (
                <div style={{ marginTop: 16 }}>
                  <h4>检测结果：</h4>
                  <div>
                    <Tag color={testResult.isRisk ? 'red' : 'green'}>
                      {testResult.isRisk ? '存在风险' : '内容安全'}
                    </Tag>
                    {testResult.isRisk && (
                      <>
                        <Tag color={getRiskLevelColor(testResult.riskLevel)}>
                          {testResult.riskLevel} 级别
                        </Tag>
                        <Tag>评分: {testResult.riskScore}</Tag>
                        {testResult.autoBlock && <Tag color="red">自动拦截</Tag>}
                      </>
                    )}
                  </div>
                  
                  {testResult.matchedKeywords && testResult.matchedKeywords.length > 0 && (
                    <div style={{ marginTop: 8 }}>
                      <strong>匹配关键词：</strong>
                      <div style={{ marginTop: 4 }}>
                        {testResult.matchedKeywords.map((kw, index) => (
                          <Tag key={index} color={getRiskLevelColor(kw.risk_level)}>
                            {kw.keyword} ({kw.risk_level})
                          </Tag>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </Space>
          </Card>
        </Col>
        
        <Col span={12}>
          <Card title="连接状态" size="small">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Badge 
                  status={wsConnected ? 'success' : 'error'} 
                  text={wsConnected ? 'WebSocket 已连接' : 'WebSocket 未连接'}
                />
                <Tag color={wsConnected ? 'green' : 'red'}>
                  {connectedClients.length} 个客户端在线
                </Tag>
              </Space>
            </div>
            
            <Table
              size="small"
              columns={clientColumns}
              dataSource={connectedClients}
              rowKey="client_id"
              pagination={false}
              scroll={{ y: 200 }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default RiskControl;
