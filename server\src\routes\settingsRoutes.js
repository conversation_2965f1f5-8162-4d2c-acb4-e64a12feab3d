const express = require('express');
const router = express.Router();
const mysqlModel = require('../models/mysql-real-model');
const fs = require('fs');
const path = require('path');

// 获取系统设置
router.get('/settings', async (req, res) => {
    try {
        // 从数据库获取系统设置
        const settings = {
            authKey: await mysqlModel.getSetting('auth_key', '1145141919810'),
            serverPort: await mysqlModel.getSetting('server_port', 8080),
            logLevel: await mysqlModel.getSetting('log_level', 'info'),
            enableAuth: await mysqlModel.getSetting('enable_auth', true),
            enableLogging: await mysqlModel.getSetting('enable_logging', true),
            maxLogSize: await mysqlModel.getSetting('max_log_size', 100),
            autoCleanLogs: await mysqlModel.getSetting('auto_clean_logs', true),
            cleanLogsDays: await mysqlModel.getSetting('clean_logs_days', 30),
            maxRequestsPerMinute: await mysqlModel.getSetting('max_requests_per_minute', 60),
            enableRateLimit: await mysqlModel.getSetting('enable_rate_limit', true)
            // 管理员账户管理和通知设置已移至用户管理功能
        };

        // 读取 websocketEnabled
        const configPath = path.join(__dirname, '../../database/config.json');
        let websocketEnabled = false;
        try {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
            websocketEnabled = !!(config.websocket && config.websocket.enabled);
        } catch (e) {}
        settings.websocketEnabled = websocketEnabled;

        res.success(settings, '获取设置成功');
    } catch (error) {
        console.error('获取设置失败:', error);
        res.error('获取设置失败', 500);
    }
});

// 保存系统设置
router.post('/settings', async (req, res) => {
    try {
        const settings = req.body;
        // 保存到数据库
        await mysqlModel.setSetting('auth_key', settings.authKey, 'string', '系统认证密钥');
        await mysqlModel.setSetting('server_port', settings.serverPort, 'number', '服务器端口');
        await mysqlModel.setSetting('log_level', settings.logLevel, 'string', '日志级别');
        await mysqlModel.setSetting('enable_auth', settings.enableAuth, 'boolean', '是否启用认证');
        await mysqlModel.setSetting('enable_logging', settings.enableLogging, 'boolean', '是否启用日志记录');
        await mysqlModel.setSetting('max_log_size', settings.maxLogSize, 'number', '最大日志大小(MB)');
        await mysqlModel.setSetting('auto_clean_logs', settings.autoCleanLogs, 'boolean', '是否自动清理日志');
        await mysqlModel.setSetting('clean_logs_days', settings.cleanLogsDays, 'number', '日志保留天数');
        await mysqlModel.setSetting('max_requests_per_minute', settings.maxRequestsPerMinute, 'number', '每分钟最大请求数');
        await mysqlModel.setSetting('enable_rate_limit', settings.enableRateLimit, 'boolean', '是否启用频率限制');
        // 新增 websocketEnabled 配置
        if (typeof settings.websocketEnabled !== 'undefined') {
            // 写入 config.json
            const configPath = path.join(__dirname, '../../database/config.json');
            const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
            config.websocket = config.websocket || {};
            config.websocket.enabled = !!settings.websocketEnabled;
            fs.writeFileSync(configPath, JSON.stringify(config, null, 2), 'utf-8');
        }
        // 管理员账户管理和通知设置已移至用户管理功能
        console.log('系统设置已更新:', settings);
        res.success(null, '设置保存成功~ ✨');
    } catch (error) {
        console.error('保存设置失败:', error);
        res.error('保存设置失败', 500);
    }
});

// 获取风控规则
router.get('/settings/risk-rules', async (req, res) => {
    try {
        const rules = await mysqlModel.getRiskRules();
        
        res.success(rules, '获取风控规则成功');
    } catch (error) {
        console.error('获取风控规则失败:', error);
        res.error('获取风控规则失败', 500);
    }
});

// 更新风控规则
router.put('/settings/risk-rules', async (req, res) => {
    try {
        const rules = req.body;
        const success = await mysqlModel.updateRiskRules(rules);
        
        if (success) {
            res.success(null, '风控规则更新成功');
        } else {
            res.error('风控规则更新失败', 500);
        }
    } catch (error) {
        console.error('更新风控规则失败:', error);
        res.error('更新风控规则失败', 500);
    }
});

// 获取黑名单
router.get('/settings/blacklist', async (req, res) => {
    try {
        const { page = 1, pageSize = 10, type, value } = req.query;
        
        const filters = {};
        if (type) filters.type = type;
        if (value) filters.value = value;
        
        const result = await mysqlModel.getBlacklist(
            parseInt(page), 
            parseInt(pageSize), 
            filters
        );
        
        res.success(result, '获取黑名单成功');
    } catch (error) {
        console.error('获取黑名单失败:', error);
        res.error('获取黑名单失败', 500);
    }
});

// 添加到黑名单
router.post('/settings/blacklist', async (req, res) => {
    try {
        const { type, value, reason } = req.body;
        
        if (!type || !value) {
            return res.error('类型和值不能为空', 400);
        }

        if (!['ip', 'uin', 'user_agent'].includes(type)) {
            return res.error('类型必须是 ip、uin 或 user_agent', 400);
        }
        
        const success = await mysqlModel.addToBlacklist(type, value, reason || '手动添加', 'admin');
        
        if (success) {
            res.success(null, '添加到黑名单成功');
        } else {
            res.error('添加到黑名单失败', 500);
        }
    } catch (error) {
        console.error('添加到黑名单失败:', error);
        res.error('添加到黑名单失败', 500);
    }
});

// 从黑名单移除
router.delete('/settings/blacklist/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        const success = await mysqlModel.removeFromBlacklist(parseInt(id));
        
        if (success) {
            res.success(null, '从黑名单移除成功');
        } else {
            res.error('记录不存在或移除失败', 404);
        }
    } catch (error) {
        console.error('从黑名单移除失败:', error);
        res.error('从黑名单移除失败', 500);
    }
});

// 获取管理员凭据
router.get('/settings/admin-credentials', async (req, res) => {
    try {
        const credentials = await mysqlModel.getAdminCredentials();
        res.json({
            code: 0,
            msg: '获取管理员凭据成功',
            data: credentials
        });
    } catch (error) {
        console.error('获取管理员凭据失败:', error);
        res.status(500).json({
            code: 500,
            msg: '获取管理员凭据失败'
        });
    }
});

// 更新管理员凭据
router.post('/settings/admin-credentials', async (req, res) => {
    try {
        const { username, password } = req.body;
        
        if (!username || !password) {
            return res.status(400).json({
                code: 400,
                msg: '用户名和密码不能为空'
            });
        }

        const success = await mysqlModel.updateAdminCredentials(username, password);
        
        if (success) {
            res.json({
                code: 0,
                msg: '管理员凭据更新成功~ ✨'
            });
        } else {
            res.status(500).json({
                code: 500,
                msg: '管理员凭据更新失败'
            });
        }
    } catch (error) {
        console.error('更新管理员凭据失败:', error);
        res.status(500).json({
            code: 500,
            msg: '更新管理员凭据失败'
        });
    }
});

module.exports = router;
