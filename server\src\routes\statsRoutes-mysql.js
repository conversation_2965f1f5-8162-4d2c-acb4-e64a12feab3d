const express = require('express');
const mysqlModel = require('../models/mysql-real-model');

const router = express.Router();

// 获取总体统计概览
router.get('/stats/overview', async (req, res) => {
    try {
        const overview = await mysqlModel.getOverviewStats();

        res.json({
            code: 0,
            msg: '查询成功',
            data: overview
        });
    } catch (error) {
        console.error('获取总体统计失败:', error);
        return res.status(500).json({
            code: 500,
            msg: '查询失败',
            error: error.message
        });
    }
});


// 获取每日统计
router.get('/stats/daily', async (req, res) => {
    try {
        const days = parseInt(req.query.days) || 7;
        const dailyStats = await mysqlModel.getDailyStats(days);
        
        res.json({
            code: 0,
            msg: '查询成功',
            data: dailyStats
        });
    } catch (error) {
        console.error('获取每日统计失败:', error);
        return res.status(500).json({ 
            code: 500, 
            msg: '查询失败', 
            error: error.message 
        });
    }
});

// 获取类型统计
router.get('/stats/types', async (req, res) => {
    try {
        const typeStats = await mysqlModel.getTypeStats();
        
        res.json({
            code: 0,
            msg: '查询成功',
            data: typeStats
        });
    } catch (error) {
        console.error('获取类型统计失败:', error);
        return res.status(500).json({ 
            code: 500, 
            msg: '查询失败', 
            error: error.message 
        });
    }
});

// 获取端点统计
router.get('/stats/endpoints', async (req, res) => {
    try {
        const endpointStats = await mysqlModel.getEndpointStats();
        
        res.json({
            code: 0,
            msg: '查询成功',
            data: endpointStats
        });
    } catch (error) {
        console.error('获取端点统计失败:', error);
        return res.status(500).json({ 
            code: 500, 
            msg: '查询失败', 
            error: error.message 
        });
    }
});

// 获取IP统计
router.get('/stats/ips', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 20;
          const ipStats = await mysqlModel.db.query(`
            SELECT 
                ip,
                COUNT(*) as total_requests,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests,
                MAX(request_time) as last_request_time
            FROM sign_logs 
            GROUP BY ip
            ORDER BY total_requests DESC
            LIMIT ?
        `, [limit]);
        
        res.json({
            code: 0,
            msg: '查询成功',
            data: ipStats
        });
    } catch (error) {
        console.error('获取IP统计失败:', error);
        return res.status(500).json({ 
            code: 500, 
            msg: '查询失败', 
            error: error.message 
        });
    }
});

// 获取UIN统计
router.get('/stats/uins', async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 20;
        const uinStats = await mysqlModel.getUINStats(limit);

        res.json({
            code: 0,
            msg: '查询成功',
            data: uinStats
        });
    } catch (error) {
        console.error('获取UIN统计失败:', error);
        return res.status(500).json({
            code: 500,
            msg: '查询失败',
            error: error.message
        });
    }
});

// 获取小时统计（今日）
router.get('/stats/hourly', async (req, res) => {
    try {
        const hourlyStats = await mysqlModel.getHourlyStats();
        
        res.json({
            code: 0,
            msg: '查询成功',
            data: hourlyStats
        });
    } catch (error) {
        console.error('获取小时统计失败:', error);
        return res.status(500).json({ 
            code: 500, 
            msg: '查询失败', 
            error: error.message 
        });
    }
});

// 获取响应时间统计
router.get('/stats/response-time', async (req, res) => {
    try {        const responseTimeStats = await mysqlModel.db.query(`
            SELECT 
                endpoint,
                AVG(response_time_ms) as avg_response_time,
                MIN(response_time_ms) as min_response_time,
                MAX(response_time_ms) as max_response_time,
                COUNT(*) as total_requests
            FROM sign_logs 
            WHERE response_time_ms > 0
            GROUP BY endpoint
            ORDER BY avg_response_time DESC
        `);
        
        res.json({
            code: 0,
            msg: '查询成功',
            data: responseTimeStats
        });
    } catch (error) {
        console.error('获取响应时间统计失败:', error);
        return res.status(500).json({ 
            code: 500, 
            msg: '查询失败', 
            error: error.message 
        });
    }
});

// 获取错误统计
router.get('/stats/errors', async (req, res) => {
    try {        const errorStats = await mysqlModel.db.query(`
            SELECT 
                error_msg,
                COUNT(*) as error_count,
                MAX(request_time) as last_occurrence
            FROM sign_logs 
            WHERE success = 0 AND error_msg IS NOT NULL AND error_msg != ''
            GROUP BY error_msg
            ORDER BY error_count DESC
            LIMIT 20
        `);
        
        res.json({
            code: 0,
            msg: '查询成功',
            data: errorStats
        });
    } catch (error) {
        console.error('获取错误统计失败:', error);
        return res.status(500).json({ 
            code: 500, 
            msg: '查询失败', 
            error: error.message 
        });
    }
});

module.exports = router;
