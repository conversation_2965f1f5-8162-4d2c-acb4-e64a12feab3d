/**
 * 通知助手 - SweetAlert2 版本
 * 提供统一的错误处理和用户友好的提示信息
 * 使用原生 SweetAlert2 弹窗系统
 */
import Swal from 'sweetalert2';

class NotificationHelper {
  static isInitialized = false;

  /**
   * 初始化通知配置
   */
  static init() {
    if (this.isInitialized) return;
    
    this.isInitialized = true;
    console.log('NotificationHelper初始化完成');
  }  /**
   * 成功提示
   */
  static success(title, description = '', config = {}) {
    this.init();
    console.log('成功通知:', title, description);
    
    return Swal.fire({
      title: title,
      text: description,
      icon: 'success',
      position: config.position || 'top-end',
      timer: config.duration || 4000,
      timerProgressBar: true,
      toast: config.toast !== false,
      showConfirmButton: false,
      showCloseButton: true,
      didOpen: (toast) => {
        if (config.toast !== false) {
          toast.addEventListener('mouseenter', Swal.stopTimer);
          toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
      }
    });
  }  /**
   * 错误提示
   */
  static error(title, description = '', config = {}) {
    this.init();
    console.log('错误通知:', title, description);
    
    // 如果是重要错误（有详细描述），显示为弹窗而不是toast
    const isImportantError = description && description.length > 0;
    
    return Swal.fire({
      title: title,
      text: description,
      icon: 'error',
      position: isImportantError ? 'center' : (config.position || 'top-end'),
      timer: isImportantError ? undefined : (config.duration || 5000),
      timerProgressBar: !isImportantError,
      toast: !isImportantError && (config.toast !== false),
      showConfirmButton: isImportantError,
      showCloseButton: !isImportantError,
      confirmButtonText: isImportantError ? '知道了' : undefined,
      allowOutsideClick: true,
      allowEscapeKey: true,
      focusConfirm: isImportantError,
      didOpen: (toast) => {
        if (!isImportantError && config.toast !== false) {
          toast.addEventListener('mouseenter', Swal.stopTimer);
          toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
      }
    });
  }
  /**
   * 警告提示
   */
  static warning(title, description = '', config = {}) {
    this.init();
    console.log('警告通知:', title, description);
    
    return Swal.fire({
      title: title,
      text: description,
      icon: 'warning',
      position: config.position || 'top-end',
      timer: config.duration || 4500,
      timerProgressBar: true,
      toast: config.toast !== false,
      showConfirmButton: false,
      didOpen: (toast) => {
        if (config.toast !== false) {
          toast.addEventListener('mouseenter', Swal.stopTimer);
          toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
      }
    });
  }

  /**
   * 信息提示
   */
  static info(title, description = '', config = {}) {
    this.init();
    console.log('信息通知:', title, description);
    
    return Swal.fire({
      title: title,
      text: description,
      icon: 'info',
      position: config.position || 'top-end',
      timer: config.duration || 4000,
      timerProgressBar: true,
      toast: config.toast !== false,
      showConfirmButton: false,
      didOpen: (toast) => {
        if (config.toast !== false) {
          toast.addEventListener('mouseenter', Swal.stopTimer);
          toast.addEventListener('mouseleave', Swal.resumeTimer);
        }
      }
    });
  }  /**
   * 2FA相关错误提示
   */
  static twoFactorError(type, details = '') {
    this.init();
    console.log('2FA错误通知:', type, details);
    
    const messages = {
      'invalid_code': {
        title: '验证码错误',
        description: '请检查您的身份验证器应用中的最新6位数字代码'
      },
      'format_error': {
        title: '格式错误',
        description: '验证码必须是6位数字，请重新输入'
      },
      'format': {
        title: '格式错误',
        description: details || '验证码必须是6位数字，请重新输入'
      },
      'verify': {
        title: '验证失败',
        description: details || '验证码错误，请检查您的身份验证器中的最新代码'
      },
      'input': {
        title: '输入错误',
        description: details || '请输入验证码'
      },
      'general': {
        title: '操作失败',
        description: details || '操作失败，请重试'
      },
      'not_bound': {
        title: '未绑定2FA',
        description: '您的账户还没有绑定二步验证，请先绑定后再试'
      },
      'time_sync': {
        title: '时间同步问题',
        description: '请确保设备时间与服务器时间同步'
      }
    };

    const messageConfig = messages[type] || {
      title: '验证失败',
      description: details || '未知错误，请重试'
    };

    return Swal.fire({
      title: messageConfig.title,
      text: messageConfig.description,
      icon: 'error',
      position: 'center',
      showConfirmButton: true,
      confirmButtonText: '知道了',
      allowOutsideClick: true,
      allowEscapeKey: true,
      focusConfirm: true
    });
  }
  /**
   * 网络错误提示
   */
  static networkError(statusCode, customMessage = '') {
    this.init();
    console.log('网络错误通知:', statusCode, customMessage);
    
    const messages = {
      400: {
        title: '请求错误',
        description: '请求参数有误，请检查输入内容'
      },
      401: {
        title: '认证失败',
        description: '用户名、密码或验证码错误，请重新输入'
      },
      403: {
        title: '权限不足',
        description: '没有权限执行此操作，请联系管理员'
      },
      404: {
        title: '未找到',
        description: '请求的资源不存在，请检查操作'
      },
      429: {
        title: '请求过频',
        description: '请求太频繁，请稍后再试'
      },
      500: {
        title: '服务器错误',
        description: '服务器出错，请稍后重试或联系管理员'
      },
      502: {
        title: '网关错误',
        description: '服务器网关错误，请稍后重试'
      },
      503: {
        title: '服务不可用',
        description: '服务暂时不可用，请稍后重试'
      }
    };

    const messageConfig = messages[statusCode] || {
      title: '网络错误',
      description: customMessage || `网络错误 (状态码: ${statusCode})，请检查网络连接`
    };

    return Swal.fire({
      title: messageConfig.title,
      text: customMessage || messageConfig.description,
      icon: 'error',
      confirmButtonText: '知道了',
      showConfirmButton: true,
      allowOutsideClick: true,
      allowEscapeKey: true,
      focusConfirm: true
    });
  }  /**
   * 操作成功提示
   */
  static operationSuccess(operation, details = '') {
    this.init();
    console.log('操作成功通知:', operation, details);
    
    const operations = {
      'save': '保存成功',
      'update': '更新成功',
      'delete': '删除成功',
      'create': '创建成功',
      'upload': '上传成功',
      'download': '下载成功',
      'import': '导入成功',
      'export': '导出成功',
      'bind': '绑定成功',
      'unbind': '解绑成功'
    };

    const title = operations[operation] || operation;
    const description = details || '操作成功完成';

    return this.success(title, description);
  }

  /**
   * 登录相关提示
   */
  static loginNotification(type, details = '') {
    this.init();
    console.log('登录通知:', type, details);
    
    const messages = {
      'success': {
        type: 'success',
        title: '登录成功',
        description: '欢迎回来，进入QSignHook管理控制台'
      },
      'logout': {
        type: 'info',
        title: '登出成功',
        description: '已安全登出，期待您的下次访问'
      },
      'need_2fa': {
        type: 'info',
        title: '需要二步验证',
        description: '请输入您的二步验证码以完成登录'
      },
      'invalid_credentials': {
        type: 'error',
        title: '登录失败',
        description: '用户名或密码错误，请重新输入'
      }
    };

    const messageConfig = messages[type];
    if (messageConfig) {
      const method = messageConfig.type;
      return this[method](messageConfig.title, details || messageConfig.description);
    }
  }

  /**
   * 测试所有通知类型
   */
  static testAll() {
    this.init();
    console.log('开始通知测试...');
    
    const tests = [
      () => this.success('测试成功通知', '这是一个成功通知测试'),
      () => this.error('测试错误通知', '这是一个错误通知测试'),
      () => this.warning('测试警告通知', '这是一个警告通知测试'),
      () => this.info('测试信息通知', '这是一个信息通知测试'),
      () => this.twoFactorError('invalid_code', '这是一个2FA错误测试'),
      () => this.networkError(401, '这是一个网络错误测试'),
      () => this.operationSuccess('bind', '这是一个操作成功测试'),
    ];

    tests.forEach((test, index) => {
      setTimeout(test, index * 1000);
    });
    
    console.log('通知测试已排队，请观察效果');
  }

  /**
   * 强制显示通知（用于调试）
   */
  static forceShow(type, title, description = '', config = {}) {
    this.init();
    console.log('强制显示通知:', type, title, description);
    
    const method = this[type] || this.info;
    return method.call(this, title, description, config);
  }

  /**
   * 清除所有通知
   */
  static clearAll() {
    console.log('已清除所有通知');
    Swal.close();
  }

  /**
   * 确认对话框
   */
  static confirm(title, description = '', config = {}) {
    this.init();
    console.log('确认对话框:', title, description);
    
    return Swal.fire({
      title: title,
      text: description,
      icon: 'question',
      position: 'center',
      showCancelButton: true,
      confirmButtonText: config.confirmText || '确定',
      cancelButtonText: config.cancelText || '取消'
    });
  }
}

// 初始化通知配置
NotificationHelper.init();

export default NotificationHelper;
