/* 二次元萌系主题样式 */
@import './styles/anime.css';

.App {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
}

/* 全局通知样式 - 确保在所有页面都能正确显示 */
.ant-notification {
  z-index: 100000 !important;
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  pointer-events: auto !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.ant-notification-notice {
  z-index: 100000 !important;
  border-radius: 12px !important;
  border: 2px solid !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(10px) !important;
  width: 380px !important;
  max-width: 90vw !important;
  pointer-events: auto !important;
  background: white !important;
  color: #333 !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.ant-notification-notice-content {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.ant-notification-notice-message {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  color: #333 !important;
  font-weight: 600 !important;
}

.ant-notification-notice-description {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  color: #666 !important;
}

.ant-message {
  z-index: 99999 !important;
  position: fixed !important;
  top: 80px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  pointer-events: auto !important;
}

.ant-message-notice {
  z-index: 99999 !important;
  border-radius: 12px !important;
  border: 2px solid rgba(24, 144, 255, 0.3) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  pointer-events: auto !important;
}

/* 确保通知不被其他元素遮挡 */
body .ant-notification,
body .ant-message {
  z-index: 100000 !important;
  position: fixed !important;
  pointer-events: auto !important;
}

/* 自定义 Ant Design 组件样式 */
.ant-layout {
  background: transparent !important;
}

.ant-layout-sider {
  background: linear-gradient(180deg, #2d3436 0%, #636e72 100%) !important;
  box-shadow: 4px 0 12px rgba(0, 0, 0, 0.1) !important;
}

.ant-layout-header {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1) !important;
  border-bottom: 2px solid rgba(255, 182, 193, 0.3) !important;
}

.ant-menu-dark {
  background: transparent !important;
}

.ant-menu-dark .ant-menu-item {
  border-radius: 10px !important;
  margin: 4px 8px !important;
  transition: all 0.3s ease !important;
  color: rgba(255, 255, 255, 0.8) !important;
}

.ant-menu-dark .ant-menu-item:hover {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
  transform: translateX(5px) !important;
  color: white !important;
}

.ant-menu-dark .ant-menu-item-selected {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%) !important;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.4) !important;
  color: white !important;
}

.ant-menu-dark .ant-menu-item-selected::after {
  display: none !important;
}

/* 内容区域样式 */
.main-content {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 20px !important;
  box-shadow: 0 8px 32px rgba(255, 107, 157, 0.3) !important;
  border: 2px solid rgba(255, 182, 193, 0.3) !important;
  position: relative !important;
  overflow: hidden !important;
}

.main-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 50%, #a8edea 100%);
  z-index: 1;
}

/* 侧边栏 Logo 样式 */
.sidebar-logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 182, 193, 0.3) !important;
  border-radius: 10px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  color: white !important;
  font-weight: bold !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.sidebar-logo::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.8s;
}

.sidebar-logo:hover::before {
  left: 100%;
}

.sidebar-logo:hover {
  background: rgba(255, 182, 193, 0.5) !important;
  transform: scale(1.05) !important;
}

/* 头部用户信息样式 */
.header-user-info {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  padding: 8px 16px !important;
  border-radius: 20px !important;
  background: rgba(255, 182, 193, 0.1) !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
}

.header-user-info:hover {
  background: rgba(255, 182, 193, 0.2) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3) !important;
}

.header-title {
  font-size: 18px !important;
  font-weight: bold !important;
  color: #6b46c1 !important;
  background: linear-gradient(135deg, #6b46c1 0%, #8b5cf6 100%) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
}

/* 加载动画 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 18px;
  font-weight: 500;
  flex-direction: column;
  gap: 16px;
}

.loading-emoji {
  font-size: 48px;
  animation: bounce 1.5s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

/* 签名日志页面表格样式 */
.table-row-light {
  background-color: #fafafa !important;
}

.table-row-dark {
  background-color: #ffffff !important;
}

.table-row-light:hover,
.table-row-dark:hover {
  background-color: #e6f7ff !important;
  transform: translateY(-1px);
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
}

/* 签名日志页面统计卡片动画 */
@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.ant-statistic .ant-statistic-content {
  animation: float 3s ease-in-out infinite;
}

/* 签名日志页面按钮样式 */
.sign-logs-button {
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.sign-logs-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 签名日志页面标签样式 */
.sign-type-tag {
  font-weight: bold !important;
  border-radius: 8px !important;
  padding: 4px 8px !important;
  border: 2px solid !important;
  transition: all 0.2s ease !important;
}

.sign-type-tag:hover {
  transform: scale(1.05) !important;
}

/* 签名日志页面详情模态框样式 */
.ant-descriptions-bordered .ant-descriptions-item-label {
  background: #f8f9fa !important;
  font-weight: bold !important;
  color: #2c3e50 !important;
}

.ant-descriptions-bordered .ant-descriptions-item-content {
  background: #fff !important;
  color: #34495e !important;
}

/* 渐变背景动画 */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.gradient-animated {
  background-size: 200% 200% !important;
  animation: gradientShift 4s ease infinite !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    margin: 8px !important;
    border-radius: 15px !important;
  }

  .sidebar-logo {
    margin: 12px !important;
    height: 28px !important;
    font-size: 12px !important;
  }

  .header-title {
    font-size: 16px !important;
  }
}
