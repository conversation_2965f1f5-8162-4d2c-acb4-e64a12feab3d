import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  InputNumber,
  Select,
  Divider,
  Row,
  Col,
  Alert,
  Space,
  Typography,
  Spin,
  notification,
  Modal,
  Table,
  Popconfirm,
  message,
  Tag
} from 'antd';
import NotificationHelper from '../components/NotificationHelper';
import { testNotifications } from '../utils/testNotification';
import { normalizeResponse, isResponseSuccess, getResponseData, getResponseMessage } from '../utils/responseHelper';
import {
  SettingOutlined,
  SecurityScanOutlined,
  DatabaseOutlined,
  BellOutlined,
  SaveOutlined,
  ReloadOutlined,
  UserOutlined,
  LockOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SafetyCertificateTwoTone,
  QrcodeOutlined,
  SmileTwoTone,
  LockTwoTone,
  UnlockTwoTone,
  PlusOutlined,
  KeyOutlined,
  DeleteOutlined,
  EditOutlined
} from '@ant-design/icons';
import { settingsAPI, configAPI } from '../services/api';
import axios from 'axios';
import api from '../services/api';

const { Option } = Select;
const { Title, Paragraph, Text } = Typography;

const TwoFASection = () => {
  const [loading, setLoading] = useState(true);
  const [enabled, setEnabled] = useState(false);
  const [secret, setSecret] = useState('');
  const [qr, setQr] = useState('');
  const [step, setStep] = useState('status'); // status | bind | verify | unbind
  const [inputCode, setInputCode] = useState('');
  const [genSecret, setGenSecret] = useState('');
  const [genQr, setGenQr] = useState('');
  const [modal, contextHolder] = Modal.useModal();

  // 获取2FA状态
  const fetchStatus = async () => {
    setLoading(true);
    try {
      const res = await api.get('/auth/2fa/status');
      const normalizedResponse = normalizeResponse(res);
      
      if (isResponseSuccess(normalizedResponse)) {
        const data = getResponseData(normalizedResponse);
        setEnabled(data.enabled);
        setSecret(data.secret || '');
        setStep('status');
      } else {
        NotificationHelper.error('获取失败', getResponseMessage(normalizedResponse));
      }
    } catch {
      NotificationHelper.networkError(500, '获取2FA状态失败');
    }
    setLoading(false);
  };

  useEffect(() => { fetchStatus(); }, []);

  // 生成二维码
  const handleGenQr = async () => {
    setLoading(true);
    try {
      const res = await api.get('/auth/2fa/generate');
      const normalizedResponse = normalizeResponse(res);
      
      if (isResponseSuccess(normalizedResponse)) {
        const data = getResponseData(normalizedResponse);
        setGenSecret(data.secret);
        setGenQr(data.qr);
        setStep('bind');
      } else {
        NotificationHelper.twoFactorError(getResponseMessage(normalizedResponse));
      }
    } catch {
      NotificationHelper.networkError(500, '生成二维码失败');
    }
    setLoading(false);
  };

  // 绑定2FA
  const handleBind = async () => {
    if (!inputCode) {
      NotificationHelper.twoFactorError('input', '请输入验证码');
      return;
    }
    if (inputCode.length !== 6) {
      NotificationHelper.twoFactorError('format', '验证码必须是6位数字');
      return;
    }
    if (!/^\d{6}$/.test(inputCode)) {
      NotificationHelper.twoFactorError('format', '验证码只能包含数字');
      return;
    }

    setLoading(true);
    try {
      const res = await api.post('/auth/2fa/bind', { secret: genSecret, code: inputCode });
      if (res.code === 0) {
        NotificationHelper.operationSuccess('bind', '2FA绑定成功，账户安全性已提升！');
        setInputCode('');
        fetchStatus();
      } else {
        // 根据错误类型提供详细提示
        if (res.code === 401) {
          NotificationHelper.twoFactorError('verify', '验证码错误，请检查身份验证器中的最新6位数字代码');
          setInputCode(''); // 清空让用户重新输入
        } else if (res.code === 400) {
          NotificationHelper.twoFactorError('format', res.msg || '参数格式错误');
        } else {
          NotificationHelper.twoFactorError('general', res.msg || '绑定失败，请重试');
        }
      }
    } catch (e) {
      console.error('2FA绑定错误:', e);
      if (e.response && e.response.status === 401) {
        NotificationHelper.twoFactorError('verify', '验证码错误，请确保使用身份验证器中的最新代码');
        setInputCode('');
      } else {
        NotificationHelper.networkError(e.response?.status || 500, '网络错误或服务器异常，请稍后重试');
      }
    }
    setLoading(false);
  };

  // 解绑2FA
  const handleUnbind = async () => {
    if (!inputCode) {
      NotificationHelper.twoFactorError('input', '请输入验证码');
      return;
    }
    if (inputCode.length !== 6) {
      NotificationHelper.twoFactorError('format', '验证码必须是6位数字');
      return;
    }
    if (!/^\d{6}$/.test(inputCode)) {
      NotificationHelper.twoFactorError('format', '验证码只能包含数字');
      return;
    }

    setLoading(true);
    try {
      const res = await api.post('/auth/2fa/unbind', { code: inputCode });
      if (res.code === 0) {
        NotificationHelper.operationSuccess('unbind', '2FA已解绑，喵！');
        setInputCode('');
        fetchStatus();
      } else {
        // 根据错误类型提供详细提示
        if (res.code === 401) {
          NotificationHelper.twoFactorError('verify', '验证码错误，请检查身份验证器中的最新6位数字代码');
          setInputCode(''); // 清空让用户重新输入
        } else if (res.code === 400) {
          NotificationHelper.twoFactorError('format', res.msg || '参数格式错误');
        } else {
          NotificationHelper.twoFactorError('general', res.msg || '解绑失败，请重试');
        }
      }
    } catch (e) {
      console.error('2FA解绑错误:', e);
      if (e.response && e.response.status === 401) {
        NotificationHelper.twoFactorError('verify', '验证码错误，请确保使用身份验证器中的最新代码');
        setInputCode('');
      } else {
        NotificationHelper.networkError(e.response?.status || 500, '网络错误或服务器异常，请稍后重试');
      }
    }
    setLoading(false);
  };

  // 萌系UI
  return (
    <Card className="peach-card" style={{ marginTop: 32, borderRadius: 20, background: 'linear-gradient(135deg, #e3f0ff 0%, #f0f8ff 100%)', boxShadow: '0 8px 32px rgba(108,160,220,0.10)' }}>
      {contextHolder}
      <div style={{ textAlign: 'center', marginBottom: 12 }}>
        <SafetyCertificateTwoTone twoToneColor={["#6ca0dc", "#e3f0ff"]} style={{ fontSize: 36 }} />
        <Title level={4} style={{ color: '#6ca0dc', margin: 0, fontWeight: 700 }}>二次元萌系二步验证</Title>
        <Paragraph style={{ color: '#6ca0dc', margin: 0, fontSize: 15 }}>守护你的账号安全喵~</Paragraph>
      </div>
      <Spin spinning={loading}>
        {step === 'status' && (
          <div style={{ textAlign: 'center', margin: 16 }}>
            {enabled ? (
              <>
                <SmileTwoTone twoToneColor="#6ca0dc" style={{ fontSize: 32 }} />
                <Paragraph style={{ color: '#6ca0dc', margin: 8 }}>已绑定2FA，当前密钥：<Text copyable>{secret}</Text></Paragraph>
                <Space>
                  <Button type="primary" shape="round" icon={<UnlockTwoTone twoToneColor="#6ca0dc" />} onClick={() => setStep('unbind')} style={{ background: 'linear-gradient(135deg, #e3f0ff 0%, #6ca0dc 100%)', border: 'none', marginTop: 8 }}>解绑2FA</Button>
                  <Button 
                    type="dashed" 
                    shape="round" 
                    icon={<BellOutlined />} 
                    onClick={testNotifications}
                    style={{ marginTop: 8 }}
                  >
                    测试通知
                  </Button>
                </Space>
              </>
            ) : (
              <>
                <LockTwoTone twoToneColor="#6ca0dc" style={{ fontSize: 32 }} />
                <Paragraph style={{ color: '#6ca0dc', margin: 8 }}>你还没有绑定2FA哦，快来保护账号安全吧！</Paragraph>
                <Space>
                  <Button type="primary" shape="round" icon={<QrcodeOutlined />} onClick={handleGenQr} style={{ background: 'linear-gradient(135deg, #e3f0ff 0%, #6ca0dc 100%)', border: 'none', marginTop: 8 }}>绑定2FA</Button>
                  <Button 
                    type="dashed" 
                    shape="round" 
                    icon={<BellOutlined />} 
                    onClick={testNotifications}
                    style={{ marginTop: 8 }}
                  >
                    测试通知
                  </Button>
                </Space>
              </>
            )}
          </div>
        )}
        {step === 'bind' && (
          <div style={{ textAlign: 'center', margin: 16 }}>
            <Paragraph style={{ color: '#6ca0dc', fontWeight: 500 }}>用二次元App（如Google Authenticator）扫码绑定喵~</Paragraph>
            <div style={{ display: 'flex', justifyContent: 'center', margin: 12 }}>
              <img src={genQr} alt="2FA二维码" style={{ width: 160, height: 160, borderRadius: 16, boxShadow: '0 2px 12px #e3f0ff' }} />
            </div>
            <Paragraph style={{ color: '#6ca0dc', fontSize: 14 }}>或手动输入密钥：<Text copyable>{genSecret}</Text></Paragraph>
            <div style={{ marginBottom: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                💡 扫码后，输入身份验证器中显示的6位数字验证码
              </Text>
            </div>
            <Input
              placeholder="000000"
              maxLength={6}
              value={inputCode}
              onChange={e => setInputCode(e.target.value.replace(/\D/g, ''))}
              onPressEnter={handleBind}
              style={{ 
                width: 180, 
                margin: '12px auto', 
                borderRadius: 12, 
                border: '1.5px solid #6ca0dc',
                fontSize: 18,
                textAlign: 'center',
                letterSpacing: '0.3em',
                fontFamily: 'monospace'
              }}
              size="large"
              status={inputCode.length > 0 && inputCode.length < 6 ? 'warning' : inputCode.length === 6 ? 'success' : ''}
            />
            {inputCode.length > 0 && inputCode.length < 6 && (
              <div style={{ marginBottom: 8 }}>
                <Text type="warning" style={{ fontSize: 12 }}>
                  ⚠️ 需要6位数字 ({inputCode.length}/6)
                </Text>
              </div>
            )}
            {inputCode.length === 6 && (
              <div style={{ marginBottom: 8 }}>
                <Text type="success" style={{ fontSize: 12 }}>
                  ✅ 格式正确，可以绑定
                </Text>
              </div>
            )}
            <Button 
              type="primary" 
              shape="round" 
              onClick={handleBind} 
              disabled={inputCode.length !== 6 || loading}
              loading={loading}
              style={{ background: 'linear-gradient(135deg, #e3f0ff 0%, #6ca0dc 100%)', border: 'none', marginTop: 8 }}
            >
              {loading ? '绑定中...' : '确认绑定'}
            </Button>
            <Button type="link" onClick={() => setStep('status')} style={{ marginLeft: 12 }}>返回</Button>
          </div>
        )}
        {step === 'unbind' && (
          <div style={{ textAlign: 'center', margin: 16 }}>
            <Paragraph style={{ color: '#6ca0dc', fontWeight: 500 }}>请输入当前2FA验证码以解绑喵~</Paragraph>
            <div style={{ marginBottom: 8 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                💡 打开您的身份验证器应用获取最新的6位数字验证码
              </Text>
            </div>
            <Input
              placeholder="000000"
              maxLength={6}
              value={inputCode}
              onChange={e => setInputCode(e.target.value.replace(/\D/g, ''))}
              onPressEnter={handleUnbind}
              style={{ 
                width: 180, 
                margin: '12px auto', 
                borderRadius: 12, 
                border: '1.5px solid #6ca0dc',
                fontSize: 18,
                textAlign: 'center',
                letterSpacing: '0.3em',
                fontFamily: 'monospace'
              }}
              size="large"
              status={inputCode.length > 0 && inputCode.length < 6 ? 'warning' : inputCode.length === 6 ? 'success' : ''}
            />
            {inputCode.length > 0 && inputCode.length < 6 && (
              <div style={{ marginBottom: 8 }}>
                <Text type="warning" style={{ fontSize: 12 }}>
                  ⚠️ 需要6位数字 ({inputCode.length}/6)
                </Text>
              </div>
            )}
            {inputCode.length === 6 && (
              <div style={{ marginBottom: 8 }}>
                <Text type="success" style={{ fontSize: 12 }}>
                  ✅ 格式正确，可以解绑
                </Text>
              </div>
            )}
            <Button 
              type="primary" 
              shape="round" 
              danger 
              onClick={handleUnbind} 
              disabled={inputCode.length !== 6 || loading}
              loading={loading}
              style={{ 
                background: 'linear-gradient(135deg, #ff7875 0%, #ff4d4f 100%)', 
                border: 'none', 
                marginTop: 8 
              }}
            >
              {loading ? '解绑中...' : '确认解绑'}
            </Button>
            <Button type="link" onClick={() => setStep('status')} style={{ marginLeft: 12 }}>返回</Button>
          </div>
        )}
      </Spin>
    </Card>
  );
};

const Settings = () => {
  const [form] = Form.useForm();
  const [baseUrlForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [baseUrlLoading, setBaseUrlLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [baseUrls, setBaseUrls] = useState({});
  const [lastSaved, setLastSaved] = useState(null);
  const [baseUrlsLastSaved, setBaseUrlsLastSaved] = useState(null);

  useEffect(() => {
    fetchSettings();
    fetchBaseUrls();
  }, []);

  const fetchSettings = async () => {
    try {
      setInitialLoading(true);
      const response = await settingsAPI.getSettings();
      if (response.data.code === 0) {
        form.setFieldsValue(response.data.data);
      }
    } catch (error) {
      console.error('获取设置失败:', error);
      // 如果获取失败，使用默认值
      form.setFieldsValue({
        authKey: '1145141919810',
        serverPort: 12041,
        logLevel: 'info',
        enableAuth: true,
        enableLogging: true,
        maxLogSize: 1000,
        autoCleanLogs: true,
        cleanLogsDays: 30,
        maxRequestsPerMinute: 100,
        enableRateLimit: true,
        websocketEnabled: false
      });
    } finally {
      setInitialLoading(false);
    }
  };
  const fetchBaseUrls = async () => {
    try {
      const response = await configAPI.getBaseUrls();
      if (response.data.success) {
        // 处理后端返回的数组格式数据
        const transformedData = {};
        const dataArray = response.data.data;
        
        if (Array.isArray(dataArray)) {
          // 后端返回数组格式，转换为对象格式
          dataArray.forEach(config => {
            if (config.client_type && config.base_url) {
              transformedData[config.client_type] = config.base_url;
            }
          });
        } else if (typeof dataArray === 'object') {
          // 后端返回对象格式，转换为简单的键值对
          Object.entries(dataArray).forEach(([clientType, config]) => {
            if (typeof config === 'object' && config.baseUrl) {
              transformedData[clientType] = config.baseUrl;
            } else if (typeof config === 'string') {
              transformedData[clientType] = config;
            } else {
              transformedData[clientType] = '';
            }
          });
        }
        
        setBaseUrls(transformedData);
        baseUrlForm.setFieldsValue({ baseUrls: transformedData });
      }
    } catch (error) {
      console.error('获取Base URLs失败:', error);
      // 设置默认值
      const defaultUrls = {
        'QQ': 'http://127.0.0.1:9511',
        'qidian': 'http://127.0.0.1:9513',
        'qqlite': 'http://127.0.0.1:9512',
        'tim': 'http://127.0.0.1:9514'
      };
      setBaseUrls(defaultUrls);
      baseUrlForm.setFieldsValue({ baseUrls: defaultUrls });
    }
  };
  const handleSave = async (values) => {
    setLoading(true);
    
    // 显示保存中的提示
    const saveNotification = notification.open({
      message: '正在保存设置...',
      description: '请稍候，系统正在保存您的配置',
      icon: <SaveOutlined style={{ color: '#1890ff' }} />,
      duration: 0, // 不自动关闭
      key: 'saving'
    });

    try {
      const response = await settingsAPI.saveSettings(values);
        // 关闭保存中提示
      notification.destroy('saving');
      
      if (response.data.code === 0) {
        // 显示成功通知
        NotificationHelper.operationSuccess('设置保存成功！', '✨ 系统配置已更新，保存时间: ' + new Date().toLocaleString());
        setLastSaved(new Date());
      } else {
        NotificationHelper.networkError(response.status || 500, response.data.msg || '保存设置失败');
      }
    } catch (error) {
      // 关闭保存中提示
      notification.destroy('saving');
      
      NotificationHelper.networkError(500, '保存设置失败: ' + error.message + '，请检查网络连接或联系管理员');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    fetchSettings();
    fetchBaseUrls();
    NotificationHelper.info('设置重置', '已重置为服务器设置');
  };
  const handleSaveBaseUrls = async (values) => {
    setBaseUrlLoading(true);
    
    // 显示保存中的提示
    const saveNotification = notification.open({
      message: '正在保存签名服务配置...',
      description: '请稍候，系统正在更新签名服务地址',
      icon: <DatabaseOutlined style={{ color: '#13c2c2' }} />,
      duration: 0,
      key: 'saving-baseUrls'
    });    try {
      // 将简单的 baseUrl 字符串转换为完整的配置对象格式
      const configData = {};
      Object.entries(values.baseUrls).forEach(([clientType, url]) => {
        if (typeof url === 'string') {
          configData[clientType] = {
            baseUrl: url,
            timeout: 10000,
            retryCount: 3,
            description: ''
          };
        } else if (typeof url === 'object' && url.baseUrl) {
          configData[clientType] = url;
        }
      });
      
      const response = await configAPI.updateBaseUrls(configData);
        // 关闭保存中提示
      notification.destroy('saving-baseUrls');
      
      if (response.data.success) {
        // 显示成功通知
        NotificationHelper.operationSuccess('签名服务配置保存成功！', '🎉 签名服务地址已更新，服务将使用新的配置地址');
        setBaseUrls(values.baseUrls);
        setBaseUrlsLastSaved(new Date());
      } else {
        NotificationHelper.networkError(response.status || 500, response.data.message || '保存签名服务配置失败');
      }
    } catch (error) {
      // 关闭保存中提示
      notification.destroy('saving-baseUrls');
      
      NotificationHelper.networkError(500, '保存签名服务配置失败: ' + error.message + '，请检查网络连接或服务状态');
      
      NotificationHelper.networkError(500, '保存签名服务配置失败: ' + error.message);
    } finally {
      setBaseUrlLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>加载设置中...</p>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>        {/* 页面标题 */}
        <div style={{ marginBottom: '24px' }}>
          <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
            <SettingOutlined style={{ marginRight: '12px' }} />
            系统设置
          </Title>
          <Paragraph style={{ margin: '8px 0 0 0', color: '#666' }}>
            配置系统参数，管理服务运行环境
          </Paragraph>
          
          {/* 保存状态指示器 */}
          <div style={{ marginTop: '12px' }}>
            <Row gutter={[16, 8]}>
              {lastSaved && (
                <Col>
                  <Alert
                    message={
                      <Space size={4}>
                        <CheckCircleOutlined style={{ color: '#52c41a' }} />
                        <span style={{ fontSize: '12px' }}>
                          系统设置已保存 - {lastSaved.toLocaleString()}
                        </span>
                      </Space>
                    }
                    type="success"
                    size="small"
                    style={{ 
                      padding: '4px 8px',
                      fontSize: '12px',
                      border: '1px solid #b7eb8f',
                      background: '#f6ffed'
                    }}
                  />
                </Col>
              )}
              {baseUrlsLastSaved && (
                <Col>
                  <Alert
                    message={
                      <Space size={4}>
                        <CheckCircleOutlined style={{ color: '#13c2c2' }} />
                        <span style={{ fontSize: '12px' }}>
                          签名服务已保存 - {baseUrlsLastSaved.toLocaleString()}
                        </span>
                      </Space>
                    }
                    type="info"
                    size="small"
                    style={{ 
                      padding: '4px 8px',
                      fontSize: '12px',
                      border: '1px solid #87e8de',
                      background: '#e6fffb'
                    }}
                  />
                </Col>
              )}
            </Row>
          </div>
        </div>

        <Row gutter={[24, 24]}>
          {/* 基础设置 */}
          <Col span={24}>
            <Card
              title={
                <Space>
                  <SecurityScanOutlined style={{ color: '#52c41a' }} />
                  <span>安全与认证设置</span>
                </Space>
              }
              extra={
                <Space>                  <Button
                    type="primary"
                    icon={loading ? <SaveOutlined spin /> : <SaveOutlined />}
                    onClick={() => form.submit()}
                    loading={loading}
                    size="small"
                    style={{
                      background: loading ? '#40a9ff' : '#1890ff',
                      borderColor: loading ? '#40a9ff' : '#1890ff',
                      boxShadow: loading ? '0 0 0 2px rgba(24, 144, 255, 0.2)' : 'none'
                    }}
                  >
                    {loading ? '保存中...' : '保存设置'}
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleReset}
                    size="small"
                  >
                    重置
                  </Button>
                </Space>
              }
              styles={{
                header: { 
                  background: 'linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%)',
                  borderBottom: '1px solid #d9f7be'
                }
              }}
            >
              <Form
                form={form}
                layout="vertical"
                onFinish={handleSave}
                initialValues={{
                  authKey: '1145141919810',
                  serverPort: 12041,
                  logLevel: 'info',
                  enableAuth: true,
                  enableLogging: true,
                  maxLogSize: 1000,
                  autoCleanLogs: true,
                  cleanLogsDays: 30,
                  enableNotification: false,
                  notificationEmail: '',
                  maxRequestsPerMinute: 100,
                  enableRateLimit: true,
                  websocketEnabled: false
                }}
              >
                <Row gutter={[24, 16]}>
                  {/* 认证设置 */}
                  <Col xs={24} lg={12}>
                    <Card 
                      size="small" 
                      title={
                        <Space>
                          <LockOutlined style={{ color: '#fa8c16' }} />
                          <span>认证配置</span>
                        </Space>
                      }
                      style={{ height: '100%' }}
                    >
                      <div style={{ padding: '16px 0' }}>
                        <div style={{ marginBottom: 16 }}>
                          <h4 style={{ margin: 0, color: '#1890ff' }}>🔐 JWT Token认证</h4>
                          <p style={{ margin: '8px 0', color: '#666' }}>
                            系统已启用JWT Token认证，无需配置API密钥
                          </p>
                        </div>

                        <div style={{
                          padding: 12,
                          background: '#f6ffed',
                          border: '1px solid #b7eb8f',
                          borderRadius: 4,
                          marginBottom: 16
                        }}>
                          <p style={{ margin: 0, fontSize: '14px' }}>
                            ✅ <strong>安全特性：</strong>
                          </p>
                          <ul style={{ margin: '8px 0 0 20px', fontSize: '13px', color: '#666' }}>
                            <li>自动Token刷新</li>
                            <li>会话过期保护</li>
                            <li>用户权限管理</li>
                            <li>登录状态监控</li>
                          </ul>
                        </div>

                        <div style={{ fontSize: '12px', color: '#999' }}>
                          💡 如需管理用户账户，请访问"用户管理"页面
                        </div>
                      </div>

                      <Form.Item 
                        label="启用请求限制" 
                        name="enableRateLimit" 
                        valuePropName="checked"
                      >
                        <Switch 
                          checkedChildren="开启" 
                          unCheckedChildren="关闭"
                        />
                      </Form.Item>

                      <Form.Item
                        label="每分钟最大请求数"
                        name="maxRequestsPerMinute"
                        rules={[{ required: true, message: '请输入最大请求数' }]}
                      >
                        <InputNumber
                          min={1}
                          max={10000}
                          style={{ width: '100%' }}
                          placeholder="100"
                        />
                      </Form.Item>
                    </Card>
                  </Col>

                  {/* 系统设置 */}
                  <Col xs={24} lg={12}>
                    <Card 
                      size="small" 
                      title={
                        <Space>
                          <SettingOutlined style={{ color: '#1890ff' }} />
                          <span>系统配置</span>
                        </Space>
                      }
                      style={{ height: '100%' }}
                    >
                      <Form.Item
                        label="服务器端口"
                        name="serverPort"
                        rules={[{ required: true, message: '请输入端口号' }]}
                      >
                        <InputNumber
                          min={1}
                          max={65535}
                          style={{ width: '100%' }}
                          placeholder="12041"
                        />
                      </Form.Item>
                      <Form.Item
                        label="启用WebSocket服务"
                        name="websocketEnabled"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                      </Form.Item>

                      <Form.Item
                        label="日志级别"
                        name="logLevel"
                        rules={[{ required: true, message: '请选择日志级别' }]}
                      >
                        <Select placeholder="请选择日志级别">
                          <Option value="error">ERROR - 仅错误</Option>
                          <Option value="warn">WARN - 警告及以上</Option>
                          <Option value="info">INFO - 信息及以上</Option>
                          <Option value="debug">DEBUG - 调试及以上</Option>
                        </Select>
                      </Form.Item>

                      <Form.Item 
                        label="启用日志记录" 
                        name="enableLogging" 
                        valuePropName="checked"
                      >
                        <Switch 
                          checkedChildren="开启" 
                          unCheckedChildren="关闭"
                        />
                      </Form.Item>

                      <Form.Item
                        label="最大日志条数"
                        name="maxLogSize"
                        rules={[{ required: true, message: '请输入最大日志条数' }]}
                      >
                        <InputNumber
                          min={100}
                          max={100000}
                          style={{ width: '100%' }}
                          placeholder="1000"
                        />
                      </Form.Item>
                    </Card>
                  </Col>

                  {/* 日志管理 */}
                  <Col xs={24} lg={12}>
                    <Card 
                      size="small" 
                      title={
                        <Space>
                          <DatabaseOutlined style={{ color: '#722ed1' }} />
                          <span>日志管理</span>
                        </Space>
                      }
                      style={{ height: '100%' }}
                    >
                      <Form.Item 
                        label="自动清理日志" 
                        name="autoCleanLogs" 
                        valuePropName="checked"
                      >
                        <Switch 
                          checkedChildren="开启" 
                          unCheckedChildren="关闭"
                        />
                      </Form.Item>

                      <Form.Item
                        label="日志保留天数"
                        name="cleanLogsDays"
                        rules={[{ required: true, message: '请输入保留天数' }]}
                      >
                        <InputNumber
                          min={1}
                          max={365}
                          style={{ width: '100%' }}
                          placeholder="30"
                          addonAfter="天"
                        />
                      </Form.Item>

                      <Alert
                        message="日志清理说明"
                        description="系统将自动清理超过指定天数的历史日志，建议保留30-90天"
                        type="info"
                        showIcon
                        style={{ marginTop: '16px' }}
                      />
                    </Card>
                  </Col>

                  {/* 管理员设置和通知设置已移至用户管理页面 */}
                </Row>
              </Form>
            </Card>
          </Col>

          {/* 系统信息 */}
          <Col span={24}>
            <Card
              title={
                <Space>
                  <SettingOutlined style={{ color: '#722ed1' }} />
                  <span>系统信息</span>
                </Space>
              }
              styles={{
                header: { 
                  background: 'linear-gradient(135deg, #f9f0ff 0%, #f0f9ff 100%)',
                  borderBottom: '1px solid #d3adf7'
                }
              }}
            >
              <Row gutter={[24, 16]}>
                <Col xs={24} sm={12} lg={6}>
                  <div style={{ textAlign: 'center', padding: '16px' }}>
                    <div style={{ fontSize: '24px', color: '#52c41a', marginBottom: '8px' }}>
                      ✅
                    </div>
                    <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>服务状态</div>
                    <div style={{ color: '#52c41a' }}>正常运行</div>
                  </div>
                </Col>
                <Col xs={24} sm={12} lg={6}>
                  <div style={{ textAlign: 'center', padding: '16px' }}>
                    <div style={{ fontSize: '24px', color: '#1890ff', marginBottom: '8px' }}>
                      🔗
                    </div>
                    <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>数据库连接</div>
                    <div style={{ color: '#1890ff' }}>SQLite 已连接</div>
                  </div>
                </Col>
                <Col xs={24} sm={12} lg={6}>
                  <div style={{ textAlign: 'center', padding: '16px' }}>
                    <div style={{ fontSize: '24px', color: '#fa8c16', marginBottom: '8px' }}>
                      📝
                    </div>
                    <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>日志记录</div>
                    <div style={{ color: '#fa8c16' }}>已启用</div>
                  </div>
                </Col>
                <Col xs={24} sm={12} lg={6}>
                  <div style={{ textAlign: 'center', padding: '16px' }}>
                    <div style={{ fontSize: '24px', color: '#eb2f96', marginBottom: '8px' }}>
                      🛡️
                    </div>
                    <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>安全认证</div>
                    <div style={{ color: '#eb2f96' }}>已启用</div>
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
        <TwoFASection />
      </div>
    </div>
  );
};

export default Settings;
