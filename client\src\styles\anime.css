/* 二次元萌系全局样式 */

/* 自定义字体 */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap');

/* 全局变量 */
:root {
  --anime-primary: #ff6b9d;
  --anime-secondary: #c44569;
  --anime-accent: #f8b500;
  --anime-purple: #6c5ce7;
  --anime-blue: #74b9ff;
  --anime-pink: #fd79a8;
  --anime-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --anime-gradient-pink: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
  --anime-gradient-purple: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  --anime-shadow: 0 8px 32px rgba(255, 107, 157, 0.3);
  --anime-shadow-hover: 0 12px 40px rgba(255, 107, 157, 0.4);
}

/* 全局样式重置 */
* {
  font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 182, 193, 0.1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: var(--anime-gradient-pink);
  border-radius: 10px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--anime-gradient);
}

/* 萌系按钮样式 */
.anime-button {
  background: var(--anime-gradient-pink) !important;
  border: none !important;
  border-radius: 20px !important;
  box-shadow: var(--anime-shadow) !important;
  transition: all 0.3s ease !important;
  font-weight: 500 !important;
  position: relative !important;
  overflow: hidden !important;
}

.anime-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.anime-button:hover::before {
  left: 100%;
}

.anime-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--anime-shadow-hover) !important;
}

/* 萌系卡片样式 */
.anime-card {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border-radius: 20px !important;
  border: 2px solid rgba(255, 182, 193, 0.3) !important;
  box-shadow: var(--anime-shadow) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.anime-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--anime-gradient-pink);
  z-index: 1;
}

.anime-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: var(--anime-shadow-hover) !important;
  border-color: rgba(255, 107, 157, 0.5) !important;
}

/* 萌系输入框样式 */
.anime-input {
  border-radius: 15px !important;
  border: 2px solid rgba(255, 182, 193, 0.5) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  transition: all 0.3s ease !important;
}

.anime-input:hover {
  border-color: var(--anime-pink) !important;
  box-shadow: 0 0 15px rgba(253, 121, 168, 0.3) !important;
}

.anime-input:focus {
  border-color: var(--anime-primary) !important;
  box-shadow: 0 0 20px rgba(255, 107, 157, 0.4) !important;
}

/* 萌系标签样式 */
.anime-tag {
  background: var(--anime-gradient-pink) !important;
  border: none !important;
  border-radius: 15px !important;
  color: white !important;
  font-weight: 500 !important;
  padding: 4px 12px !important;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3) !important;
}

.anime-tag.success {
  background: linear-gradient(135deg, #00b894 0%, #00cec9 100%) !important;
}

.anime-tag.error {
  background: linear-gradient(135deg, #e17055 0%, #fd79a8 100%) !important;
}

.anime-tag.warning {
  background: linear-gradient(135deg, #fdcb6e 0%, #f8b500 100%) !important;
}

/* 萌系表格样式 */
.anime-table .ant-table {
  border-radius: 15px !important;
  overflow: hidden !important;
  box-shadow: var(--anime-shadow) !important;
}

.anime-table .ant-table-thead > tr > th {
  background: var(--anime-gradient-purple) !important;
  color: #6c5ce7 !important;
  font-weight: 600 !important;
  border: none !important;
  text-align: center !important;
}

.anime-table .ant-table-tbody > tr:hover > td {
  background: rgba(255, 182, 193, 0.1) !important;
}

.anime-table .ant-table-tbody > tr > td {
  border-bottom: 1px solid rgba(255, 182, 193, 0.2) !important;
  transition: all 0.3s ease !important;
}

/* 萌系统计卡片 */
.anime-stat-card {
  background: var(--anime-gradient-purple) !important;
  border-radius: 20px !important;
  padding: 20px !important;
  text-align: center !important;
  box-shadow: var(--anime-shadow) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;
}

.anime-stat-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

.anime-stat-card:hover {
  transform: translateY(-5px) scale(1.02) !important;
  box-shadow: var(--anime-shadow-hover) !important;
}

.anime-stat-card .ant-statistic-title {
  color: #6c5ce7 !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
}

.anime-stat-card .ant-statistic-content {
  color: #2d3436 !important;
  font-weight: 700 !important;
}

/* 萌系菜单样式 */
.anime-menu.ant-menu-dark {
  background: linear-gradient(180deg, #2d3436 0%, #636e72 100%) !important;
}

.anime-menu.ant-menu-dark .ant-menu-item {
  border-radius: 10px !important;
  margin: 4px 8px !important;
  transition: all 0.3s ease !important;
}

.anime-menu.ant-menu-dark .ant-menu-item:hover {
  background: var(--anime-gradient-pink) !important;
  transform: translateX(5px) !important;
}

.anime-menu.ant-menu-dark .ant-menu-item-selected {
  background: var(--anime-gradient-pink) !important;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.4) !important;
}

/* 萌系头像样式 */
.anime-avatar {
  background: var(--anime-gradient-pink) !important;
  box-shadow: 0 4px 12px rgba(255, 107, 157, 0.3) !important;
  transition: all 0.3s ease !important;
}

.anime-avatar:hover {
  transform: scale(1.1) !important;
  box-shadow: 0 6px 20px rgba(255, 107, 157, 0.5) !important;
}

/* 萌系进度条 */
.anime-progress .ant-progress-bg {
  background: var(--anime-gradient-pink) !important;
}

.anime-progress .ant-progress-outer {
  border-radius: 10px !important;
  overflow: hidden !important;
}

/* 萌系消息提示 */
.ant-message .ant-message-notice-content {
  border-radius: 15px !important;
  box-shadow: var(--anime-shadow) !important;
  backdrop-filter: blur(10px) !important;
}

/* 萌系模态框 */
.anime-modal .ant-modal-content {
  border-radius: 20px !important;
  overflow: hidden !important;
  box-shadow: var(--anime-shadow-hover) !important;
  border: 2px solid rgba(255, 182, 193, 0.3) !important;
}

.anime-modal .ant-modal-header {
  background: var(--anime-gradient-purple) !important;
  border: none !important;
  padding: 20px 24px !important;
}

.anime-modal .ant-modal-title {
  color: #6c5ce7 !important;
  font-weight: 600 !important;
}

/* 动画效果 */
@keyframes shimmer {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* 萌系加载动画 */
.anime-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: var(--anime-gradient);
  color: white;
  font-size: 18px;
  font-weight: 500;
}

.anime-loading::before {
  content: '🌸';
  font-size: 32px;
  margin-right: 16px;
  animation: pulse 1.5s ease-in-out infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .anime-card {
    margin: 8px !important;
    border-radius: 15px !important;
  }
  
  .anime-stat-card {
    padding: 15px !important;
  }
  
  .anime-button {
    border-radius: 15px !important;
  }
}
