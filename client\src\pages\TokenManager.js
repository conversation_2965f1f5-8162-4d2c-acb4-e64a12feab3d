import { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  message,
  Popconfirm,
  Tag,
  Space,
  Tooltip,
  InputNumber,
  Switch,
  Alert
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ApiOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import api from '../services/api';
import Swal from 'sweetalert2';
import TokenDisplay from '../components/TokenDisplay';

const { TextArea } = Input;

const TokenManager = () => {
  const [tokens, setTokens] = useState([]);
  const [availableNodes, setAvailableNodes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingToken, setEditingToken] = useState(null);
  const [form] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [visibleTokens, setVisibleTokens] = useState(new Set());
  const [createLoading, setCreateLoading] = useState(false);

  // 获取token列表
  const fetchTokens = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const response = await api.get('/token-management/tokens', {
        params: { page, limit: pageSize }
      });
      
      if (response.data.code === 0) {
        setTokens(response.data.data.tokens);
        setPagination({
          current: page,
          pageSize,
          total: response.data.data.pagination.total
        });
      } else {
        message.error(response.data.msg || '获取token列表失败');
      }
    } catch (error) {
      message.error('获取token列表失败');
      console.error('获取token列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取可用节点列表
  const fetchAvailableNodes = async () => {
    try {
      const response = await api.get('/token-management/available-nodes');
      if (response.data.code === 0) {
        setAvailableNodes(response.data.data);
      }
    } catch (error) {
      console.error('获取可用节点失败:', error);
    }
  };

  useEffect(() => {
    fetchTokens();
    fetchAvailableNodes();
  }, []);

  // 创建新token
  const handleCreate = () => {
    setEditingToken(null);
    setModalVisible(true);
    form.resetFields();
    fetchAvailableNodes(); // 刷新可用节点列表
  };

  // 编辑token
  const handleEdit = (record) => {
    setEditingToken(record);
    setModalVisible(true);
    form.setFieldsValue({
      baseUrl: record.baseUrl,
      description: record.description,
      requestLimit: record.requestLimit,
      timeout: record.timeout
    });
  };

  // 保存token
  const handleSave = async () => {
    try {
      setCreateLoading(true);

      // 验证表单
      const values = await form.validateFields();
      console.log('提交的表单数据:', values); // 调试日志

      // 检查必填字段
      if (!editingToken && (!values.selectedNodes || values.selectedNodes.length === 0)) {
        message.error('请选择至少一个节点');
        return;
      }

      if (editingToken) {
        // 更新现有token
        const response = await api.put(`/token-management/tokens/${editingToken.nodeType}`, values);
        if (response.data.code === 0) {
          message.success('更新成功');
          setModalVisible(false);
          fetchTokens(pagination.current, pagination.pageSize);
          fetchAvailableNodes();
        } else {
          message.error(response.data.msg || '更新失败');
        }
      } else {
        // 创建新token
        console.log('正在创建token...'); // 调试日志
        const response = await api.post('/token-management/tokens', values);
        console.log('创建token响应:', response.data); // 调试日志

        if (response.data.code === 0) {
          const { results, errors, shareToken } = response.data.data;

          // 显示创建结果
          Modal.info({
            title: `Token创建完成`,
            content: (
              <div>
                <p style={{ marginBottom: 16 }}>
                  <strong>{response.data.msg}</strong>
                </p>

                {shareToken && results.length > 0 && (
                  <div style={{ marginBottom: 16, padding: 12, background: '#e6f7ff', borderRadius: 4, border: '1px solid #91d5ff' }}>
                    <p><strong>共享Token:</strong></p>
                    <p><code style={{ fontSize: '12px', wordBreak: 'break-all' }}>{results[0].token}</code></p>
                    <p style={{ margin: 0, color: '#666' }}>此Token可用于以下 {results.length} 个节点：</p>
                    {results.map((result, index) => (
                      <span key={index} style={{ marginRight: 8 }}>
                        <Tag color="blue">{result.nodeType}</Tag>
                      </span>
                    ))}
                  </div>
                )}

                {!shareToken && (
                  <div>
                    <p><strong>独立Token列表:</strong></p>
                    {results.map((result, index) => (
                      <div key={index} style={{ marginBottom: 12, padding: 8, background: '#f6f6f6', borderRadius: 4 }}>
                        <p><strong>节点:</strong> {result.nodeType}</p>
                        <p><strong>Token:</strong> <code style={{ fontSize: '12px' }}>{result.token}</code></p>
                      </div>
                    ))}
                  </div>
                )}

                {errors && errors.length > 0 && (
                  <div>
                    <p style={{ color: '#ff4d4f', marginTop: 16 }}>
                      <strong>以下节点处理失败:</strong>
                    </p>
                    {errors.map((error, index) => (
                      <p key={index} style={{ color: '#ff4d4f', margin: 0 }}>• {error}</p>
                    ))}
                  </div>
                )}
                <p style={{ color: '#ff4d4f', marginTop: 16 }}>
                  请妥善保存这些Token，关闭后将无法再次查看完整Token
                </p>
              </div>
            ),
            width: 800
          });
          message.success(response.data.msg);
          setModalVisible(false);
          fetchTokens(pagination.current, pagination.pageSize);
          fetchAvailableNodes();
        } else {
          message.error(response.data.msg || '创建失败');
        }
      }
    } catch (error) {
      console.error('保存失败:', error);
      const errorMsg = error.response?.data?.msg ||
                      error.response?.data?.message ||
                      error.message ||
                      '保存失败';
      message.error(errorMsg);
    } finally {
      setCreateLoading(false);
    }
  };

  // 删除token
  const handleDelete = async (nodeType) => {
    try {
      await api.delete(`/token-management/tokens/${nodeType}`);
      message.success('删除成功');
      fetchTokens(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error('删除失败');
      console.error('删除失败:', error);
    }
  };

  // 重新生成token
  const handleRegenerate = async (nodeType) => {
    try {
      const response = await api.post(`/token-management/tokens/${nodeType}/regenerate`);
      if (response.data.code === 0) {
        message.success('重新生成成功');
        // 显示新生成的token
        Modal.info({
          title: '新Token已生成',
          content: (
            <div>
              <p>节点类型: {nodeType}</p>
              <p>新Token: <code>{response.data.data.token}</code></p>
              <p style={{ color: '#ff4d4f' }}>请妥善保存此Token，旧Token已失效</p>
            </div>
          ),
          width: 600
        });
        fetchTokens(pagination.current, pagination.pageSize);
      } else {
        message.error(response.data.msg || '重新生成失败');
      }
    } catch (error) {
      message.error('重新生成失败');
      console.error('重新生成失败:', error);
    }
  };

  // 测试连接
  const handleTest = async (nodeType) => {
    try {
      const response = await api.post(`/token-management/tokens/${nodeType}/test`);
      if (response.data.code === 0) {
        message.success(`连接成功，延迟: ${response.data.data.latency}ms`);
      } else {
        message.error(`连接失败: ${response.data.msg}`);
      }
    } catch (error) {
      message.error('测试连接失败');
      console.error('测试连接失败:', error);
    }
  };

  // 测试SweetAlert2
  const testSweetAlert = () => {
    Swal.fire({
      title: '🧪 SweetAlert2 测试',
      text: '如果您看到这个弹窗，说明SweetAlert2工作正常！',
      icon: 'success',
      confirmButtonText: '太好了！'
    });
  };



  // 切换token可见性
  const toggleTokenVisibility = (nodeType) => {
    const newVisibleTokens = new Set(visibleTokens);
    if (newVisibleTokens.has(nodeType)) {
      newVisibleTokens.delete(nodeType);
    } else {
      newVisibleTokens.add(nodeType);
    }
    setVisibleTokens(newVisibleTokens);
  };

  const columns = [
    {
      title: '节点类型',
      dataIndex: 'nodeType',
      key: 'nodeType',
      width: 120,
      render: (text) => <Tag color="blue">{text}</Tag>
    },
    {
      title: 'Token',
      dataIndex: 'token',
      key: 'token',
      width: 300,
      render: (token, record) => (
        <Space>
          <code style={{ fontSize: '12px' }}>
            {visibleTokens.has(record.nodeType) ? token : record.tokenPreview}
          </code>
          <Button
            type="text"
            size="small"
            icon={visibleTokens.has(record.nodeType) ? <EyeInvisibleOutlined /> : <EyeOutlined />}
            onClick={() => toggleTokenVisibility(record.nodeType)}
          />
          <TokenDisplay
            token={token}
            nodeType={record.nodeType}
          />
        </Space>
      )
    },
    {
      title: '授权节点',
      key: 'authorizedNodes',
      width: 300,
      render: (_, record) => {
        // 如果有authorizedNodes数组，说明是新的多节点格式
        if (record.authorizedNodes && Array.isArray(record.authorizedNodes)) {
          return (
            <div>
              {record.authorizedNodes.map((node, index) => (
                <div key={index} style={{ marginBottom: 4 }}>
                  <Tag color="blue" size="small">{node.nodeType}</Tag>
                  <span style={{ fontSize: '12px', color: '#666' }}>
                    {node.baseUrl || '未配置URL'}
                  </span>
                </div>
              ))}
              <div style={{ fontSize: '12px', color: '#999' }}>
                共 {record.authorizedNodes.length} 个节点
              </div>
            </div>
          );
        }
        // 兼容旧格式
        return record.baseUrl || <span style={{ color: '#999' }}>未配置</span>;
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 150,
      ellipsis: true
    },
    {
      title: '请求限制',
      dataIndex: 'requestLimit',
      key: 'requestLimit',
      width: 100,
      render: (limit) => limit || '无限制'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="测试连接">
            <Button
              type="text"
              size="small"
              icon={<ApiOutlined />}
              onClick={() => handleTest(record.nodeType)}
              disabled={!record.baseUrl}
            />
          </Tooltip>
          <Tooltip title="重新生成Token">
            <Popconfirm
              title="确定要重新生成Token吗？旧Token将失效"
              onConfirm={() => handleRegenerate(record.nodeType)}
            >
              <Button type="text" size="small" icon={<ReloadOutlined />} />
            </Popconfirm>
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个Token吗？"
              onConfirm={() => handleDelete(record.nodeType)}
            >
              <Button type="text" size="small" danger icon={<DeleteOutlined />} />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2 style={{ margin: 0 }}>Token管理</h2>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            创建Token
          </Button>
          <Button
            type="default"
            onClick={testSweetAlert}
            style={{ display: process.env.NODE_ENV === 'development' ? 'inline-block' : 'none' }}
          >
            🧪 测试弹窗
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => fetchTokens(pagination.current, pagination.pageSize)}
          >
            刷新
          </Button>
        </Space>
      </div>

      <Table
        columns={columns}
        dataSource={tokens}
        rowKey="nodeType"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`,
          onChange: (page, pageSize) => {
            fetchTokens(page, pageSize);
          }
        }}
      />

      <Modal
        title={editingToken ? '编辑Token配置' : '创建新Token'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={() => setModalVisible(false)}
        width={600}
        confirmLoading={createLoading}
      >
        <Form form={form} layout="vertical">
          {!editingToken && (
            <>
              <Form.Item
                name="selectedNodes"
                label="选择节点"
                rules={[{ required: true, message: '请选择至少一个节点' }]}
              >
                <Select
                  mode="multiple"
                  placeholder="请选择要创建Token的节点"
                  style={{ width: '100%' }}
                  options={availableNodes}
                  showSearch
                  filterOption={(input, option) =>
                    option.label.toLowerCase().includes(input.toLowerCase())
                  }
                  notFoundContent={
                    availableNodes.length === 0 ?
                    "没有可用节点，请先在签名节点管理中创建节点" :
                    "没有找到匹配的节点"
                  }
                />
              </Form.Item>

              <Form.Item
                name="shareToken"
                label="Token模式"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch
                  checkedChildren="共享Token"
                  unCheckedChildren="独立Token"
                  onChange={(checked) => {
                    // 可以在这里添加模式切换的逻辑
                  }}
                />
              </Form.Item>

              <Alert
                message="Token模式说明"
                description={
                  <div>
                    <p><strong>共享Token模式：</strong>所有选中的节点使用同一个Token，支持负载均衡和故障转移</p>
                    <p><strong>独立Token模式：</strong>每个节点使用独立的Token，便于单独管理和权限控制</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            </>
          )}

          {editingToken && (
            <>
              <Form.Item
                name="baseUrl"
                label="节点URL"
                rules={[
                  { required: true, message: '请输入节点URL' },
                  { type: 'url', message: '请输入有效的URL' }
                ]}
              >
                <Input placeholder="http://127.0.0.1:9511" />
              </Form.Item>

              <Form.Item
                name="requestLimit"
                label="请求限制"
              >
                <InputNumber
                  min={0}
                  placeholder="0表示无限制"
                  style={{ width: '100%' }}
                />
              </Form.Item>

              <Form.Item
                name="timeout"
                label="超时时间(ms)"
              >
                <InputNumber
                  min={1000}
                  max={60000}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </>
          )}

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea
              rows={3}
              placeholder={editingToken ? "更新节点描述信息" : "为选中的节点添加描述信息"}
              maxLength={200}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TokenManager;
