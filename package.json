{"name": "signature-server", "version": "1.0.0", "description": "签名算法服务端 - 中转服务器和控制台", "main": "index.js", "private": true, "workspaces": ["client", "server"], "scripts": {"dev": "concurrently \"pnpm --filter server dev\" \"pnpm --filter client start\"", "start": "concurrently \"pnpm --filter server start\" \"pnpm --filter client start\"", "build": "pnpm --filter client build", "server": "pnpm --filter server start", "server:dev": "pnpm --filter server dev", "client": "pnpm --filter client start", "client:build": "pnpm --filter client build", "client:test": "pnpm --filter client test", "install:all": "pnpm install", "clean": "rimraf node_modules server/node_modules client/node_modules server/pnpm-lock.yaml client/pnpm-lock.yaml", "fresh": "pnpm run clean && pnpm install", "config:init": "pnpm --filter server config:init", "db:init": "pnpm --filter server db:init", "db:setup": "pnpm --filter server db:setup"}, "keywords": ["signature", "server", "proxy", "qq"], "author": "", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "nodemon": "^3.0.1", "rimraf": "^5.0.5"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@arco-design/web-react": "^2.66.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.25.4", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "colors": "^1.4.0", "cors": "^2.8.5", "eslint-config-react-app": "^7.0.1", "express": "^4.18.2", "geoip-lite": "^1.4.10", "got": "^11.8.6", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "multer": "^2.0.1", "mysql2": "^3.14.1", "node-cron": "^4.2.0", "node-fetch": "^2.7.0", "node-json-db": "^2.3.0", "node-rsa": "^1.1.1", "otplib": "^12.0.1", "qrcode": "^1.5.4", "rc-slider-captcha": "^1.7.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-scripts": "5.0.1", "recharts": "^2.15.3", "sql.js": "^1.13.0", "sweetalert2": "^11.22.2", "ua-parser-js": "^2.0.4", "web-vitals": "^2.1.4", "ws": "^8.18.2"}, "resolutions": {"eslint-config-react-app": "7.0.1"}}