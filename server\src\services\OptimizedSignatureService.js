const SignatureQueue = require('./SignatureQueue');
const axios = require('axios');
const colors = require('colors');

/**
 * 优化的签名服务
 * 支持负载均衡、健康检查、连接池、智能路由
 */
class OptimizedSignatureService extends SignatureQueue {
    constructor(options = {}) {
        super(options);
        
        // 节点管理
        this.nodes = new Map(); // 可用节点
        this.nodeStats = new Map(); // 节点统计
        this.healthCheckInterval = options.healthCheckInterval || 30000; // 健康检查间隔
        this.nodeTimeout = options.nodeTimeout || 10000; // 节点请求超时
        
        // 连接池配置
        this.connectionPool = new Map(); // 连接池
        this.maxConnectionsPerNode = options.maxConnectionsPerNode || 10;
        
        // 负载均衡策略
        this.loadBalanceStrategy = options.loadBalanceStrategy || 'round_robin'; // round_robin, least_connections, response_time
        this.currentNodeIndex = 0;
        
        // 启动健康检查
        this.startHealthCheck();
        
        console.log(`🎯 优化签名服务启动 - 负载均衡策略: ${this.loadBalanceStrategy}`.green);
    }
    
    /**
     * 添加签名节点
     */
    addNode(nodeConfig) {
        const { client_type, base_url, auth_key, timeout = 10000, retry_count = 3 } = nodeConfig;
        
        if (!client_type || !base_url) {
            throw new Error('节点配置不完整');
        }
        
        const node = {
            id: client_type,
            baseUrl: base_url,
            authKey: auth_key,
            timeout,
            retryCount: retry_count,
            isHealthy: true,
            lastHealthCheck: Date.now(),
            connectionCount: 0,
            maxConnections: this.maxConnectionsPerNode
        };
        
        this.nodes.set(client_type, node);
        this.nodeStats.set(client_type, {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            lastRequestTime: 0,
            errorRate: 0
        });
        
        // 创建连接池
        this.createConnectionPool(node);
        
        console.log(`✅ 添加签名节点: ${client_type} - ${base_url}`.green);
    }
    
    /**
     * 创建连接池
     */
    createConnectionPool(node) {
        const axiosInstance = axios.create({
            baseURL: node.baseUrl,
            timeout: node.timeout,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': node.authKey ? `Bearer ${node.authKey}` : undefined
            },
            // 连接池配置
            maxRedirects: 0,
            validateStatus: (status) => status < 500, // 只有5xx才认为是错误
        });
        
        // 请求拦截器
        axiosInstance.interceptors.request.use(
            (config) => {
                node.connectionCount++;
                return config;
            },
            (error) => {
                return Promise.reject(error);
            }
        );
        
        // 响应拦截器
        axiosInstance.interceptors.response.use(
            (response) => {
                node.connectionCount = Math.max(0, node.connectionCount - 1);
                return response;
            },
            (error) => {
                node.connectionCount = Math.max(0, node.connectionCount - 1);
                return Promise.reject(error);
            }
        );
        
        this.connectionPool.set(node.id, axiosInstance);
    }
    
    /**
     * 移除节点
     */
    removeNode(nodeId) {
        this.nodes.delete(nodeId);
        this.nodeStats.delete(nodeId);
        this.connectionPool.delete(nodeId);
        console.log(`🗑️  移除签名节点: ${nodeId}`.yellow);
    }
    
    /**
     * 执行签名请求 (重写父类方法)
     * 使用现有的转发逻辑，不改变转发过程
     */
    async executeRequest(request) {
        const { data } = request;
        const { cmd, uin, requestData, originalFormat } = data;

        // 选择最佳节点
        const node = this.selectBestNode(data);
        if (!node) {
            throw new Error('没有可用的签名节点');
        }

        const startTime = Date.now();

        try {
            // 检查连接数限制
            if (node.connectionCount >= node.maxConnections) {
                throw new Error(`节点 ${node.id} 连接数已满`);
            }

            // 使用现有的转发逻辑
            const result = await this.forwardToNode(node, requestData, originalFormat);

            const responseTime = Date.now() - startTime;

            // 更新节点统计
            this.updateNodeStats(node.id, responseTime, true);

            return {
                success: true,
                data: result.data,
                nodeId: node.id,
                responseTime,
                nodeMessage: result.msg
            };

        } catch (error) {
            const responseTime = Date.now() - startTime;
            this.updateNodeStats(node.id, responseTime, false);

            // 如果是连接错误，标记节点为不健康
            if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
                this.markNodeUnhealthy(node.id);
            }

            throw error;
        }
    }

    /**
     * 转发请求到节点 - 使用现有的转发逻辑
     */
    async forwardToNode(node, requestData, originalFormat) {
        const axios = require('axios');

        // 数据处理 - 保持原有逻辑
        let processedData = String(requestData);
        processedData = processedData.replace(/buffer/g, "data");
        processedData = processedData.replace(/(\&|^)seq=/g, "$1ssoseq=");
        processedData = processedData.replace(/&android_id=/g, "&type=getSign&android_id=");
        processedData = processedData.replace(/b'/g, '');
        processedData = processedData.replace(/'/g, "");

        // 数据格式转换 - 保持原有逻辑
        let formattedData = processedData;
        if (originalFormat === 'json' && processedData.startsWith('{') && processedData.endsWith('}')) {
            try {
                const jsonData = JSON.parse(processedData);
                const params = new URLSearchParams();
                for (const [key, value] of Object.entries(jsonData)) {
                    params.append(key, value);
                }
                formattedData = params.toString();
            } catch (e) {
                formattedData = processedData;
            }
        } else {
            if (!processedData.includes('type=')) {
                formattedData = processedData + '&type=getSign';
            }
        }

        // 发送请求 - 保持原有的请求格式和头部
        const headers = { "Content-Type": "application/x-www-form-urlencoded" };

        // 确保请求发送到正确的端点
        let targetUrl = node.baseUrl;
        if (!targetUrl.includes('/sign') && !targetUrl.includes('/api')) {
            // 如果baseUrl是根URL，添加默认的签名端点
            targetUrl = targetUrl.endsWith('/') ? targetUrl + 'sign' : targetUrl + '/sign';
        }

        node.connectionCount++;

        try {
            const response = await axios.post(targetUrl, formattedData, {
                headers,
                timeout: node.timeout || 30000, // 默认30秒超时
                transformRequest: [(data) => data] // 不进行任何转换
            });

            const result = response.data;
            console.log(`🔍 节点原始响应: ${JSON.stringify(result)}`.gray);

            // 处理不同的响应格式 - 保持原有逻辑
            let resultData;

            if (result.code === 0 && result.data) {
                // 如果节点返回标准格式 {code: 0, data: {...}}
                resultData = result.data;
            } else if (result.sign || result.token || result.extra) {
                // 如果节点直接返回签名数据
                resultData = {
                    sign: result.sign || '',
                    token: result.token || '',
                    extra: result.extra || ''
                };
            } else if (result.result) {
                // 如果节点返回 {result: {...}} 格式
                resultData = {
                    sign: result.result.sign || '',
                    token: result.result.token || '',
                    extra: result.result.extra || ''
                };
            } else {
                // 其他情况，尝试从响应中提取
                resultData = {
                    sign: result.sign || '',
                    token: result.token || '',
                    extra: result.extra || '',
                    // 如果都没有，保留原始响应
                    raw: result
                };
            }

            return {
                code: 0,
                msg: result.message || `当前签名服务端: ${node.id}`,
                data: resultData
            };

        } finally {
            node.connectionCount = Math.max(0, node.connectionCount - 1);
        }
    }
    
    /**
     * 选择最佳节点
     */
    selectBestNode(requestData) {
        const healthyNodes = Array.from(this.nodes.values()).filter(node => node.isHealthy);
        
        if (healthyNodes.length === 0) {
            return null;
        }
        
        switch (this.loadBalanceStrategy) {
            case 'round_robin':
                return this.selectRoundRobin(healthyNodes);
            
            case 'least_connections':
                return this.selectLeastConnections(healthyNodes);
            
            case 'response_time':
                return this.selectByResponseTime(healthyNodes);
            
            case 'client_type':
                return this.selectByClientType(healthyNodes, requestData);
            
            default:
                return this.selectRoundRobin(healthyNodes);
        }
    }
    
    /**
     * 轮询选择
     */
    selectRoundRobin(nodes) {
        const node = nodes[this.currentNodeIndex % nodes.length];
        this.currentNodeIndex++;
        return node;
    }
    
    /**
     * 最少连接数选择
     */
    selectLeastConnections(nodes) {
        return nodes.reduce((best, current) => {
            return current.connectionCount < best.connectionCount ? current : best;
        });
    }
    
    /**
     * 响应时间选择
     */
    selectByResponseTime(nodes) {
        return nodes.reduce((best, current) => {
            const currentStats = this.nodeStats.get(current.id);
            const bestStats = this.nodeStats.get(best.id);
            
            if (!currentStats) return best;
            if (!bestStats) return current;
            
            return currentStats.averageResponseTime < bestStats.averageResponseTime ? current : best;
        });
    }
    
    /**
     * 根据客户端类型选择
     */
    selectByClientType(nodes, requestData) {
        // 优先选择匹配的客户端类型
        const preferredNode = nodes.find(node => {
            // 这里可以根据请求数据选择最适合的节点类型
            return true; // 简化实现
        });
        
        return preferredNode || this.selectLeastConnections(nodes);
    }
    
    /**
     * 更新节点统计
     */
    updateNodeStats(nodeId, responseTime, success) {
        const stats = this.nodeStats.get(nodeId);
        if (!stats) return;
        
        stats.totalRequests++;
        stats.lastRequestTime = Date.now();
        
        if (success) {
            stats.successfulRequests++;
            // 更新平均响应时间
            const totalTime = stats.averageResponseTime * (stats.successfulRequests - 1) + responseTime;
            stats.averageResponseTime = Math.round(totalTime / stats.successfulRequests);
        } else {
            stats.failedRequests++;
        }
        
        // 计算错误率
        stats.errorRate = (stats.failedRequests / stats.totalRequests * 100).toFixed(2);
    }
    
    /**
     * 标记节点为不健康
     */
    markNodeUnhealthy(nodeId) {
        const node = this.nodes.get(nodeId);
        if (node) {
            node.isHealthy = false;
            console.log(`⚠️  节点 ${nodeId} 标记为不健康`.yellow);
        }
    }
    
    /**
     * 启动健康检查
     */
    startHealthCheck() {
        setInterval(async () => {
            await this.performHealthCheck();
        }, this.healthCheckInterval);
    }
    
    /**
     * 执行健康检查
     */
    async performHealthCheck() {
        const promises = Array.from(this.nodes.entries()).map(async ([nodeId, node]) => {
            try {
                const axiosInstance = this.connectionPool.get(nodeId);
                if (!axiosInstance) return;

                // 直接访问节点根路由，有任何返回值就认为健康
                const response = await axiosInstance.get('/', {
                    timeout: 5000,
                    validateStatus: () => true // 接受任何状态码
                });

                // 只要有响应就认为节点健康
                if (response) {
                    if (!node.isHealthy) {
                        console.log(`✅ 节点 ${nodeId} 恢复健康 (${node.baseUrl})`.green);
                    }
                    node.isHealthy = true;
                } else {
                    this.markNodeUnhealthy(nodeId);
                }

                node.lastHealthCheck = Date.now();

            } catch (error) {
                // 网络错误或超时才认为不健康
                if (!node.isHealthy) {
                    console.log(`❌ 节点 ${nodeId} 健康检查失败: ${error.message}`.red);
                }
                this.markNodeUnhealthy(nodeId);
            }
        });

        await Promise.allSettled(promises);
    }
    
    /**
     * 获取服务状态
     */
    getServiceStatus() {
        const baseStatus = this.getStatus();
        
        const nodeStatus = Array.from(this.nodes.entries()).map(([nodeId, node]) => {
            const stats = this.nodeStats.get(nodeId);
            return {
                nodeId,
                baseUrl: node.baseUrl,
                isHealthy: node.isHealthy,
                connectionCount: node.connectionCount,
                maxConnections: node.maxConnections,
                stats: stats || {}
            };
        });
        
        return {
            ...baseStatus,
            nodes: nodeStatus,
            totalNodes: this.nodes.size,
            healthyNodes: nodeStatus.filter(n => n.isHealthy).length
        };
    }

    /**
     * 批量添加节点
     */
    async loadNodesFromConfig(dataModel) {
        try {
            const configs = await dataModel.getAllApiConfigs();
            const validConfigs = configs.filter(config =>
                config.base_url &&
                config.status === 'active' &&
                config.auth_key &&
                config.auth_key.length === 128
            );

            for (const config of validConfigs) {
                this.addNode(config);
            }

            console.log(`📡 加载了 ${validConfigs.length} 个签名节点`.green);
            return validConfigs.length;
        } catch (error) {
            console.error('加载节点配置失败:', error);
            return 0;
        }
    }

    /**
     * 智能签名请求
     */
    async requestSignature(cmd, uin, qua, buffer, priority = 'normal') {
        const requestData = { cmd, uin, qua, buffer };

        try {
            const result = await this.addRequest(requestData, priority);
            return result;
        } catch (error) {
            console.error(`签名请求失败: ${error.message}`.red);
            throw error;
        }
    }

    /**
     * 获取节点推荐
     */
    getNodeRecommendation() {
        const healthyNodes = Array.from(this.nodes.values()).filter(node => node.isHealthy);

        if (healthyNodes.length === 0) {
            return { recommendation: 'no_healthy_nodes', message: '没有健康的节点可用' };
        }

        const nodePerformance = healthyNodes.map(node => {
            const stats = this.nodeStats.get(node.id);
            return {
                nodeId: node.id,
                baseUrl: node.baseUrl,
                performance: this.calculateNodePerformance(node, stats)
            };
        }).sort((a, b) => b.performance - a.performance);

        return {
            recommendation: 'optimal_distribution',
            bestNode: nodePerformance[0],
            allNodes: nodePerformance
        };
    }

    /**
     * 计算节点性能分数
     */
    calculateNodePerformance(node, stats) {
        if (!stats || stats.totalRequests === 0) {
            return 50; // 默认分数
        }

        // 性能分数计算：成功率 * 0.4 + 响应时间分数 * 0.3 + 连接负载分数 * 0.3
        const successRate = (stats.successfulRequests / stats.totalRequests) * 100;
        const responseTimeScore = Math.max(0, 100 - (stats.averageResponseTime / 100));
        const connectionLoadScore = Math.max(0, 100 - (node.connectionCount / node.maxConnections * 100));

        return Math.round(successRate * 0.4 + responseTimeScore * 0.3 + connectionLoadScore * 0.3);
    }
}

module.exports = OptimizedSignatureService;
