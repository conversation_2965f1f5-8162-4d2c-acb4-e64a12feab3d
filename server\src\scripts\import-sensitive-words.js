const colors = require('colors');
const mysqlModel = require('../models/mysql-real-model');

/**
 * 完善的敏感词库
 * 包括政治敏感词、色情暴力、赌博诈骗、外挂作弊、谐音词等
 */
const sensitiveWords = [
  // ==================== 政治敏感词 ====================
  // 直接政治敏感词
  { keyword: '六四', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感事件' },
  { keyword: '天安门', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感地点' },
  { keyword: '法轮功', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感组织' },
  { keyword: '台独', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感词汇' },
  { keyword: '港独', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感词汇' },
  { keyword: '藏独', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感词汇' },
  { keyword: '疆独', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感词汇' },
  { keyword: '反共', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感词汇' },
  { keyword: '民运', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感词汇' },
  { keyword: '学运', keyword_type: 'strict', risk_level: 'high', description: '政治敏感词汇' },
  
  // 谐音政治敏感词
  { keyword: '64', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感事件谐音' },
  { keyword: '8964', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感事件谐音' },
  { keyword: '89年', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感事件谐音' },
  { keyword: '坦克人', keyword_type: 'strict', risk_level: 'critical', description: '政治敏感事件' },
  { keyword: '维尼熊', keyword_type: 'fuzzy', risk_level: 'high', description: '政治相关谐音' },
  { keyword: '小熊维尼', keyword_type: 'fuzzy', risk_level: 'high', description: '政治相关谐音' },
  { keyword: '庆丰包子', keyword_type: 'fuzzy', risk_level: 'medium', description: '政治相关谐音' },

  // ==================== 外挂作弊类 ====================
  // 直接外挂词汇
  { keyword: '外挂', keyword_type: 'strict', risk_level: 'critical', description: '使用外挂工具' },
  { keyword: '挂机', keyword_type: 'strict', risk_level: 'high', description: '挂机行为' },
  { keyword: '脚本', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能使用脚本' },
  { keyword: '机器人', keyword_type: 'fuzzy', risk_level: 'medium', description: '机器人行为' },
  { keyword: 'bot', keyword_type: 'fuzzy', risk_level: 'medium', description: '机器人行为英文' },
  { keyword: '自动化', keyword_type: 'fuzzy', risk_level: 'medium', description: '自动化操作' },
  { keyword: '辅助工具', keyword_type: 'strict', risk_level: 'high', description: '游戏辅助工具' },
  { keyword: '修改器', keyword_type: 'strict', risk_level: 'critical', description: '游戏修改器' },
  { keyword: '变速器', keyword_type: 'strict', risk_level: 'high', description: '游戏变速器' },
  { keyword: 'CE', keyword_type: 'strict', risk_level: 'high', description: 'Cheat Engine缩写' },
  { keyword: 'cheat', keyword_type: 'fuzzy', risk_level: 'high', description: '作弊相关英文' },
  { keyword: 'hack', keyword_type: 'fuzzy', risk_level: 'high', description: '破解相关英文' },
  
  // 外挂谐音词
  { keyword: '瓦依挂', keyword_type: 'strict', risk_level: 'high', description: '外挂谐音' },
  { keyword: '歪鸡', keyword_type: 'strict', risk_level: 'high', description: '外挂谐音' },
  { keyword: '歪果', keyword_type: 'strict', risk_level: 'medium', description: '外挂谐音' },

  // ==================== 赌博诈骗类 ====================
  // 直接赌博词汇
  { keyword: '赌博', keyword_type: 'strict', risk_level: 'critical', description: '赌博行为' },
  { keyword: '博彩', keyword_type: 'strict', risk_level: 'critical', description: '博彩行为' },
  { keyword: '彩票', keyword_type: 'strict', risk_level: 'high', description: '彩票相关' },
  { keyword: '六合彩', keyword_type: 'strict', risk_level: 'critical', description: '非法彩票' },
  { keyword: '时时彩', keyword_type: 'strict', risk_level: 'critical', description: '非法彩票' },
  { keyword: '澳门赌场', keyword_type: 'strict', risk_level: 'critical', description: '赌场相关' },
  { keyword: '百家乐', keyword_type: 'strict', risk_level: 'critical', description: '赌博游戏' },
  { keyword: '德州扑克', keyword_type: 'strict', risk_level: 'high', description: '赌博游戏' },
  { keyword: '21点', keyword_type: 'strict', risk_level: 'high', description: '赌博游戏' },
  { keyword: '老虎机', keyword_type: 'strict', risk_level: 'critical', description: '赌博机器' },
  { keyword: '投注', keyword_type: 'fuzzy', risk_level: 'high', description: '投注行为' },
  { keyword: '下注', keyword_type: 'fuzzy', risk_level: 'high', description: '下注行为' },
  { keyword: '押注', keyword_type: 'fuzzy', risk_level: 'high', description: '押注行为' },
  
  // 诈骗相关
  { keyword: '诈骗', keyword_type: 'strict', risk_level: 'critical', description: '诈骗行为' },
  { keyword: '骗钱', keyword_type: 'strict', risk_level: 'critical', description: '骗钱行为' },
  { keyword: '刷单', keyword_type: 'strict', risk_level: 'high', description: '违规刷单行为' },
  { keyword: '兼职刷单', keyword_type: 'strict', risk_level: 'high', description: '刷单兼职诈骗' },
  { keyword: '返利', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能的诈骗返利' },
  { keyword: '高额回报', keyword_type: 'strict', risk_level: 'high', description: '诈骗常用词' },
  { keyword: '日赚千元', keyword_type: 'strict', risk_level: 'high', description: '诈骗常用词' },
  { keyword: '躺赚', keyword_type: 'strict', risk_level: 'medium', description: '可疑赚钱方式' },
  { keyword: '传销', keyword_type: 'strict', risk_level: 'critical', description: '传销行为' },
  { keyword: '拉人头', keyword_type: 'strict', risk_level: 'high', description: '传销特征' },
  { keyword: '发展下线', keyword_type: 'strict', risk_level: 'high', description: '传销特征' },
  
  // 赌博谐音词
  { keyword: '赌帛', keyword_type: 'strict', risk_level: 'high', description: '赌博谐音' },
  { keyword: '堵博', keyword_type: 'strict', risk_level: 'high', description: '赌博谐音' },
  { keyword: '独播', keyword_type: 'strict', risk_level: 'medium', description: '赌博谐音' },

  // ==================== 色情暴力类 ====================
  // 色情相关
  { keyword: '色情', keyword_type: 'strict', risk_level: 'critical', description: '色情内容' },
  { keyword: '黄色', keyword_type: 'fuzzy', risk_level: 'high', description: '色情相关' },
  { keyword: '成人', keyword_type: 'fuzzy', risk_level: 'medium', description: '成人内容' },
  { keyword: '裸体', keyword_type: 'strict', risk_level: 'critical', description: '色情内容' },
  { keyword: '性爱', keyword_type: 'strict', risk_level: 'critical', description: '色情内容' },
  { keyword: '做爱', keyword_type: 'strict', risk_level: 'critical', description: '色情内容' },
  { keyword: '操逼', keyword_type: 'strict', risk_level: 'critical', description: '色情粗俗词汇' },
  { keyword: '强奸', keyword_type: 'strict', risk_level: 'critical', description: '暴力犯罪' },
  { keyword: '强暴', keyword_type: 'strict', risk_level: 'critical', description: '暴力犯罪' },
  { keyword: '性侵', keyword_type: 'strict', risk_level: 'critical', description: '暴力犯罪' },
  { keyword: '卖淫', keyword_type: 'strict', risk_level: 'critical', description: '违法行为' },
  { keyword: '嫖娼', keyword_type: 'strict', risk_level: 'critical', description: '违法行为' },
  { keyword: '援交', keyword_type: 'strict', risk_level: 'critical', description: '违法行为' },
  { keyword: '包养', keyword_type: 'strict', risk_level: 'high', description: '不当交易' },
  
  // 暴力相关
  { keyword: '杀人', keyword_type: 'strict', risk_level: 'critical', description: '暴力犯罪' },
  { keyword: '谋杀', keyword_type: 'strict', risk_level: 'critical', description: '暴力犯罪' },
  { keyword: '自杀', keyword_type: 'strict', risk_level: 'high', description: '自我伤害' },
  { keyword: '爆炸', keyword_type: 'strict', risk_level: 'critical', description: '暴力行为' },
  { keyword: '恐怖主义', keyword_type: 'strict', risk_level: 'critical', description: '恐怖主义' },
  { keyword: '恐怖分子', keyword_type: 'strict', risk_level: 'critical', description: '恐怖主义' },
  { keyword: '血腥', keyword_type: 'strict', risk_level: 'high', description: '暴力血腥' },
  { keyword: '砍人', keyword_type: 'strict', risk_level: 'critical', description: '暴力行为' },
  { keyword: '捅刀', keyword_type: 'strict', risk_level: 'critical', description: '暴力行为' },
  
  // 色情暴力谐音词
  { keyword: 'Cao', keyword_type: 'strict', risk_level: 'high', description: '粗俗词汇拼音' },
  { keyword: 'SB', keyword_type: 'strict', risk_level: 'medium', description: '骂人词汇缩写' },
  { keyword: 'CNM', keyword_type: 'strict', risk_level: 'high', description: '骂人词汇缩写' },
  { keyword: 'MLGB', keyword_type: 'strict', risk_level: 'high', description: '骂人词汇缩写' },

  // ==================== 毒品相关 ====================
  { keyword: '毒品', keyword_type: 'strict', risk_level: 'critical', description: '毒品相关' },
  { keyword: '大麻', keyword_type: 'strict', risk_level: 'critical', description: '毒品类型' },
  { keyword: '海洛因', keyword_type: 'strict', risk_level: 'critical', description: '毒品类型' },
  { keyword: '冰毒', keyword_type: 'strict', risk_level: 'critical', description: '毒品类型' },
  { keyword: '摇头丸', keyword_type: 'strict', risk_level: 'critical', description: '毒品类型' },
  { keyword: 'K粉', keyword_type: 'strict', risk_level: 'critical', description: '毒品类型' },
  { keyword: '吸毒', keyword_type: 'strict', risk_level: 'critical', description: '吸毒行为' },
  { keyword: '贩毒', keyword_type: 'strict', risk_level: 'critical', description: '贩毒行为' },
  { keyword: '制毒', keyword_type: 'strict', risk_level: 'critical', description: '制毒行为' },

  // ==================== 违法犯罪类 ====================
  { keyword: '洗钱', keyword_type: 'strict', risk_level: 'critical', description: '洗钱犯罪' },
  { keyword: '走私', keyword_type: 'strict', risk_level: 'critical', description: '走私犯罪' },
  { keyword: '偷税', keyword_type: 'strict', risk_level: 'high', description: '税务犯罪' },
  { keyword: '逃税', keyword_type: 'strict', risk_level: 'high', description: '税务犯罪' },
  { keyword: '贿赂', keyword_type: 'strict', risk_level: 'critical', description: '贿赂犯罪' },
  { keyword: '受贿', keyword_type: 'strict', risk_level: 'critical', description: '受贿犯罪' },
  { keyword: '腐败', keyword_type: 'strict', risk_level: 'high', description: '腐败行为' },
  { keyword: '黑社会', keyword_type: 'strict', risk_level: 'critical', description: '黑社会组织' },
  { keyword: '黑帮', keyword_type: 'strict', risk_level: 'critical', description: '黑帮组织' },
  { keyword: '绑架', keyword_type: 'strict', risk_level: 'critical', description: '绑架犯罪' },
  { keyword: '拐卖', keyword_type: 'strict', risk_level: 'critical', description: '拐卖犯罪' },
  { keyword: '人口贩卖', keyword_type: 'strict', risk_level: 'critical', description: '人口贩卖' },

  // ==================== 游戏违规类 ====================
  // 刷单作弊
  { keyword: '代练', keyword_type: 'strict', risk_level: 'medium', description: '代练行为' },
  { keyword: '代打', keyword_type: 'strict', risk_level: 'medium', description: '代打行为' },
  { keyword: '买号', keyword_type: 'strict', risk_level: 'medium', description: '账号交易' },
  { keyword: '卖号', keyword_type: 'strict', risk_level: 'medium', description: '账号交易' },
  { keyword: '租号', keyword_type: 'strict', risk_level: 'medium', description: '账号租借' },
  { keyword: '共享账号', keyword_type: 'strict', risk_level: 'medium', description: '账号共享' },
  { keyword: '批量注册', keyword_type: 'strict', risk_level: 'high', description: '批量注册账号' },
  { keyword: '刷经验', keyword_type: 'strict', risk_level: 'medium', description: '刷经验行为' },
  { keyword: '刷金币', keyword_type: 'strict', risk_level: 'medium', description: '刷金币行为' },
  { keyword: '刷装备', keyword_type: 'strict', risk_level: 'medium', description: '刷装备行为' },
  { keyword: '无限刷', keyword_type: 'strict', risk_level: 'high', description: '无限刷取' },
  { keyword: '卡bug', keyword_type: 'strict', risk_level: 'high', description: '利用bug' },
  { keyword: '复制', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能的复制bug' },
  
  // 游戏破解
  { keyword: '破解版', keyword_type: 'strict', risk_level: 'high', description: '破解游戏' },
  { keyword: '盗版', keyword_type: 'strict', risk_level: 'high', description: '盗版游戏' },
  { keyword: '免费版', keyword_type: 'fuzzy', risk_level: 'low', description: '可能的盗版' },
  { keyword: '无限金币', keyword_type: 'strict', risk_level: 'high', description: '修改游戏' },
  { keyword: '无限钻石', keyword_type: 'strict', risk_level: 'high', description: '修改游戏' },
  { keyword: '内购破解', keyword_type: 'strict', risk_level: 'high', description: '内购破解' },
  { keyword: '去广告', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能的破解' },

  // ==================== 网络诈骗专用词 ====================
  { keyword: '网络兼职', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能的诈骗兼职' },
  { keyword: '打字员', keyword_type: 'fuzzy', risk_level: 'medium', description: '诈骗常用兼职' },
  { keyword: '录入员', keyword_type: 'fuzzy', risk_level: 'medium', description: '诈骗常用兼职' },
  { keyword: '足不出户', keyword_type: 'strict', risk_level: 'medium', description: '诈骗常用词' },
  { keyword: '月入过万', keyword_type: 'strict', risk_level: 'high', description: '诈骗常用词' },
  { keyword: '轻松赚钱', keyword_type: 'strict', risk_level: 'medium', description: '诈骗常用词' },
  { keyword: '无需押金', keyword_type: 'strict', risk_level: 'medium', description: '诈骗常用词' },
  { keyword: '先付后返', keyword_type: 'strict', risk_level: 'high', description: '诈骗套路' },
  { keyword: '保证金', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能的诈骗' },
  { keyword: '激活费', keyword_type: 'strict', risk_level: 'high', description: '诈骗常用词' },
  { keyword: '会员费', keyword_type: 'fuzzy', risk_level: 'low', description: '可能的诈骗' },
  { keyword: '培训费', keyword_type: 'fuzzy', risk_level: 'low', description: '可能的诈骗' },
  { keyword: '刷信誉', keyword_type: 'strict', risk_level: 'high', description: '刷信誉诈骗' },
  { keyword: '刷好评', keyword_type: 'strict', risk_level: 'medium', description: '刷好评行为' },
  { keyword: '快速提现', keyword_type: 'strict', risk_level: 'medium', description: '诈骗常用词' },
  { keyword: '秒到账', keyword_type: 'strict', risk_level: 'medium', description: '诈骗常用词' },

  // ==================== 金融诈骗类 ====================
  { keyword: '套现', keyword_type: 'strict', risk_level: 'high', description: '信用卡套现' },
  { keyword: '代办信用卡', keyword_type: 'strict', risk_level: 'high', description: '信用卡诈骗' },
  { keyword: '信用卡提额', keyword_type: 'strict', risk_level: 'medium', description: '可能的诈骗' },
  { keyword: '无抵押贷款', keyword_type: 'strict', risk_level: 'high', description: '贷款诈骗' },
  { keyword: '快速放款', keyword_type: 'strict', risk_level: 'medium', description: '贷款诈骗' },
  { keyword: '黑户贷款', keyword_type: 'strict', risk_level: 'high', description: '贷款诈骗' },
  { keyword: '洗白征信', keyword_type: 'strict', risk_level: 'high', description: '征信诈骗' },
  { keyword: '修复征信', keyword_type: 'strict', risk_level: 'high', description: '征信诈骗' },
  { keyword: '投资理财', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能的金融诈骗' },
  { keyword: '高收益', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能的投资诈骗' },
  { keyword: '稳赚不赔', keyword_type: 'strict', risk_level: 'high', description: '投资诈骗' },
  { keyword: '内幕消息', keyword_type: 'strict', risk_level: 'high', description: '投资诈骗' },
  { keyword: '股票推荐', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能的股票诈骗' },
  { keyword: '外汇交易', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能的外汇诈骗' },
  { keyword: '比特币', keyword_type: 'fuzzy', risk_level: 'low', description: '虚拟货币' },
  { keyword: '数字货币', keyword_type: 'fuzzy', risk_level: 'low', description: '虚拟货币' },
  { keyword: '挖矿', keyword_type: 'fuzzy', risk_level: 'low', description: '虚拟货币挖矿' },

  // ==================== 社交诈骗类 ====================
  { keyword: '交友', keyword_type: 'fuzzy', risk_level: 'low', description: '可能的交友诈骗' },
  { keyword: '征婚', keyword_type: 'fuzzy', risk_level: 'low', description: '可能的征婚诈骗' },
  { keyword: '美女', keyword_type: 'fuzzy', risk_level: 'low', description: '可能的美人计' },
  { keyword: '帅哥', keyword_type: 'fuzzy', risk_level: 'low', description: '可能的美人计' },
  { keyword: '约会', keyword_type: 'fuzzy', risk_level: 'low', description: '可能的约会诈骗' },
  { keyword: '私聊', keyword_type: 'fuzzy', risk_level: 'low', description: '可能的私聊诈骗' },
  { keyword: '视频聊天', keyword_type: 'fuzzy', risk_level: 'low', description: '可能的视频诈骗' },
  { keyword: '裸聊', keyword_type: 'strict', risk_level: 'high', description: '裸聊敲诈' },
  { keyword: '敲诈', keyword_type: 'strict', risk_level: 'critical', description: '敲诈勒索' },
  { keyword: '勒索', keyword_type: 'strict', risk_level: 'critical', description: '敲诈勒索' },

  // ==================== 垃圾广告类 ====================
  { keyword: '微商', keyword_type: 'fuzzy', risk_level: 'low', description: '微商广告' },
  { keyword: '代购', keyword_type: 'fuzzy', risk_level: 'low', description: '代购广告' },
  { keyword: '减肥', keyword_type: 'fuzzy', risk_level: 'low', description: '减肥广告' },
  { keyword: '丰胸', keyword_type: 'strict', risk_level: 'medium', description: '丰胸广告' },
  { keyword: '壮阳', keyword_type: 'strict', risk_level: 'medium', description: '壮阳广告' },
  { keyword: '保健品', keyword_type: 'fuzzy', risk_level: 'low', description: '保健品广告' },
  { keyword: '特效药', keyword_type: 'strict', risk_level: 'high', description: '虚假药品广告' },
  { keyword: '包治百病', keyword_type: 'strict', risk_level: 'high', description: '虚假医疗广告' },
  { keyword: '祖传秘方', keyword_type: 'strict', risk_level: 'high', description: '虚假医疗广告' },
  { keyword: '包治', keyword_type: 'fuzzy', risk_level: 'medium', description: '虚假医疗广告' },
  { keyword: '速效', keyword_type: 'fuzzy', risk_level: 'medium', description: '夸大宣传' },
  { keyword: '神药', keyword_type: 'strict', risk_level: 'high', description: '虚假药品' },
  { keyword: '仙丹', keyword_type: 'strict', risk_level: 'medium', description: '虚假药品' },

  // ==================== 迷信封建类 ====================
  { keyword: '算命', keyword_type: 'fuzzy', risk_level: 'low', description: '封建迷信' },
  { keyword: '占卜', keyword_type: 'fuzzy', risk_level: 'low', description: '封建迷信' },
  { keyword: '风水', keyword_type: 'fuzzy', risk_level: 'low', description: '封建迷信' },
  { keyword: '看相', keyword_type: 'fuzzy', risk_level: 'low', description: '封建迷信' },
  { keyword: '转运', keyword_type: 'fuzzy', risk_level: 'low', description: '封建迷信' },
  { keyword: '开光', keyword_type: 'fuzzy', risk_level: 'low', description: '封建迷信' },
  { keyword: '驱邪', keyword_type: 'strict', risk_level: 'medium', description: '封建迷信' },
  { keyword: '消灾', keyword_type: 'fuzzy', risk_level: 'low', description: '封建迷信' },
  { keyword: '破财免灾', keyword_type: 'strict', risk_level: 'medium', description: '迷信诈骗' },
  { keyword: '大师', keyword_type: 'fuzzy', risk_level: 'low', description: '可能的迷信诈骗' },
  { keyword: '神婆', keyword_type: 'strict', risk_level: 'medium', description: '封建迷信' },
  { keyword: '巫师', keyword_type: 'strict', risk_level: 'medium', description: '封建迷信' },

  // ==================== 常见谐音缩写 ====================
  { keyword: 'TMD', keyword_type: 'strict', risk_level: 'medium', description: '骂人词汇缩写' },
  { keyword: 'TM', keyword_type: 'strict', risk_level: 'low', description: '骂人词汇缩写' },
  { keyword: 'WTF', keyword_type: 'strict', risk_level: 'medium', description: '骂人词汇缩写' },
  { keyword: 'FUCK', keyword_type: 'strict', risk_level: 'medium', description: '英文粗俗词汇' },
  { keyword: 'SHIT', keyword_type: 'strict', risk_level: 'medium', description: '英文粗俗词汇' },
  { keyword: 'DAMN', keyword_type: 'strict', risk_level: 'low', description: '英文粗俗词汇' },
  { keyword: 'BITCH', keyword_type: 'strict', risk_level: 'medium', description: '英文粗俗词汇' },

  // ==================== 网络暴力类 ====================
  { keyword: '人肉搜索', keyword_type: 'strict', risk_level: 'high', description: '网络暴力' },
  { keyword: '网络暴力', keyword_type: 'strict', risk_level: 'high', description: '网络暴力' },
  { keyword: '网暴', keyword_type: 'strict', risk_level: 'high', description: '网络暴力' },
  { keyword: '开盒', keyword_type: 'strict', risk_level: 'high', description: '恶意开盒' },
  { keyword: '爆破', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能的网络攻击' },
  { keyword: '社工', keyword_type: 'fuzzy', risk_level: 'medium', description: '社会工程学攻击' },
  { keyword: '钓鱼', keyword_type: 'fuzzy', risk_level: 'medium', description: '钓鱼攻击' },
  { keyword: '挂马', keyword_type: 'strict', risk_level: 'high', description: '恶意挂马' },
  { keyword: '木马', keyword_type: 'fuzzy', risk_level: 'medium', description: '木马病毒' },
  { keyword: '病毒', keyword_type: 'fuzzy', risk_level: 'low', description: '计算机病毒' },

  // ==================== 其他违法违规 ====================
  { keyword: '偷拍', keyword_type: 'strict', risk_level: 'high', description: '偷拍行为' },
  { keyword: '偷窥', keyword_type: 'strict', risk_level: 'high', description: '偷窥行为' },
  { keyword: '跟踪', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能的跟踪骚扰' },
  { keyword: '骚扰', keyword_type: 'strict', risk_level: 'high', description: '骚扰行为' },
  { keyword: '威胁', keyword_type: 'strict', risk_level: 'high', description: '威胁行为' },
  { keyword: '恐吓', keyword_type: 'strict', risk_level: 'high', description: '恐吓行为' },
  { keyword: '报复', keyword_type: 'fuzzy', risk_level: 'medium', description: '可能的报复行为' },
  { keyword: '私人信息', keyword_type: 'strict', risk_level: 'medium', description: '隐私泄露' },
  { keyword: '身份证号', keyword_type: 'strict', risk_level: 'high', description: '敏感个人信息' },
  { keyword: '银行卡号', keyword_type: 'strict', risk_level: 'high', description: '敏感金融信息' },
  { keyword: '密码', keyword_type: 'fuzzy', risk_level: 'medium', description: '敏感账户信息' },
  { keyword: '验证码', keyword_type: 'fuzzy', risk_level: 'medium', description: '敏感验证信息' },

  // ==================== 特殊符号混淆 ====================
  { keyword: 'f*ck', keyword_type: 'strict', risk_level: 'medium', description: '符号混淆粗俗词' },
  { keyword: 's*it', keyword_type: 'strict', risk_level: 'medium', description: '符号混淆粗俗词' },
  { keyword: 'c@o', keyword_type: 'strict', risk_level: 'medium', description: '符号混淆粗俗词' },
  { keyword: '赌8博', keyword_type: 'strict', risk_level: 'high', description: '数字混淆赌博' },
  { keyword: '外挂', keyword_type: 'strict', risk_level: 'critical', description: '使用外挂工具' }
];

/**
 * 批量导入敏感词库到数据库
 */
async function importSensitiveWords() {
  console.log('\n🛡️ 开始导入完善的敏感词库...'.cyan.bold);
  
  try {
    await mysqlModel.ensureConnection();
    
    console.log(`📝 准备导入 ${sensitiveWords.length} 个敏感词...`.yellow);
    
    // 批量导入
    const result = await mysqlModel.batchImportRiskKeywords(sensitiveWords);
    
    console.log('\n✅ 敏感词库导入完成!'.green.bold);
    console.log(`📊 导入统计:`.cyan);
    console.log(`   📝 总词数: ${sensitiveWords.length}`.gray);
    console.log(`   ✅ 成功导入: ${result.successCount}`.green);
    console.log(`   ❌ 导入失败: ${result.errorCount}`.red);
    
    if (result.errors.length > 0) {
      console.log(`\n❌ 导入失败的词汇:`.red);
      result.errors.forEach(error => {
        console.log(`   - ${error.keyword}: ${error.error}`.gray);
      });
    }
    
    // 显示统计信息
    console.log('\n📊 敏感词分类统计:'.cyan);
    const categories = {
      '政治敏感': 0,
      '外挂作弊': 0,
      '赌博诈骗': 0,
      '色情暴力': 0,
      '毒品相关': 0,
      '违法犯罪': 0,
      '游戏违规': 0,
      '网络诈骗': 0,
      '金融诈骗': 0,
      '社交诈骗': 0,
      '垃圾广告': 0,
      '迷信封建': 0,
      '网络暴力': 0,
      '其他违规': 0
    };
    
    sensitiveWords.forEach(word => {
      if (word.description.includes('政治')) categories['政治敏感']++;
      else if (word.description.includes('外挂') || word.description.includes('作弊') || word.description.includes('机器人') || word.description.includes('脚本')) categories['外挂作弊']++;
      else if (word.description.includes('赌博') || word.description.includes('博彩') || word.description.includes('彩票')) categories['赌博诈骗']++;
      else if (word.description.includes('色情') || word.description.includes('暴力') || word.description.includes('粗俗')) categories['色情暴力']++;
      else if (word.description.includes('毒品')) categories['毒品相关']++;
      else if (word.description.includes('犯罪') || word.description.includes('违法')) categories['违法犯罪']++;
      else if (word.description.includes('游戏') || word.description.includes('代练') || word.description.includes('破解')) categories['游戏违规']++;
      else if (word.description.includes('诈骗') && (word.description.includes('网络') || word.description.includes('兼职'))) categories['网络诈骗']++;
      else if (word.description.includes('诈骗') && word.description.includes('金融')) categories['金融诈骗']++;
      else if (word.description.includes('诈骗') && word.description.includes('交友')) categories['社交诈骗']++;
      else if (word.description.includes('广告')) categories['垃圾广告']++;
      else if (word.description.includes('迷信') || word.description.includes('封建')) categories['迷信封建']++;
      else if (word.description.includes('网络暴力') || word.description.includes('开盒')) categories['网络暴力']++;
      else categories['其他违规']++;
    });
    
    Object.entries(categories).forEach(([category, count]) => {
      if (count > 0) {
        console.log(`   🏷️  ${category}: ${count} 个`.gray);
      }
    });
    
  } catch (error) {
    console.error('❌ 导入敏感词库失败:'.red, error.message);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  importSensitiveWords()
    .then(() => {
      console.log('\n🎉 敏感词库导入任务完成!'.green.bold);
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 导入任务失败:'.red.bold, error);
      process.exit(1);
    });
}

module.exports = { sensitiveWords, importSensitiveWords };
