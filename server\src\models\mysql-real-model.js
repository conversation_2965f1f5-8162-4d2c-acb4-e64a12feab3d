const bcrypt = require('bcryptjs');
const colors = require('colors');
const mysqlConnection = require('../config/mysql');

/**
 * 真正的MySQL数据库操作模型
 * 提供统一的数据库操作接口，使用MySQL数据库
 */
class MySQLDataModel {
    constructor() {
        this.db = mysqlConnection;
    }

    /**
     * 记录签名请求日志
     */
    async logSignRequest(logData) {
        const {
            ip, uin, cmd, type, endpoint, success, error_msg,
            request_data, response_data, user_agent, response_time_ms = 0
        } = logData;

        const sql = `INSERT INTO sign_logs (ip, uin, cmd, type, endpoint, success, error_msg, request_data, response_data, user_agent, response_time_ms)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;
        
        const params = [
            ip || null,
            uin || null,
            cmd || null,
            type || null,
            endpoint || null,
            success ? 1 : 0,
            error_msg || null,
            request_data || null,
            response_data || null,
            user_agent || null,
            response_time_ms || 0
        ];

        const result = await this.db.run(sql, params);
        return result.insertId;
    }

    /**
     * 获取签名日志（分页）
     */
    async getSignLogs(filters = {}, pagination = { page: 1, limit: 50 }) {
        const { page, limit } = pagination;
        const offset = (page - 1) * limit;
        
        let whereClause = 'WHERE 1=1';
        const params = [];
        
        // 构建过滤条件
        if (filters.ip) {
            whereClause += ' AND ip LIKE ?';
            params.push(`%${filters.ip}%`);
        }
        if (filters.uin) {
            whereClause += ' AND uin = ?';
            params.push(filters.uin);
        }
        if (filters.type) {
            whereClause += ' AND type = ?';
            params.push(filters.type);
        }
        if (filters.success !== undefined) {
            whereClause += ' AND success = ?';
            params.push(filters.success ? 1 : 0);
        }
        if (filters.startDate) {
            whereClause += ' AND DATE(request_time) >= ?';
            params.push(filters.startDate);
        }
        if (filters.endDate) {
            whereClause += ' AND DATE(request_time) <= ?';
            params.push(filters.endDate);
        }

        // 获取总数
        const countSql = `SELECT COUNT(*) as total FROM sign_logs ${whereClause}`;
        const countResult = await this.db.query(countSql, params);
        const total = countResult[0].total;

        // 获取数据
        const dataSql = `SELECT * FROM sign_logs ${whereClause} ORDER BY request_time DESC LIMIT ? OFFSET ?`;
        const logs = await this.db.query(dataSql, [...params, limit, offset]);

        return {
            logs,
            pagination: {
                page,
                limit,
                total,
                totalPages: Math.ceil(total / limit)
            }
        };
    }

    /**
     * 根据ID获取签名日志
     */
    async getSignLogById(id) {
        const sql = 'SELECT * FROM sign_logs WHERE id = ?';
        const result = await this.db.query(sql, [id]);
        return result.length > 0 ? result[0] : null;
    }

    /**
     * 获取今日概览统计
     */
    async getTodayOverview() {
        const today = new Date().toISOString().split('T')[0];
        
        const sql = `
            SELECT 
                COUNT(*) as total_requests,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_requests,
                COUNT(DISTINCT ip) as unique_ips,
                COUNT(DISTINCT uin) as unique_uins
            FROM sign_logs 
            WHERE DATE(request_time) = ?
        `;
        
        const result = await this.db.query(sql, [today]);
        const stats = result[0];
        
        return {
            total_requests: stats.total_requests || 0,
            success_requests: stats.success_requests || 0,
            failed_requests: (stats.total_requests || 0) - (stats.success_requests || 0),
            success_rate: stats.total_requests ? 
                ((stats.success_requests || 0) / stats.total_requests * 100).toFixed(2) : '0.00',
            unique_ips: stats.unique_ips || 0,
            unique_uins: stats.unique_uins || 0
        };
    }

    /**
     * 获取每日统计
     */
    async getDailyStats(days = 7) {
        const sql = `
            SELECT 
                DATE(request_time) as date,
                COUNT(*) as total_requests,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_requests,
                COUNT(DISTINCT ip) as unique_ips,
                COUNT(DISTINCT uin) as unique_uins,
                AVG(response_time_ms) as avg_response_time
            FROM sign_logs 
            WHERE request_time >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
            GROUP BY DATE(request_time)
            ORDER BY date ASC
        `;
        
        const result = await this.db.query(sql, [days]);
        
        return result.map(row => ({
            ...row,
            failed_requests: row.total_requests - row.success_requests,
            success_rate: row.total_requests ? 
                ((row.success_requests / row.total_requests) * 100).toFixed(2) : '0.00',
            avg_response_time: Math.round(row.avg_response_time || 0)
        }));
    }

    /**
     * 获取类型统计
     */
    async getTypeStats() {
        const sql = `
            SELECT 
                type,
                COUNT(*) as total_requests,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_requests,
                AVG(response_time_ms) as avg_response_time
            FROM sign_logs 
            GROUP BY type
            ORDER BY total_requests DESC
        `;
        
        const result = await this.db.query(sql);
        
        return result.map(row => ({
            ...row,
            failed_requests: row.total_requests - row.success_requests,
            success_rate: row.total_requests ? 
                ((row.success_requests / row.total_requests) * 100).toFixed(2) : '0.00',
            avg_response_time: Math.round(row.avg_response_time || 0)
        }));
    }

    /**
     * 获取端点统计
     */
    async getEndpointStats() {
        const sql = `
            SELECT 
                endpoint,
                COUNT(*) as total_requests,
                SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_requests,
                AVG(response_time_ms) as avg_response_time
            FROM sign_logs 
            GROUP BY endpoint
            ORDER BY total_requests DESC
        `;
        
        const result = await this.db.query(sql);
        
        return result.map(row => ({
            ...row,
            failed_requests: row.total_requests - row.success_requests,
            success_rate: row.total_requests ? 
                ((row.success_requests / row.total_requests) * 100).toFixed(2) : '0.00',
            avg_response_time: Math.round(row.avg_response_time || 0)
        }));
    }

    /**
     * 用户验证
     */
    async verifyUser(username, password) {
        const sql = 'SELECT * FROM users WHERE username = ? AND status = "active"';
        const result = await this.db.query(sql, [username]);
        
        if (!result || result.length === 0) return null;
        
        const user = result[0];
        const isMatch = await bcrypt.compare(password, user.password);
        
        if (!isMatch) return null;
        
        // 更新最后登录时间
        await this.db.run('UPDATE users SET last_login = NOW() WHERE id = ?', [user.id]);
        
        return user;
    }

    /**
     * 获取API配置
     */
    async getApiConfig(clientType) {
        const sql = 'SELECT * FROM api_configs WHERE client_type = ? AND status = "active"';
        const result = await this.db.query(sql, [clientType]);
        return result.length > 0 ? result[0] : null;
    }

    /**
     * 设置API配置
     */
    async setApiConfig(clientType, baseUrl, timeout = 10000, retryCount = 3, description = '', authKey = '', requestLimit = 0, tokenMode = 'exclusive') {
        const existing = await this.getApiConfig(clientType);

        if (existing) {
            const sql = `UPDATE api_configs SET base_url = ?, timeout = ?, retry_count = ?, description = ?, auth_key = ?, token_mode = ?, request_limit = ?, updated_at = NOW() WHERE client_type = ?`;
            await this.db.run(sql, [baseUrl, timeout, retryCount, description, authKey, tokenMode, requestLimit, clientType]);
        } else {
            const sql = `INSERT INTO api_configs (client_type, base_url, timeout, retry_count, description, auth_key, token_mode, request_limit) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
            await this.db.run(sql, [clientType, baseUrl, timeout, retryCount, description, authKey, tokenMode, requestLimit]);
        }
    }

    /**
     * 删除API配置
     */
    async deleteApiConfig(clientType) {
        try {
            const sql = 'DELETE FROM api_configs WHERE client_type = ?';
            const result = await this.db.run(sql, [clientType]);
            return result.affectedRows > 0;
        } catch (error) {
            console.error('删除API配置失败:', error);
            throw error;
        }
    }

    /**
     * 获取所有API配置
     */
    async getAllApiConfigs() {
        const sql = 'SELECT * FROM api_configs WHERE status = "active" ORDER BY client_type';
        return await this.db.query(sql);
    }

    /**
     * 根据token获取token模式和相关节点
     */
    async getTokenModeAndNodes(authKey) {
        if (!authKey || authKey.length !== 128) {
            return { mode: null, nodes: [] };
        }

        const sql = 'SELECT client_type, token_mode, base_url, timeout, retry_count, description, request_limit, status FROM api_configs WHERE auth_key = ? AND status = "active"';
        const nodes = await this.db.query(sql, [authKey]);

        if (nodes.length === 0) {
            return { mode: null, nodes: [] };
        }

        // 所有使用相同token的节点应该有相同的token_mode
        const mode = nodes[0].token_mode;
        return { mode, nodes };
    }

    /**
     * 生成128位认证密钥
     */
    generateAuthKey() {
        const crypto = require('crypto');
        return crypto.randomBytes(64).toString('hex'); // 64字节 = 128位十六进制字符串
    }

    /**
     * 批量更新API配置
     */
    async updateAllApiConfigs(baseUrls) {
        try {
            for (const [clientType, config] of Object.entries(baseUrls)) {
                const baseUrl = typeof config === 'string' ? config : config.baseUrl;
                const timeout = typeof config === 'object' ? (config.timeout || 10000) : 10000;
                const retryCount = typeof config === 'object' ? (config.retryCount || 3) : 3;
                const description = typeof config === 'object' ? (config.description || '') : '';

                await this.setApiConfig(clientType, baseUrl, timeout, retryCount, description);
            }
            return true;
        } catch (error) {
            console.error('批量更新API配置失败:', error);
            throw error;
        }
    }

    /**
     * 获取系统设置
     */
    async getSetting(key, defaultValue = null) {
        const sql = 'SELECT key_value FROM settings WHERE key_name = ?';
        const result = await this.db.query(sql, [key]);
        
        if (result.length > 0) {
            return result[0].key_value;
        }
        return defaultValue;
    }

    /**
     * 设置系统设置
     */
    async setSetting(key, value, type = 'string', description = '') {
        // 处理 undefined 值，转换为 null
        const safeValue = value === undefined ? null : value;
        const safeDescription = description === undefined ? '' : description;
        const safeType = type === undefined ? 'string' : type;

        // 调试信息
        console.log(`setSetting 调用参数:`, { key, value, type, description });
        console.log(`处理后的参数:`, { key, safeValue, safeDescription, safeType });

        const sql = `INSERT INTO settings (key_name, key_value, description, type) VALUES (?, ?, ?, ?)
                     ON DUPLICATE KEY UPDATE key_value = ?, description = ?, type = ?, updated_at = NOW()`;

        const params = [key, safeValue, safeDescription, safeType, safeValue, safeDescription, safeType];
        console.log(`SQL 参数:`, params);

        await this.db.run(sql, params);
    }

    /**
     * 检查是否在黑名单
     */
    async isBlacklisted(clientInfo) {
        const { ip_address, username, device_id, uin } = clientInfo;

        const sql = `
            SELECT * FROM blacklist
            WHERE is_active = 1
            AND (expire_time IS NULL OR expire_time > NOW())
            AND (
                (ip_address = ? AND ip_address IS NOT NULL) OR
                (username = ? AND username IS NOT NULL) OR
                (device_id = ? AND device_id IS NOT NULL) OR
                (uin = ? AND uin IS NOT NULL)
            )
            LIMIT 1
        `;

        const result = await this.db.query(sql, [ip_address, username, device_id, uin]);

        if (result.length > 0) {
            return {
                isBlacklisted: true,
                record: result[0]
            };
        }

        return { isBlacklisted: false };
    }

    /**
     * 检查是否在白名单
     */
    async isWhitelisted(clientInfo) {
        const { ip_address, username, device_id, uin } = clientInfo;

        const sql = `
            SELECT * FROM whitelist
            WHERE is_active = 1
            AND (
                (ip_address = ? AND ip_address IS NOT NULL) OR
                (username = ? AND username IS NOT NULL) OR
                (device_id = ? AND device_id IS NOT NULL) OR
                (uin = ? AND uin IS NOT NULL)
            )
            LIMIT 1
        `;

        const result = await this.db.query(sql, [ip_address, username, device_id, uin]);
        return result.length > 0;
    }

    /**
     * 记录风控数据
     */
    async recordRiskData(data) {
        const {
            username, device_id, ip_address, uin, risk_content,
            matched_keywords = [], risk_score = 0, risk_level = 'medium',
            auto_blocked = false, client_info = {}, request_data = ''
        } = data;

        const sql = `INSERT INTO risk_records
            (username, device_id, ip_address, uin, risk_content, matched_keywords, risk_score, risk_level, auto_blocked, client_info, request_data)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

        const params = [
            username || null,
            device_id || null,
            ip_address || null,
            uin || null,
            risk_content || '',
            JSON.stringify(matched_keywords),
            risk_score,
            risk_level,
            auto_blocked ? 1 : 0,
            JSON.stringify(client_info),
            request_data || ''
        ];

        const result = await this.db.run(sql, params);
        return result.insertId;
    }

    /**
     * 创建用户
     */
    async createUser(username, password, role = 'admin') {
        try {
            const hashedPassword = await bcrypt.hash(password, 10);

            const sql = 'INSERT INTO users (username, password, role) VALUES (?, ?, ?)';
            const result = await this.db.run(sql, [username, hashedPassword, role]);
            return result.insertId;
        } catch (error) {
            console.error('创建用户失败:', error);
            throw error;
        }
    }

    /**
     * 删除用户
     */
    async deleteUser(userId) {
        try {
            const sql = 'DELETE FROM users WHERE id = ?';
            await this.db.run(sql, [userId]);
            return true;
        } catch (error) {
            console.error('删除用户失败:', error);
            throw error;
        }
    }

    /**
     * 获取风控关键词
     */
    async getRiskKeywords(filters = {}, pagination = { page: 1, limit: 1000 }) {
        try {
            // 确保分页参数是有效的数字
            const page = parseInt(pagination.page) || 1;
            const limit = parseInt(pagination.limit) || 1000;
            const offset = (page - 1) * limit;

            let whereClause = 'WHERE is_active = 1';
            const whereParams = [];

            if (filters.is_active !== undefined) {
                whereClause = 'WHERE is_active = ?';
                whereParams.push(filters.is_active ? 1 : 0);
            }

            if (filters.keyword) {
                whereClause += whereParams.length > 0 ? ' AND keyword LIKE ?' : ' WHERE keyword LIKE ?';
                whereParams.push(`%${filters.keyword}%`);
            }

            if (filters.keyword_type) {
                whereClause += whereParams.length > 0 ? ' AND keyword_type = ?' : ' WHERE keyword_type = ?';
                whereParams.push(filters.keyword_type);
            }

            if (filters.risk_level) {
                whereClause += whereParams.length > 0 ? ' AND risk_level = ?' : ' WHERE risk_level = ?';
                whereParams.push(filters.risk_level);
            }

            // 查询数据
            const sql = `SELECT * FROM risk_keywords ${whereClause} ORDER BY risk_level DESC, keyword ASC LIMIT ? OFFSET ?`;
            const queryParams = [...whereParams, limit, offset];
            const keywords = await this.db.query(sql, queryParams);

            // 获取总数
            const countSql = `SELECT COUNT(*) as total FROM risk_keywords ${whereClause}`;
            const countResult = await this.db.query(countSql, whereParams);
            const total = countResult[0].total;

            return {
                keywords,
                pagination: {
                    total,
                    page,
                    limit,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            console.error('获取风控关键词失败:', error);
            throw error;
        }
    }

    /**
     * 获取所有用户
     */
    async getAllUsers() {
        const sql = 'SELECT id, username, email, role, status, last_login, created_at, updated_at, totp_secret FROM users ORDER BY created_at DESC';
        return await this.db.query(sql);
    }

    /**
     * 根据用户名获取用户
     */
    async getUserByUsername(username) {
        const sql = 'SELECT * FROM users WHERE username = ?';
        const result = await this.db.query(sql, [username]);
        return result.length > 0 ? result[0] : null;
    }

    /**
     * 根据ID获取用户
     */
    async getUserById(id) {
        const sql = 'SELECT * FROM users WHERE id = ?';
        const result = await this.db.query(sql, [id]);
        return result.length > 0 ? result[0] : null;
    }

    /**
     * 更新用户密码
     */
    async updateUserPassword(userId, newPassword) {
        try {
            const hashedPassword = await bcrypt.hash(newPassword, 10);
            const sql = 'UPDATE users SET password = ?, updated_at = NOW() WHERE id = ?';
            await this.db.run(sql, [hashedPassword, userId]);
            return true;
        } catch (error) {
            console.error('更新用户密码失败:', error);
            throw error;
        }
    }

    /**
     * 删除用户
     */
    async deleteUser(userId) {
        try {
            const sql = 'DELETE FROM users WHERE id = ?';
            await this.db.run(sql, [userId]);
            return true;
        } catch (error) {
            console.error('删除用户失败:', error);
            throw error;
        }
    }

    /**
     * 清除用户的TOTP密钥
     */
    async clearUserTotpSecret(userId) {
        try {
            const sql = 'UPDATE users SET totp_secret = NULL, updated_at = NOW() WHERE id = ?';
            await this.db.run(sql, [userId]);
            return true;
        } catch (error) {
            console.error('清除用户TOTP密钥失败:', error);
            throw error;
        }
    }

    /**
     * 记录未授权访问日志
     */
    async logUnauthorizedAccess(logData) {
        const {
            ip, endpoint, method, user_agent, request_body, reason = '未授权访问'
        } = logData;

        const sql = `INSERT INTO unauthorized_logs (ip, endpoint, method, user_agent, request_body, reason)
            VALUES (?, ?, ?, ?, ?, ?)`;

        const params = [
            ip || null,
            endpoint || null,
            method || null,
            user_agent || null,
            request_body || null,
            reason
        ];

        try {
            await this.db.run(sql, params);
            console.log(`📝 记录未授权访问: ${ip} -> ${method} ${endpoint}`.yellow);
        } catch (error) {
            console.error('记录未授权访问日志失败:', error);
        }
    }

    /**
     * 获取未授权访问日志
     */
    async getUnauthorizedLogs(filters = {}, pagination = { page: 1, limit: 50 }) {
        try {
            // 确保参数正确
            const page = parseInt(pagination.page) || 1;
            const limit = parseInt(pagination.limit) || 50;
            const offset = (page - 1) * limit;

            let whereClause = 'WHERE 1=1';
            const whereParams = [];

            // 处理过滤条件，确保不传递undefined
            if (filters && typeof filters === 'object') {
                if (filters.ip) {
                    whereClause += ' AND ip = ?';
                    whereParams.push(filters.ip);
                }
                if (filters.startDate) {
                    whereClause += ' AND attempt_time >= ?';
                    whereParams.push(filters.startDate);
                }
                if (filters.endDate) {
                    whereClause += ' AND attempt_time <= ?';
                    whereParams.push(filters.endDate);
                }
            }

            // 查询数据
            const sql = `
                SELECT * FROM unauthorized_logs
                ${whereClause}
                ORDER BY attempt_time DESC
                LIMIT ? OFFSET ?
            `;
            const queryParams = [...whereParams, limit, offset];
            const logs = await this.db.query(sql, queryParams);

            // 获取总数
            const countSql = `SELECT COUNT(*) as total FROM unauthorized_logs ${whereClause}`;
            const countResult = await this.db.query(countSql, whereParams);
            const total = countResult[0].total;

            return {
                logs,
                pagination: {
                    total,
                    page,
                    limit,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            console.error('获取未授权访问日志失败:', error);
            throw error;
        }
    }

    /**
     * 获取风控统计数据
     */
    async getRiskControlStats() {
        try {
            // 获取关键词统计
            const keywordsSql = `
                SELECT
                    COUNT(*) as total_keywords,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_keywords
                FROM risk_keywords
            `;
            const keywordsResult = await this.db.query(keywordsSql);
            const keywordsStats = keywordsResult[0] || { total_keywords: 0, active_keywords: 0 };

            // 获取风控记录统计
            const recordsSql = `
                SELECT
                    COUNT(*) as total_records,
                    SUM(CASE WHEN status = 'auto_blocked' THEN 1 ELSE 0 END) as blocked_count,
                    SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_count,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_records,
                    COUNT(DISTINCT ip_address) as unique_ips,
                    COUNT(DISTINCT uin) as unique_uins
                FROM risk_records
                WHERE created_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            `;
            const recordsResult = await this.db.query(recordsSql);
            const recordsStats = recordsResult[0] || {
                total_records: 0,
                blocked_count: 0,
                rejected_count: 0,
                pending_records: 0,
                unique_ips: 0,
                unique_uins: 0
            };

            // 获取黑名单统计
            const blacklistSql = `
                SELECT
                    COUNT(*) as total_blacklist,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_blacklist
                FROM blacklist_records
            `;
            const blacklistResult = await this.db.query(blacklistSql);
            const blacklistStats = blacklistResult[0] || { total_blacklist: 0, active_blacklist: 0 };

            // 获取白名单统计
            const whitelistSql = `
                SELECT
                    COUNT(*) as total_whitelist,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_whitelist
                FROM whitelist_records
            `;
            const whitelistResult = await this.db.query(whitelistSql);
            const whitelistStats = whitelistResult[0] || { total_whitelist: 0, active_whitelist: 0 };

            return {
                keywords: {
                    total_keywords: keywordsStats.total_keywords,
                    active_keywords: keywordsStats.active_keywords
                },
                records: {
                    total_records: recordsStats.total_records,
                    blocked_count: recordsStats.blocked_count,
                    rejected_count: recordsStats.rejected_count,
                    pending_records: recordsStats.pending_records,
                    unique_ips: recordsStats.unique_ips,
                    unique_uins: recordsStats.unique_uins
                },
                blacklist: {
                    total_blacklist: blacklistStats.total_blacklist,
                    active_blacklist: blacklistStats.active_blacklist
                },
                whitelist: {
                    total_whitelist: whitelistStats.total_whitelist,
                    active_whitelist: whitelistStats.active_whitelist
                }
            };
        } catch (error) {
            console.error('获取风控统计失败:', error);
            throw error;
        }
    }

    /**
     * 获取风控规则
     */
    async getRiskRules() {
        try {
            const sql = 'SELECT * FROM risk_keywords WHERE is_active = 1 ORDER BY risk_level DESC, keyword ASC';
            return await this.db.query(sql);
        } catch (error) {
            console.error('获取风控规则失败:', error);
            throw error;
        }
    }

    /**
     * 更新风控规则
     */
    async updateRiskRules(rules) {
        try {
            // 这里可以根据具体需求实现规则更新逻辑
            // 暂时返回成功
            return true;
        } catch (error) {
            console.error('更新风控规则失败:', error);
            throw error;
        }
    }

    /**
     * 初始化风控配置表
     */
    async initRiskControlSettingsTable() {
        try {
            // 创建表
            const createTableSql = `
                CREATE TABLE IF NOT EXISTS \`risk_control_settings\` (
                  \`id\` int(11) NOT NULL AUTO_INCREMENT,
                  \`setting_key\` varchar(100) NOT NULL COMMENT '配置键名',
                  \`setting_value\` text COMMENT '配置值',
                  \`setting_type\` enum('boolean','string','number','json') DEFAULT 'string' COMMENT '配置类型',
                  \`description\` varchar(255) DEFAULT NULL COMMENT '配置描述',
                  \`category\` varchar(50) DEFAULT 'general' COMMENT '配置分类',
                  \`is_enabled\` tinyint(1) DEFAULT 1 COMMENT '是否启用',
                  \`created_time\` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                  \`updated_time\` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                  PRIMARY KEY (\`id\`),
                  UNIQUE KEY \`uk_setting_key\` (\`setting_key\`),
                  KEY \`idx_category\` (\`category\`),
                  KEY \`idx_enabled\` (\`is_enabled\`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风控系统配置表'
            `;

            await this.db.run(createTableSql);

            // 插入默认配置
            const defaultSettings = [
                ['enable_risk_detection', 'true', 'boolean', '是否启用风控检测', 'detection'],
                ['enable_keyword_filter', 'true', 'boolean', '是否启用关键词过滤', 'detection'],
                ['enable_ip_blacklist', 'true', 'boolean', '是否启用IP黑名单', 'blacklist'],
                ['enable_user_blacklist', 'true', 'boolean', '是否启用用户黑名单', 'blacklist'],
                ['enable_auto_block', 'true', 'boolean', '是否启用自动拦截', 'action'],
                ['enable_whitelist_bypass', 'true', 'boolean', '是否启用白名单绕过', 'whitelist'],
                ['max_risk_score', '80', 'number', '最大风险评分阈值', 'threshold'],
                ['auto_block_threshold', '70', 'number', '自动拦截阈值', 'threshold'],
                ['batch_operation_limit', '100', 'number', '批量操作限制数量', 'batch'],
                ['enable_batch_review', 'true', 'boolean', '是否启用批量审核', 'batch'],
                ['enable_batch_delete', 'true', 'boolean', '是否启用批量删除', 'batch'],
                ['enable_batch_blacklist', 'true', 'boolean', '是否启用批量加入黑名单', 'batch']
            ];

            for (const [key, value, type, description, category] of defaultSettings) {
                const insertSql = `
                    INSERT IGNORE INTO risk_control_settings
                    (setting_key, setting_value, setting_type, description, category)
                    VALUES (?, ?, ?, ?, ?)
                `;
                await this.db.run(insertSql, [key, value, type, description, category]);
            }

            console.log('✅ 风控配置表初始化完成');
            return true;
        } catch (error) {
            console.error('初始化风控配置表失败:', error);
            throw error;
        }
    }

    /**
     * 获取风控系统配置
     */
    async getRiskControlSettings(category = null) {
        try {
            // 先尝试初始化表（如果不存在）
            await this.initRiskControlSettingsTable();

            let sql = 'SELECT * FROM risk_control_settings WHERE is_enabled = 1';
            const params = [];

            if (category) {
                sql += ' AND category = ?';
                params.push(category);
            }

            sql += ' ORDER BY category, setting_key';

            const settings = await this.db.query(sql, params);

            // 转换为键值对格式
            const result = {};
            settings.forEach(setting => {
                let value = setting.setting_value;

                // 根据类型转换值
                switch (setting.setting_type) {
                    case 'boolean':
                        value = value === 'true' || value === '1';
                        break;
                    case 'number':
                        value = parseFloat(value);
                        break;
                    case 'json':
                        try {
                            value = JSON.parse(value);
                        } catch (e) {
                            console.error('解析JSON配置失败:', setting.setting_key, value);
                        }
                        break;
                }

                result[setting.setting_key] = {
                    value,
                    type: setting.setting_type,
                    description: setting.description,
                    category: setting.category
                };
            });

            return result;
        } catch (error) {
            console.error('获取风控配置失败:', error);
            // 如果还是失败，返回默认配置
            return this.getDefaultRiskSettings();
        }
    }

    /**
     * 获取默认风控配置
     */
    getDefaultRiskSettings() {
        return {
            enable_risk_detection: { value: true, type: 'boolean', description: '是否启用风控检测', category: 'detection' },
            enable_keyword_filter: { value: true, type: 'boolean', description: '是否启用关键词过滤', category: 'detection' },
            enable_ip_blacklist: { value: true, type: 'boolean', description: '是否启用IP黑名单', category: 'blacklist' },
            enable_user_blacklist: { value: true, type: 'boolean', description: '是否启用用户黑名单', category: 'blacklist' },
            enable_auto_block: { value: true, type: 'boolean', description: '是否启用自动拦截', category: 'action' },
            enable_whitelist_bypass: { value: true, type: 'boolean', description: '是否启用白名单绕过', category: 'whitelist' },
            max_risk_score: { value: 80, type: 'number', description: '最大风险评分阈值', category: 'threshold' },
            auto_block_threshold: { value: 70, type: 'number', description: '自动拦截阈值', category: 'threshold' },
            batch_operation_limit: { value: 100, type: 'number', description: '批量操作限制数量', category: 'batch' },
            enable_batch_review: { value: true, type: 'boolean', description: '是否启用批量审核', category: 'batch' },
            enable_batch_delete: { value: true, type: 'boolean', description: '是否启用批量删除', category: 'batch' },
            enable_batch_blacklist: { value: true, type: 'boolean', description: '是否启用批量加入黑名单', category: 'batch' }
        };
    }

    /**
     * 更新风控系统配置
     */
    async updateRiskControlSetting(settingKey, settingValue, settingType = 'string') {
        try {
            // 先确保表存在
            await this.initRiskControlSettingsTable();

            const sql = `
                INSERT INTO risk_control_settings (setting_key, setting_value, setting_type, is_enabled)
                VALUES (?, ?, ?, 1)
                ON DUPLICATE KEY UPDATE
                setting_value = VALUES(setting_value),
                setting_type = VALUES(setting_type),
                updated_time = NOW()
            `;

            await this.db.run(sql, [settingKey, settingValue.toString(), settingType]);
            return true;
        } catch (error) {
            console.error('更新风控配置失败:', error);
            throw error;
        }
    }

    /**
     * 批量审核风控记录
     */
    async batchReviewRiskRecords(recordIds, reviewData) {
        try {
            const { status, reviewer_id, review_reason } = reviewData;

            if (!Array.isArray(recordIds) || recordIds.length === 0) {
                throw new Error('记录ID列表不能为空');
            }

            const placeholders = recordIds.map(() => '?').join(',');
            const sql = `
                UPDATE risk_records
                SET status = ?, reviewer_id = ?, review_reason = ?, review_time = NOW()
                WHERE id IN (${placeholders})
            `;

            const params = [status, reviewer_id, review_reason, ...recordIds];
            const result = await this.db.run(sql, params);

            return {
                success: true,
                affectedRows: result.affectedRows,
                updatedIds: recordIds
            };
        } catch (error) {
            console.error('批量审核风控记录失败:', error);
            throw error;
        }
    }

    /**
     * 批量删除风控记录
     */
    async batchDeleteRiskRecords(recordIds) {
        try {
            if (!Array.isArray(recordIds) || recordIds.length === 0) {
                throw new Error('记录ID列表不能为空');
            }

            const placeholders = recordIds.map(() => '?').join(',');
            const sql = `DELETE FROM risk_records WHERE id IN (${placeholders})`;

            const result = await this.db.run(sql, recordIds);

            return {
                success: true,
                deletedCount: result.affectedRows,
                deletedIds: recordIds
            };
        } catch (error) {
            console.error('批量删除风控记录失败:', error);
            throw error;
        }
    }

    /**
     * 批量加入黑名单
     */
    async batchAddToBlacklist(records, banData) {
        try {
            if (!Array.isArray(records) || records.length === 0) {
                throw new Error('记录列表不能为空');
            }

            const { ban_reason, ban_type = 'permanent', ban_duration, creator_id } = banData;
            const results = [];

            for (const record of records) {
                try {
                    const blacklistData = {
                        username: record.username,
                        device_id: record.device_id,
                        ip_address: record.ip_address,
                        uin: record.uin,
                        risk_content: record.risk_content || '批量加入黑名单',
                        ban_reason: ban_reason || '批量风控处理',
                        ban_type,
                        ban_duration,
                        creator_id
                    };

                    const blacklistId = await this.addToBlacklist(blacklistData);
                    results.push({
                        recordId: record.id,
                        blacklistId,
                        success: true
                    });
                } catch (error) {
                    results.push({
                        recordId: record.id,
                        success: false,
                        error: error.message
                    });
                }
            }

            const successCount = results.filter(r => r.success).length;
            const failCount = results.length - successCount;

            return {
                success: true,
                total: results.length,
                successCount,
                failCount,
                results
            };
        } catch (error) {
            console.error('批量加入黑名单失败:', error);
            throw error;
        }
    }

    /**
     * 获取黑名单
     */
    async getBlacklist(filters = {}, pagination = { page: 1, limit: 20 }) {
        try {
            const { page, limit } = pagination;
            const offset = (page - 1) * limit;

            let whereClause = 'WHERE 1=1';
            const whereParams = [];

            // 支持通过ID查询单条记录
            if (filters.id) {
                whereClause += ' AND id = ?';
                whereParams.push(filters.id);
            }

            if (filters.username) {
                whereClause += ' AND username LIKE ?';
                whereParams.push(`%${filters.username}%`);
            }

            if (filters.device_id) {
                whereClause += ' AND device_id = ?';
                whereParams.push(filters.device_id);
            }

            if (filters.ip_address) {
                whereClause += ' AND ip_address = ?';
                whereParams.push(filters.ip_address);
            }

            if (filters.uin) {
                whereClause += ' AND uin = ?';
                whereParams.push(filters.uin);
            }

            if (filters.ban_type) {
                whereClause += ' AND ban_type = ?';
                whereParams.push(filters.ban_type);
            }

            if (filters.is_active !== undefined) {
                whereClause += ' AND is_active = ?';
                whereParams.push(filters.is_active ? 1 : 0);
            }

            // 查询数据
            const sql = `SELECT * FROM blacklist_records ${whereClause} ORDER BY created_time DESC LIMIT ? OFFSET ?`;
            const queryParams = [...whereParams, limit, offset];
            const blacklist = await this.db.query(sql, queryParams);

            // 获取总数
            const countSql = `SELECT COUNT(*) as total FROM blacklist_records ${whereClause}`;
            const countResult = await this.db.query(countSql, whereParams);
            const total = countResult[0].total;

            return {
                blacklist,
                pagination: {
                    total,
                    page,
                    limit,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            console.error('获取黑名单失败:', error);
            throw error;
        }
    }

    /**
     * 添加到黑名单
     */
    async addToBlacklist(data, value = null, reason = '') {
        try {
            let insertData;

            // 支持两种调用方式
            if (typeof data === 'object' && data !== null) {
                // 新的调用方式：addToBlacklist(data)
                insertData = {
                    username: data.username || null,
                    device_id: data.device_id || null,
                    ip_address: data.ip_address || null,
                    uin: data.uin || null,
                    risk_content: data.risk_content || '',
                    ban_reason: data.ban_reason || '手动添加',
                    ban_type: data.ban_type || 'permanent',
                    ban_end_time: data.ban_end_time || null,
                    is_active: data.is_active !== undefined ? (data.is_active ? 1 : 0) : 1
                };

                const sql = `
                    INSERT INTO blacklist_records
                    (username, device_id, ip_address, uin, risk_content, ban_reason, ban_type, ban_end_time, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                `;
                const result = await this.db.run(sql, [
                    insertData.username,
                    insertData.device_id,
                    insertData.ip_address,
                    insertData.uin,
                    insertData.risk_content,
                    insertData.ban_reason,
                    insertData.ban_type,
                    insertData.ban_end_time,
                    insertData.is_active
                ]);

                return {
                    success: true,
                    id: result.insertId
                };
            } else {
                // 旧的调用方式：addToBlacklist(type, value, reason)
                const sql = 'INSERT INTO blacklist (type, value, reason) VALUES (?, ?, ?)';
                const result = await this.db.run(sql, [data, value, reason]);
                return {
                    success: true,
                    id: result.insertId
                };
            }
        } catch (error) {
            console.error('添加到黑名单失败:', error);
            throw error;
        }
    }

    /**
     * 从黑名单移除
     */
    async removeFromBlacklist(id) {
        try {
            const sql = 'DELETE FROM blacklist_records WHERE id = ?';
            const result = await this.db.run(sql, [id]);
            return result.affectedRows > 0;
        } catch (error) {
            console.error('从黑名单移除失败:', error);
            throw error;
        }
    }

    /**
     * 获取白名单
     */
    async getWhitelist(filters = {}, pagination = { page: 1, limit: 20 }) {
        try {
            const { page, limit } = pagination;
            const offset = (page - 1) * limit;

            let whereClause = 'WHERE 1=1';
            const whereParams = [];

            if (filters.username) {
                whereClause += ' AND username LIKE ?';
                whereParams.push(`%${filters.username}%`);
            }

            if (filters.ip_address) {
                whereClause += ' AND ip_address = ?';
                whereParams.push(filters.ip_address);
            }

            if (filters.uin) {
                whereClause += ' AND uin = ?';
                whereParams.push(filters.uin);
            }

            if (filters.whitelist_type) {
                whereClause += ' AND whitelist_type = ?';
                whereParams.push(filters.whitelist_type);
            }

            if (filters.is_active !== undefined) {
                whereClause += ' AND is_active = ?';
                whereParams.push(filters.is_active ? 1 : 0);
            }

            // 查询数据
            const sql = `SELECT * FROM whitelist_records ${whereClause} ORDER BY created_time DESC LIMIT ? OFFSET ?`;
            const queryParams = [...whereParams, limit, offset];
            const whitelist = await this.db.query(sql, queryParams);

            // 获取总数
            const countSql = `SELECT COUNT(*) as total FROM whitelist_records ${whereClause}`;
            const countResult = await this.db.query(countSql, whereParams);
            const total = countResult[0].total;

            return {
                whitelist,
                pagination: {
                    total,
                    page,
                    limit,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            console.error('获取白名单失败:', error);
            throw error;
        }
    }

    /**
     * 添加到白名单
     */
    async addToWhitelist(data) {
        try {
            const { username, device_id, ip_address, uin, whitelist_type, description, reason, is_active = true } = data;

            const sql = `
                INSERT INTO whitelist_records
                (username, device_id, ip_address, uin, whitelist_type, description, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            `;
            const result = await this.db.run(sql, [
                username || null,
                device_id || null,
                ip_address || null,
                uin || null,
                whitelist_type || 'comprehensive',
                description || reason || '',
                is_active ? 1 : 0
            ]);

            return {
                success: true,
                id: result.insertId
            };
        } catch (error) {
            console.error('添加到白名单失败:', error);
            throw error;
        }
    }

    /**
     * 从白名单移除
     */
    async removeFromWhitelist(id) {
        try {
            const sql = 'DELETE FROM whitelist_records WHERE id = ?';
            const result = await this.db.run(sql, [id]);
            return result.affectedRows > 0;
        } catch (error) {
            console.error('从白名单移除失败:', error);
            throw error;
        }
    }

    /**
     * 更新黑名单记录
     */
    async updateBlacklist(id, data) {
        try {
            const { username, device_id, ip_address, uin, risk_content, ban_reason, ban_type, ban_end_time, is_active } = data;

            const sql = `
                UPDATE blacklist_records
                SET username = ?, device_id = ?, ip_address = ?, uin = ?,
                    risk_content = ?, ban_reason = ?, ban_type = ?, ban_end_time = ?,
                    is_active = ?, updated_time = NOW()
                WHERE id = ?
            `;

            const result = await this.db.run(sql, [
                username || null,
                device_id || null,
                ip_address || null,
                uin || null,
                risk_content || '',
                ban_reason || '',
                ban_type || 'permanent',
                ban_end_time || null,
                is_active !== undefined ? (is_active ? 1 : 0) : 1,
                id
            ]);

            return {
                success: result.affectedRows > 0,
                affectedRows: result.affectedRows
            };
        } catch (error) {
            console.error('更新黑名单失败:', error);
            throw error;
        }
    }

    /**
     * 更新白名单记录
     */
    async updateWhitelist(id, data) {
        try {
            const { username, device_id, ip_address, uin, whitelist_type, description, is_active } = data;

            const sql = `
                UPDATE whitelist_records
                SET username = ?, device_id = ?, ip_address = ?, uin = ?,
                    whitelist_type = ?, description = ?, is_active = ?, updated_time = NOW()
                WHERE id = ?
            `;

            const result = await this.db.run(sql, [
                username || null,
                device_id || null,
                ip_address || null,
                uin || null,
                whitelist_type || 'comprehensive',
                description || '',
                is_active !== undefined ? (is_active ? 1 : 0) : 1,
                id
            ]);

            return {
                success: result.affectedRows > 0,
                affectedRows: result.affectedRows
            };
        } catch (error) {
            console.error('更新白名单失败:', error);
            throw error;
        }
    }

    /**
     * 获取管理员凭据
     */
    async getAdminCredentials() {
        try {
            const sql = 'SELECT username, role FROM users WHERE role = "admin" ORDER BY created_at ASC';
            return await this.db.query(sql);
        } catch (error) {
            console.error('获取管理员凭据失败:', error);
            throw error;
        }
    }

    /**
     * 获取风控记录
     */
    async getRiskRecords(filters = {}, pagination = { page: 1, limit: 50 }) {
        try {
            const { page, limit } = pagination;
            const offset = (page - 1) * limit;

            let whereClause = 'WHERE 1=1';
            const whereParams = [];

            if (filters.status) {
                whereClause += ' AND status = ?';
                whereParams.push(filters.status);
            }

            if (filters.risk_level) {
                whereClause += ' AND risk_level = ?';
                whereParams.push(filters.risk_level);
            }

            if (filters.ip_address) {
                whereClause += ' AND ip_address = ?';
                whereParams.push(filters.ip_address);
            }

            if (filters.uin) {
                whereClause += ' AND uin = ?';
                whereParams.push(filters.uin);
            }

            if (filters.startDate) {
                whereClause += ' AND created_time >= ?';
                whereParams.push(filters.startDate);
            }

            if (filters.endDate) {
                whereClause += ' AND created_time <= ?';
                whereParams.push(filters.endDate);
            }

            // 查询数据
            const sql = `SELECT * FROM risk_records ${whereClause} ORDER BY created_time DESC LIMIT ? OFFSET ?`;
            const queryParams = [...whereParams, limit, offset];
            const rawRecords = await this.db.query(sql, queryParams);

            // 处理 JSON 字段
            const records = rawRecords.map(record => {
                try {
                    // 解析 matched_keywords JSON 字段
                    if (record.matched_keywords && typeof record.matched_keywords === 'string') {
                        record.matched_keywords = JSON.parse(record.matched_keywords);
                    }

                    // 解析 client_info JSON 字段
                    if (record.client_info && typeof record.client_info === 'string') {
                        record.client_info = JSON.parse(record.client_info);
                    }
                } catch (error) {
                    console.error('解析风控记录JSON字段失败:', error, 'Record ID:', record.id);
                    // 如果解析失败，保持原值
                }
                return record;
            });

            // 获取总数
            const countSql = `SELECT COUNT(*) as total FROM risk_records ${whereClause}`;
            const countResult = await this.db.query(countSql, whereParams);
            const total = countResult[0].total;

            return {
                records,
                pagination: {
                    total,
                    page,
                    limit,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            console.error('获取风控记录失败:', error);
            throw error;
        }
    }

    /**
     * 根据ID获取单个风控记录详情
     */
    async getRiskRecordById(id) {
        try {
            const sql = 'SELECT * FROM risk_records WHERE id = ?';
            const result = await this.db.query(sql, [id]);

            if (result.length === 0) {
                return null;
            }

            const record = result[0];

            // 处理 JSON 字段
            try {
                // 解析 matched_keywords JSON 字段
                if (record.matched_keywords && typeof record.matched_keywords === 'string') {
                    record.matched_keywords = JSON.parse(record.matched_keywords);
                }

                // 解析 client_info JSON 字段
                if (record.client_info && typeof record.client_info === 'string') {
                    record.client_info = JSON.parse(record.client_info);
                }

                // 解析 request_data JSON 字段（如果是JSON格式）
                if (record.request_data && typeof record.request_data === 'string') {
                    try {
                        record.request_data = JSON.parse(record.request_data);
                    } catch (e) {
                        // 如果不是JSON格式，保持原值
                    }
                }
            } catch (error) {
                console.error('解析风控记录JSON字段失败:', error, 'Record ID:', record.id);
                // 如果解析失败，保持原值
            }

            return record;
        } catch (error) {
            console.error('获取风控记录详情失败:', error);
            throw error;
        }
    }

    /**
     * 审核风控记录
     */
    async reviewRiskRecord(recordId, reviewData) {
        try {
            const { status, reviewer_id, review_reason, ban_type, ban_duration } = reviewData;

            // 更新风控记录状态
            const updateSql = `
                UPDATE risk_records
                SET status = ?, reviewer_id = ?, review_reason = ?, review_time = NOW()
                WHERE id = ?
            `;
            await this.db.run(updateSql, [status, reviewer_id, review_reason, recordId]);

            // 如果审核通过，添加到黑名单
            if (status === 'approved') {
                const recordSql = 'SELECT * FROM risk_records WHERE id = ?';
                const records = await this.db.query(recordSql, [recordId]);

                if (records.length > 0) {
                    const record = records[0];

                    // 计算封禁结束时间
                    let banEndTime = null;
                    if (ban_type === 'temporary' && ban_duration) {
                        const endTime = new Date();
                        endTime.setHours(endTime.getHours() + parseInt(ban_duration));
                        banEndTime = endTime;
                    }

                    // 添加到黑名单
                    const blacklistSql = `
                        INSERT INTO blacklist_records
                        (username, device_id, ip_address, uin, risk_content, ban_reason, ban_type, ban_end_time, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
                    `;
                    await this.db.run(blacklistSql, [
                        record.username,
                        record.device_id,
                        record.ip_address,
                        record.uin,
                        record.risk_content,
                        review_reason || '风控审核通过',
                        ban_type || 'permanent',
                        banEndTime
                    ]);
                }
            }

            return true;
        } catch (error) {
            console.error('审核风控记录失败:', error);
            throw error;
        }
    }

    /**
     * 获取UIN统计
     */
    async getUINStats(limit = 20) {
        try {
            const sql = `
                SELECT
                    uin,
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests,
                    MAX(request_time) as last_request_time
                FROM sign_logs
                WHERE uin IS NOT NULL AND uin != ''
                GROUP BY uin
                ORDER BY total_requests DESC
                LIMIT ?
            `;
            return await this.db.query(sql, [limit]);
        } catch (error) {
            console.error('获取UIN统计失败:', error);
            throw error;
        }
    }

    /**
     * 获取小时统计（今日）
     */
    async getHourlyStats() {
        try {
            const sql = `
                SELECT
                    HOUR(request_time) as hour,
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests
                FROM sign_logs
                WHERE DATE(request_time) = CURDATE()
                GROUP BY HOUR(request_time)
                ORDER BY hour
            `;
            const result = await this.db.query(sql);

            // 填充0-23小时的数据
            const hourlyData = Array.from({ length: 24 }, (_, hour) => {
                const found = result.find(item => item.hour === hour);
                return {
                    hour,
                    total_requests: found ? found.total_requests : 0,
                    successful_requests: found ? found.successful_requests : 0,
                    failed_requests: found ? (found.total_requests - found.successful_requests) : 0
                };
            });

            return hourlyData;
        } catch (error) {
            console.error('获取小时统计失败:', error);
            throw error;
        }
    }

    /**
     * 获取总体统计概览
     */
    async getOverviewStats() {
        try {
            // 获取今日统计
            const todayStats = await this.getTodayOverview();

            // 获取总体统计
            const totalSql = `
                SELECT
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as success_requests,
                    COUNT(DISTINCT ip) as unique_ips,
                    COUNT(DISTINCT uin) as unique_uins,
                    AVG(response_time_ms) as avg_response_time
                FROM sign_logs
            `;
            const totalResult = await this.db.query(totalSql);
            const totalStats = totalResult[0];

            const total = {
                total_requests: totalStats.total_requests || 0,
                success_requests: totalStats.success_requests || 0,
                failed_requests: (totalStats.total_requests || 0) - (totalStats.success_requests || 0),
                success_rate: totalStats.total_requests ?
                    ((totalStats.success_requests || 0) / totalStats.total_requests * 100).toFixed(2) : '0.00',
                unique_ips: totalStats.unique_ips || 0,
                unique_uins: totalStats.unique_uins || 0,
                avg_response_time: Math.round(totalStats.avg_response_time || 0)
            };

            return {
                today: todayStats,
                total: total
            };
        } catch (error) {
            console.error('获取总体统计失败:', error);
            throw error;
        }
    }

    /**
     * 添加风控关键词
     */
    async addRiskKeyword(data) {
        try {
            // 支持两种调用方式：对象参数和单独参数
            let keyword, keywordType, riskLevel, description, isActive;

            if (typeof data === 'object') {
                ({ keyword, keyword_type: keywordType, risk_level: riskLevel, description, is_active: isActive } = data);
            } else {
                // 兼容旧的调用方式
                keyword = arguments[0];
                keywordType = arguments[1] || 'strict';
                riskLevel = arguments[2] || 'medium';
                description = arguments[3] || '';
                isActive = true;
            }

            const sql = 'INSERT INTO risk_keywords (keyword, keyword_type, risk_level, description, is_active) VALUES (?, ?, ?, ?, ?)';
            const result = await this.db.run(sql, [
                keyword,
                keywordType || 'strict',
                riskLevel || 'medium',
                description || '',
                isActive !== undefined ? (isActive ? 1 : 0) : 1
            ]);
            return result.insertId;
        } catch (error) {
            console.error('添加风控关键词失败:', error);
            throw error;
        }
    }

    /**
     * 删除风控关键词
     */
    async deleteRiskKeyword(id) {
        try {
            const sql = 'DELETE FROM risk_keywords WHERE id = ?';
            const result = await this.db.run(sql, [id]);
            return result.affectedRows > 0;
        } catch (error) {
            console.error('删除风控关键词失败:', error);
            return false;
        }
    }

    /**
     * 批量导入风控关键词
     */
    async batchImportRiskKeywords(keywords) {
        try {
            let successCount = 0;
            let errorCount = 0;
            const errors = [];

            for (const keyword of keywords) {
                try {
                    await this.addRiskKeyword(keyword);
                    successCount++;
                } catch (error) {
                    errorCount++;
                    errors.push({
                        keyword: keyword.keyword,
                        error: error.message
                    });
                }
            }

            return {
                successCount,
                errorCount,
                errors
            };
        } catch (error) {
            console.error('批量导入风控关键词失败:', error);
            throw error;
        }
    }

    /**
     * 检查风控内容
     */
    async checkRiskContent(content, clientInfo = {}) {
        try {
            // 获取所有活跃的风控关键词
            const keywordsResult = await this.getRiskKeywords({ is_active: true }, { page: 1, limit: 10000 });
            const keywords = keywordsResult.keywords || [];

            let isRisk = false;
            let riskScore = 0;
            let riskLevel = 'low';
            let matchedKeywords = [];
            let autoBlock = false;

            // 检查每个关键词
            for (const keyword of keywords) {
                let matched = false;

                switch (keyword.keyword_type) {
                    case 'strict':
                        matched = content.includes(keyword.keyword);
                        break;
                    case 'fuzzy':
                        matched = content.toLowerCase().includes(keyword.keyword.toLowerCase());
                        break;
                    case 'regex':
                        try {
                            const regex = new RegExp(keyword.keyword, 'i');
                            matched = regex.test(content);
                        } catch (e) {
                            // 正则表达式错误，跳过
                            continue;
                        }
                        break;
                }

                if (matched) {
                    isRisk = true;
                    matchedKeywords.push(keyword);

                    // 计算风险分数
                    const levelScores = {
                        low: 10,
                        medium: 30,
                        high: 60,
                        critical: 100
                    };
                    riskScore += levelScores[keyword.risk_level] || 10;
                }
            }

            // 确定最终风险等级
            if (riskScore >= 100) {
                riskLevel = 'critical';
                autoBlock = true;
            } else if (riskScore >= 60) {
                riskLevel = 'high';
                autoBlock = true;
            } else if (riskScore >= 30) {
                riskLevel = 'medium';
            } else if (riskScore > 0) {
                riskLevel = 'low';
            }

            return {
                isRisk,
                riskScore: Math.min(riskScore, 100), // 最大100分
                riskLevel,
                matchedKeywords,
                autoBlock
            };
        } catch (error) {
            console.error('检查风控内容失败:', error);
            throw error;
        }
    }

    /**
     * 检查是否在白名单中
     */
    async isWhitelisted(clientInfo) {
        try {
            const { username, device_id, ip_address, uin } = clientInfo || {};

            // 将undefined转换为null
            const safeUsername = username || null;
            const safeDeviceId = device_id || null;
            const safeIpAddress = ip_address || null;
            const safeUin = uin || null;

            const sql = `
                SELECT * FROM whitelist_records
                WHERE is_active = 1 AND (
                    (whitelist_type = 'username' AND username = ?) OR
                    (whitelist_type = 'device' AND device_id = ?) OR
                    (whitelist_type = 'ip' AND ip_address = ?) OR
                    (whitelist_type = 'uin' AND uin = ?) OR
                    (whitelist_type = 'comprehensive' AND (
                        username = ? OR device_id = ? OR ip_address = ? OR uin = ?
                    ))
                )
                LIMIT 1
            `;

            const result = await this.db.query(sql, [
                safeUsername, safeDeviceId, safeIpAddress, safeUin,
                safeUsername, safeDeviceId, safeIpAddress, safeUin
            ]);

            return result.length > 0;
        } catch (error) {
            console.error('检查白名单失败:', error);
            return false;
        }
    }

    /**
     * 检查是否在黑名单中
     */
    async isBlacklisted(clientInfo) {
        try {
            const { username, device_id, ip_address, uin } = clientInfo || {};

            // 将undefined转换为null
            const safeUsername = username || null;
            const safeDeviceId = device_id || null;
            const safeIpAddress = ip_address || null;
            const safeUin = uin || null;

            const sql = `
                SELECT * FROM blacklist_records
                WHERE is_active = 1 AND (
                    username = ? OR device_id = ? OR ip_address = ? OR uin = ?
                ) AND (
                    ban_type = 'permanent' OR
                    (ban_type = 'temporary' AND ban_end_time > NOW())
                )
                LIMIT 1
            `;

            const result = await this.db.query(sql, [safeUsername, safeDeviceId, safeIpAddress, safeUin]);

            return {
                isBlacklisted: result.length > 0,
                record: result.length > 0 ? result[0] : null
            };
        } catch (error) {
            console.error('检查黑名单失败:', error);
            return { isBlacklisted: false, record: null };
        }
    }

    /**
     * 清理旧日志（数据库清理功能）
     */
    async cleanOldLogs(days = 7) {
        try {
            console.log(`🧹 开始清理 ${days} 天前的日志数据...`.yellow);

            // 清理签名日志
            const signLogsSql = 'DELETE FROM sign_logs WHERE request_time < DATE_SUB(NOW(), INTERVAL ? DAY)';
            const signLogsResult = await this.db.run(signLogsSql, [days]);
            console.log(`✅ 清理签名日志: ${signLogsResult.affectedRows} 条`.green);

            // 清理风控记录
            const riskRecordsSql = 'DELETE FROM risk_records WHERE created_time < DATE_SUB(NOW(), INTERVAL ? DAY)';
            const riskRecordsResult = await this.db.run(riskRecordsSql, [days]);
            console.log(`✅ 清理风控记录: ${riskRecordsResult.affectedRows} 条`.green);

            // 清理未授权访问日志
            const unauthorizedLogsSql = 'DELETE FROM unauthorized_logs WHERE attempt_time < DATE_SUB(NOW(), INTERVAL ? DAY)';
            const unauthorizedLogsResult = await this.db.run(unauthorizedLogsSql, [days]);
            console.log(`✅ 清理未授权日志: ${unauthorizedLogsResult.affectedRows} 条`.green);

            // 清理消息日志
            const messageLogsSql = 'DELETE FROM message_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)';
            const messageLogsResult = await this.db.run(messageLogsSql, [days]);
            console.log(`✅ 清理消息日志: ${messageLogsResult.affectedRows} 条`.green);

            const totalCleaned = signLogsResult.affectedRows + riskRecordsResult.affectedRows +
                               unauthorizedLogsResult.affectedRows + messageLogsResult.affectedRows;

            console.log(`🎉 数据库清理完成，共清理 ${totalCleaned} 条记录`.green);

            return {
                success: true,
                totalCleaned,
                details: {
                    signLogs: signLogsResult.affectedRows,
                    riskRecords: riskRecordsResult.affectedRows,
                    unauthorizedLogs: unauthorizedLogsResult.affectedRows,
                    messageLogs: messageLogsResult.affectedRows
                }
            };
        } catch (error) {
            console.error('数据库清理失败:'.red, error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// 创建单例实例
const mysqlDataModel = new MySQLDataModel();

module.exports = mysqlDataModel;
