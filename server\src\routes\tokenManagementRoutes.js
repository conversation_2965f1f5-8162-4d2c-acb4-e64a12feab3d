const express = require('express');
const router = express.Router();
const { authenticate } = require('../middleware/auth');
const dataModel = require('../models/mysql-real-model');
const { nodeSelector } = require('../middleware/nodeSelector');

// 获取所有token管理记录
router.get('/tokens', authenticate, async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 20;
        const offset = (page - 1) * limit;

        // 获取所有API配置（包含token信息）
        const allConfigs = await dataModel.getAllApiConfigs();
        
        // 过滤出有token的配置
        const tokenConfigs = allConfigs.filter(config => 
            config.auth_key && config.auth_key.length === 128
        );

        // 分页处理
        const total = tokenConfigs.length;
        const paginatedConfigs = tokenConfigs.slice(offset, offset + limit);

        // 格式化返回数据
        const tokens = paginatedConfigs.map(config => ({
            id: config.id,
            token: config.auth_key,
            tokenPreview: config.auth_key.substring(0, 8) + '...' + config.auth_key.substring(120),
            nodeType: config.client_type,
            baseUrl: config.base_url,
            description: config.description || '',
            status: config.status || 'active',
            tokenMode: config.token_mode || 'exclusive', // 添加token模式
            requestLimit: config.request_limit || 0,
            timeout: config.timeout || 10000,
            createdAt: config.created_at,
            updatedAt: config.updated_at
        }));

        res.json({
            code: 0,
            msg: '获取成功',
            data: {
                tokens,
                pagination: {
                    page,
                    limit,
                    total,
                    totalPages: Math.ceil(total / limit)
                }
            }
        });
    } catch (error) {
        console.error('获取token列表失败:', error);
        res.status(500).json({
            code: 500,
            msg: '获取失败',
            error: error.message
        });
    }
});

// 创建新的token
router.post('/tokens', authenticate, async (req, res) => {
    try {
        console.log('收到创建token请求:', req.body); // 调试日志
        const { selectedNodes, description, shareToken } = req.body;

        if (!selectedNodes || !Array.isArray(selectedNodes) || selectedNodes.length === 0) {
            console.log('节点选择验证失败:', selectedNodes); // 调试日志
            return res.status(400).json({
                code: 400,
                msg: '请选择至少一个节点'
            });
        }

        console.log('开始处理节点:', selectedNodes, '共享模式:', shareToken); // 调试日志

        const results = [];
        const errors = [];

        if (shareToken) {
            // 共享token模式：为所有选中的节点使用同一个token
            const token = dataModel.generateAuthKey();

            for (const nodeType of selectedNodes) {
                try {
                    // 检查节点是否存在
                    const existingConfig = await dataModel.getApiConfig(nodeType);
                    if (!existingConfig) {
                        errors.push(`节点 ${nodeType} 不存在`);
                        continue;
                    }

                    // 检查是否已有128位token
                    if (existingConfig.auth_key && existingConfig.auth_key.length === 128) {
                        errors.push(`节点 ${nodeType} 已有token`);
                        continue;
                    }

                    // 为节点分配共享token
                    await dataModel.setApiConfig(
                        nodeType,
                        existingConfig.base_url,
                        existingConfig.timeout,
                        existingConfig.retry_count,
                        description || existingConfig.description,
                        token, // 使用共享token
                        existingConfig.request_limit,
                        'shared' // 设置为共享模式
                    );

                    results.push({
                        nodeType,
                        token,
                        tokenPreview: token.substring(0, 8) + '...' + token.substring(120),
                        shared: true
                    });

                } catch (error) {
                    errors.push(`节点 ${nodeType} 处理失败: ${error.message}`);
                }
            }

            if (results.length > 0) {
                console.log(`🔗 创建共享token: ${token.substring(0, 8)}... 授权给 ${results.length} 个节点`.green);
            }

        } else {
            // 独立token模式：为每个节点生成独立的token
            for (const nodeType of selectedNodes) {
                try {
                    // 检查节点是否存在
                    const existingConfig = await dataModel.getApiConfig(nodeType);
                    if (!existingConfig) {
                        errors.push(`节点 ${nodeType} 不存在`);
                        continue;
                    }

                    // 检查是否已有128位token
                    if (existingConfig.auth_key && existingConfig.auth_key.length === 128) {
                        errors.push(`节点 ${nodeType} 已有token`);
                        continue;
                    }

                    // 生成独立的128位token
                    const token = dataModel.generateAuthKey();

                    // 更新节点配置，添加token
                    await dataModel.setApiConfig(
                        nodeType,
                        existingConfig.base_url,
                        existingConfig.timeout,
                        existingConfig.retry_count,
                        description || existingConfig.description,
                        token,
                        existingConfig.request_limit,
                        'exclusive' // 设置为独享模式
                    );

                    results.push({
                        nodeType,
                        token,
                        tokenPreview: token.substring(0, 8) + '...' + token.substring(120),
                        shared: false
                    });

                } catch (error) {
                    errors.push(`节点 ${nodeType} 处理失败: ${error.message}`);
                }
            }
        }

        // 刷新节点选择器缓存
        await nodeSelector.refreshCache();

        if (results.length === 0) {
            return res.status(400).json({
                code: 400,
                msg: '没有成功创建任何token',
                errors
            });
        }

        res.json({
            code: 0,
            msg: shareToken ?
                `成功创建共享token，授权给 ${results.length} 个节点` :
                `成功为 ${results.length} 个节点创建独立token`,
            data: {
                results,
                errors: errors.length > 0 ? errors : undefined,
                shareToken
            }
        });
    } catch (error) {
        console.error('创建token失败:', error);
        res.status(500).json({
            code: 500,
            msg: '创建失败',
            error: error.message
        });
    }
});

// 更新token配置
router.put('/tokens/:nodeType', authenticate, async (req, res) => {
    try {
        const { nodeType } = req.params;
        const { baseUrl, description, requestLimit, timeout, status } = req.body;

        // 检查配置是否存在
        const existingConfig = await dataModel.getApiConfig(nodeType);
        if (!existingConfig) {
            return res.status(404).json({
                code: 404,
                msg: '配置不存在'
            });
        }

        // 更新配置
        await dataModel.setApiConfig(
            nodeType,
            baseUrl || existingConfig.base_url,
            timeout || existingConfig.timeout,
            existingConfig.retry_count,
            description !== undefined ? description : existingConfig.description,
            existingConfig.auth_key, // 保持原token不变
            requestLimit !== undefined ? requestLimit : existingConfig.request_limit,
            existingConfig.token_mode || 'exclusive' // 保持原token模式不变
        );

        // 如果有状态更新，需要单独处理
        if (status !== undefined) {
            // 这里可以添加状态更新逻辑
            console.log(`更新节点 ${nodeType} 状态为: ${status}`);
        }

        // 刷新节点选择器缓存
        await nodeSelector.refreshCache();

        res.json({
            code: 0,
            msg: '更新成功'
        });
    } catch (error) {
        console.error('更新token配置失败:', error);
        res.status(500).json({
            code: 500,
            msg: '更新失败',
            error: error.message
        });
    }
});

// 删除token
router.delete('/tokens/:nodeType', authenticate, async (req, res) => {
    try {
        const { nodeType } = req.params;

        // 检查配置是否存在
        const existingConfig = await dataModel.getApiConfig(nodeType);
        if (!existingConfig) {
            return res.status(404).json({
                code: 404,
                msg: '配置不存在'
            });
        }

        // 删除配置
        await dataModel.deleteApiConfig(nodeType);

        // 刷新节点选择器缓存
        await nodeSelector.refreshCache();

        res.json({
            code: 0,
            msg: '删除成功'
        });
    } catch (error) {
        console.error('删除token失败:', error);
        res.status(500).json({
            code: 500,
            msg: '删除失败',
            error: error.message
        });
    }
});

// 重新生成token
router.post('/tokens/:nodeType/regenerate', authenticate, async (req, res) => {
    try {
        const { nodeType } = req.params;

        // 检查配置是否存在
        const existingConfig = await dataModel.getApiConfig(nodeType);
        if (!existingConfig) {
            return res.status(404).json({
                code: 404,
                msg: '配置不存在'
            });
        }

        // 生成新的128位token
        const newToken = dataModel.generateAuthKey();

        // 更新配置中的token，保持原有的token模式
        await dataModel.setApiConfig(
            nodeType,
            existingConfig.base_url,
            existingConfig.timeout,
            existingConfig.retry_count,
            existingConfig.description,
            newToken,
            existingConfig.request_limit,
            existingConfig.token_mode || 'exclusive' // 保持原有模式
        );

        // 刷新节点选择器缓存
        await nodeSelector.refreshCache();

        res.json({
            code: 0,
            msg: '重新生成成功',
            data: {
                token: newToken,
                tokenPreview: newToken.substring(0, 8) + '...' + newToken.substring(120)
            }
        });
    } catch (error) {
        console.error('重新生成token失败:', error);
        res.status(500).json({
            code: 500,
            msg: '重新生成失败',
            error: error.message
        });
    }
});

// 获取可用节点列表（用于选择）
router.get('/available-nodes', authenticate, async (req, res) => {
    try {
        const allConfigs = await dataModel.getAllApiConfigs();

        // 返回所有配置的基本信息，排除已有128位token的节点
        const nodes = allConfigs
            .filter(config => !(config.auth_key && config.auth_key.length === 128))
            .map(config => {
                // 解析节点标识，获取类型和名称
                const parts = config.client_type.split('_');
                const type = parts[0];
                const name = parts.slice(1).join('_');

                const typeDisplayName = {
                    'QQ': 'QQ',
                    'TIM': 'TIM',
                    'QQlite': 'QQ轻聊版',
                    '企点QQ': '企点QQ'
                }[type] || type;

                const displayLabel = name ?
                    `${typeDisplayName}(${name}) - ${config.base_url || '未配置URL'}` :
                    `${typeDisplayName} - ${config.base_url || '未配置URL'}`;

                return {
                    value: config.client_type,
                    label: displayLabel,
                    nodeType: config.client_type,
                    baseUrl: config.base_url,
                    description: config.description || '',
                    status: config.status || 'active',
                    timeout: config.timeout || 10000,
                    requestLimit: config.request_limit || 0,
                    retryCount: config.retry_count || 3
                };
            });

        res.json({
            code: 0,
            msg: '获取成功',
            data: nodes
        });
    } catch (error) {
        console.error('获取可用节点失败:', error);
        res.status(500).json({
            code: 500,
            msg: '获取失败',
            error: error.message
        });
    }
});

// 测试token连接
router.post('/tokens/:nodeType/test', authenticate, async (req, res) => {
    try {
        const { nodeType } = req.params;
        
        const config = await dataModel.getApiConfig(nodeType);
        if (!config || !config.base_url) {
            return res.status(404).json({
                code: 404,
                msg: '节点配置不存在或未配置URL'
            });
        }

        const axios = require('axios');
        const startTime = Date.now();
        
        try {
            // 健康检查 - 访问节点根路由，有任何响应就认为健康
            const response = await axios.get(config.base_url, {
                timeout: config.timeout || 5000,
                validateStatus: () => true, // 接受任何状态码
                headers: {
                    'User-Agent': 'QsignHook-HealthCheck/1.0',
                    'x-node-token': config.auth_key
                }
            });

            const latency = Date.now() - startTime;

            // 尝试获取响应预览
            let responsePreview = '';
            if (response.data) {
                try {
                    if (typeof response.data === 'string') {
                        responsePreview = response.data.substring(0, 100);
                    } else if (typeof response.data === 'object') {
                        responsePreview = JSON.stringify(response.data).substring(0, 100);
                    } else {
                        responsePreview = String(response.data).substring(0, 100);
                    }
                } catch (e) {
                    responsePreview = 'Unable to parse response';
                }
            }

            res.json({
                code: 0,
                msg: '节点健康检查成功',
                data: {
                    latency,
                    status: response.status,
                    healthy: true,
                    nodeType,
                    baseUrl: config.base_url,
                    responsePreview: responsePreview || 'No response data'
                }
            });
        } catch (error) {
            const latency = Date.now() - startTime;

            // 分析错误类型
            let errorType = 'unknown';
            let suggestion = '';

            if (error.code === 'ECONNREFUSED') {
                errorType = 'connection_refused';
                suggestion = '节点可能未启动或端口错误';
            } else if (error.code === 'ETIMEDOUT') {
                errorType = 'timeout';
                suggestion = '节点响应超时，检查网络连接';
            } else if (error.code === 'ENOTFOUND') {
                errorType = 'not_found';
                suggestion = '域名或IP地址无法解析';
            }

            res.json({
                code: 1,
                msg: '节点健康检查失败',
                data: {
                    latency,
                    healthy: false,
                    error: error.message,
                    errorType,
                    suggestion,
                    nodeType,
                    baseUrl: config.base_url
                }
            });
        }
    } catch (error) {
        console.error('测试token连接失败:', error);
        res.status(500).json({
            code: 500,
            msg: '测试失败',
            error: error.message
        });
    }
});

module.exports = router;
