// 配置管理器 - 用于从配置文件读取和写入配置
const fs = require('fs');
const path = require('path');
const colors = require('colors');

// 用于处理API配置的MySQL模型
const dataModel = require('../models/mysql-real-model');

// 配置默认值
const DEFAULT_CONFIG = {
  server: {
    port: 12041,
    host: '0.0.0.0',
    logLevel: 'info'
  },  client: {
    port: 3000,
    apiUrl: 'http://0.0.0.0:12041/api'
  },  api: {
    defaultAuthKey: '1145141919810',
    timeout: 10000
  },
  database: {
    path: './database/signature.json',
    backupInterval: 24 // 小时
  }
};

// 确保数据库目录存在
const dbDir = path.join(__dirname, '../../database');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// 配置文件路径
const configPath = path.join(dbDir, 'config.json');

class ConfigManager {
  constructor() {
    this.config = null;
    this.initialized = false;
    this.initPromise = this.init();
  }

  async init() {
    try {
      // 检查配置文件是否存在
      if (fs.existsSync(configPath)) {
        const configData = fs.readFileSync(configPath, 'utf8');
        this.config = JSON.parse(configData);
        console.log('配置文件已加载'.green);
      } else {
        console.log('创建默认配置文件...'.yellow);
        this.config = {...DEFAULT_CONFIG};
        fs.writeFileSync(configPath, JSON.stringify(this.config, null, 2));
      }
      
      this.initialized = true;
      console.log('配置初始化完成'.green);
    } catch (error) {
      console.error('配置初始化失败:'.red, error);
      this.config = {...DEFAULT_CONFIG};
      throw error;
    }
  }
  async ensureInitialized() {
    if (!this.initialized) {
      await this.initPromise;
    }
  }

  // 获取配置
  async get(path, defaultValue) {
    await this.ensureInitialized();
    try {
      // 支持点路径: 'server.port'
      const parts = path.split('.');
      let value = this.config;
      
      for (const part of parts) {
        if (value === undefined || value === null) {
          return defaultValue;
        }
        value = value[part];
      }
      
      return value !== undefined ? value : defaultValue;
    } catch (error) {
      console.error(`获取配置 ${path} 失败:`.red, error);
      return defaultValue;
    }
  }
  // 设置配置
  async set(path, value) {
    await this.ensureInitialized();
    try {
      console.log(`📝 设置配置 ${path}:`.blue, JSON.stringify(value, null, 2));
      
      // 支持点路径: 'server.port'
      const parts = path.split('.');
      let current = this.config;
      
      // 遍历路径直到倒数第二级
      for (let i = 0; i < parts.length - 1; i++) {
        const part = parts[i];
        if (current[part] === undefined) {
          current[part] = {};
          console.log(`🆕 创建配置节: ${parts.slice(0, i + 1).join('.')}`.yellow);
        }
        current = current[part];
      }
      
      // 设置最后一级的值
      const lastPart = parts[parts.length - 1];
      const oldValue = current[lastPart];
      current[lastPart] = value;
      
      console.log(`🔄 ${path}: ${JSON.stringify(oldValue)} -> ${JSON.stringify(value)}`.cyan);
      
      // 保存到文件
      const configData = JSON.stringify(this.config, null, 2);
      fs.writeFileSync(configPath, configData);
      console.log(`�� 配置已保存到文件: ${configPath}`.green);
      
      return true;
    } catch (error) {
      console.error(`设置配置 ${path} 失败:`.red, error);
      return false;
    }
  }
  // 获取公开配置（供前端初始化使用）
  async getPublicConfig() {
    await this.ensureInitialized();
    try {
      return {
        clientPort: await this.get('client.port', DEFAULT_CONFIG.client.port),
        apiUrl: await this.get('client.apiUrl', DEFAULT_CONFIG.client.apiUrl)
      };
    } catch (error) {
      console.error('获取公开配置失败:'.red, error);
      return {
        clientPort: DEFAULT_CONFIG.client.port,
        apiUrl: DEFAULT_CONFIG.client.apiUrl
      };
    }
  }

  // 获取服务器配置
  async getServerConfig() {
    return await this.get('server', DEFAULT_CONFIG.server);
  }

  // 获取客户端配置
  async getClientConfig() {
    return await this.get('client', DEFAULT_CONFIG.client);
  }

  // 获取API配置
  async getApiConfig() {
    return await this.get('api', DEFAULT_CONFIG.api);
  }

  // 获取数据库配置
  async getDatabaseConfig() {
    return await this.get('database', DEFAULT_CONFIG.database);
  }  // 获取API Base URL
  async getBaseUrl(type) {
      const apiConfig = await dataModel.getApiConfig(type);
      return apiConfig ? apiConfig.base_url : null;
  }
  // 获取所有API Base URLs
  async getAllBaseUrls() {
    try {
      const apiConfigs = await dataModel.getAllApiConfigs();
      return apiConfigs;
    } catch (error) {
      console.error('获取所有API配置失败:'.red, error);
      return {};
    }
  }
  // 设置API Base URL
  async setBaseUrl(type, url) {
    try {
      console.log(`🔄 设置API配置 ${type}:`.blue, url);
      const result = await dataModel.setApiConfig(type, url);
      if (result) {
        console.log(`✅ API配置 ${type} 更新成功`.green);
      } else {
        console.log(`❌ API配置 ${type} 更新失败`.red);
      }
      return result;
    } catch (error) {
      console.error(`设置API配置 ${type} 失败:`.red, error);
      return false;
    }
  }  // 更新所有API Base URLs
  async updateBaseUrls(baseUrls) {
    try {
      console.log('🔄 开始更新Base URLs配置到数据库:'.blue, JSON.stringify(baseUrls, null, 2));
      const result = await dataModel.updateAllApiConfigs(baseUrls);
      if (result) {
        console.log('✅ Base URLs配置更新到数据库成功'.green);
        // 验证保存结果
        const saved = await this.getAllBaseUrls();
        console.log('🔍 保存后的配置:'.yellow, JSON.stringify(saved, null, 2));
      } else {
        console.log('❌ Base URLs配置更新到数据库失败'.red);
      }
      return result;
    } catch (error) {
      console.error('更新Base URLs配置失败:'.red, error);
      return false;
    }
  }

  // 重置所有配置
  async reset() {
    await this.ensureInitialized();
    try {
      this.config = {...DEFAULT_CONFIG};
      fs.writeFileSync(configPath, JSON.stringify(this.config, null, 2));
      return true;
    } catch (error) {
      console.error('重置配置失败:'.red, error);
      return false;
    }
  }
}

// 单例模式
const configManager = new ConfigManager();
module.exports = configManager;
