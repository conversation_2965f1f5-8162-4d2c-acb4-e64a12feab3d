const express = require('express');
const bodyParser = require('body-parser');

const app = express();

// 测试原始请求体捕获
app.use(bodyParser.json({
  verify: (req, res, buf, encoding) => {
    req.rawBody = buf.toString('utf8');
    console.log('📝 JSON原始请求体:', req.rawBody);
  }
}));

app.use(bodyParser.urlencoded({ 
  extended: true,
  verify: (req, res, buf, encoding) => {
    if (!req.rawBody) {
      req.rawBody = buf.toString('utf8');
      console.log('📝 URL编码原始请求体:', req.rawBody);
    }
  }
}));

// 测试路由
app.post('/test', (req, res) => {
  console.log('=== 请求信息 ===');
  console.log('Content-Type:', req.get('content-type'));
  console.log('原始请求体:', req.rawBody);
  console.log('解析后的请求体:', req.body);
  console.log('================');
  
  res.json({
    success: true,
    rawBody: req.rawBody,
    parsedBody: req.body,
    contentType: req.get('content-type')
  });
});

const port = 3001;
app.listen(port, () => {
  console.log(`🚀 测试服务器运行在 http://localhost:${port}`);
  console.log('📡 测试URL: POST http://localhost:3001/test');
  console.log('');
  console.log('测试命令:');
  console.log('curl -X POST http://localhost:3001/test \\');
  console.log('  -H "Content-Type: application/x-www-form-urlencoded" \\');
  console.log('  -d "data=810_9&guid=1145141919810&version=6.0.254"');
});
