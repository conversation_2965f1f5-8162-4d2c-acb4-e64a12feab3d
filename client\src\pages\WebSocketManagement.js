import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Tag, Space, Statistic, Row, Col, Modal, message, Badge } from 'antd';
import { 
  ReloadOutlined, 
  DisconnectOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined,
  UserOutlined,
  GlobalOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import api from '../services/api';
import NotificationHelper from '../components/NotificationHelper';
import { normalizeResponse, isResponseSuccess, getResponseData, getResponseMessage } from '../utils/responseHelper';

const WebSocketManagement = () => {
  const [status, setStatus] = useState({});
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(false);

  // 加载数据
  useEffect(() => {
    loadData();
    
    // 定时刷新
    const interval = setInterval(loadData, 5000);
    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      await Promise.all([
        loadStatus(),
        loadClients()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
    }
  };

  // 加载服务状态
  const loadStatus = async () => {
    try {
      const response = await api.get('/risk/websocket/status');
      const normalizedResponse = normalizeResponse(response);
      if (isResponseSuccess(normalizedResponse)) {
        setStatus(getResponseData(normalizedResponse));
      }
    } catch (error) {
      console.error('加载状态失败:', error);
    }
  };
  // 加载客户端列表
  const loadClients = async () => {
    setLoading(true);
    try {
      const response = await api.get('/risk/websocket/clients');
      if (response.data.code === 200) {
        setClients(response.data.data);
      }
    } catch (error) {
      console.error('加载客户端失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '加载客户端列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 踢出客户端
  const kickClient = (clientId) => {
    console.log('🚀 准备踢出客户端:', clientId);
    
    if (!clientId) {
      console.error('❌ 客户端ID为空');
      NotificationHelper.error('客户端ID无效', '无法踢出客户端：ID为空');
      return;
    }
    
    Modal.confirm({
      title: '确认踢出客户端',
      content: (
        <div>
          <p>确定要踢出以下客户端吗？</p>
          <p><strong>客户端ID:</strong> <code>{clientId}</code></p>
          <p style={{ color: '#ff4d4f', fontSize: '12px' }}>此操作将立即断开该客户端的连接</p>
        </div>
      ),
      okText: '确定踢出',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        console.log('📤 发送踢出请求:', clientId);
        setLoading(true);
        
        try {
          const kickUrl = `/risk/websocket/clients/${encodeURIComponent(clientId)}/kick`;
          console.log('🌐 请求URL:', kickUrl);
          
          const requestData = {
            reason: '管理员手动踢出',
            timestamp: new Date().toISOString()
          };
          console.log('📝 请求数据:', requestData);
            const response = await api.post(kickUrl, requestData);
          console.log('📥 踢出响应:', response);
          
          const normalizedResponse = normalizeResponse(response);
          if (isResponseSuccess(normalizedResponse)) {
            console.log('✅ 踢出成功');
            NotificationHelper.operationSuccess('踢出', '客户端已成功断开连接');
            
            // 立即刷新列表
            await loadClients();
          } else {
            console.error('❌ 踢出失败:', normalizedResponse);
            NotificationHelper.error('踢出操作失败', getResponseMessage(normalizedResponse));
          }
        } catch (error) {
          console.error('💥 踢出请求异常:', error);
          
          if (error.response) {
            // 服务器响应了错误状态码
            console.error('📄 错误响应:', error.response.data);
            console.error('🔢 状态码:', error.response.status);
            NotificationHelper.networkError(error.response.status, error.response.data?.message);
          } else if (error.request) {
            // 请求发出但没有收到响应
            console.error('📡 网络请求失败:', error.request);
            NotificationHelper.networkError(500, '网络连接异常，请检查服务器状态');
          } else {
            // 其他错误
            console.error('🔧 请求配置错误:', error.message);
            NotificationHelper.error('请求配置错误', error.message);
          }
        } finally {
          setLoading(false);
        }
      },
      onCancel: () => {
        console.log('🚫 用户取消踢出操作');
      }
    });
  };

  // 客户端表格列定义
  const columns = [
    {
      title: '客户端ID',
      dataIndex: 'id',
      key: 'id',
      render: (id) => (
        <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          {id}
        </span>
      )
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      render: (ip) => (
        <Tag icon={<GlobalOutlined />} color="blue">
          {ip}
        </Tag>
      )
    },
    {
      title: '用户代理',
      dataIndex: 'userAgent',
      key: 'userAgent',
      ellipsis: true,
      render: (userAgent) => (
        <span title={userAgent}>
          {userAgent && userAgent.length > 50 
            ? userAgent.substring(0, 50) + '...' 
            : userAgent || '未知'
          }
        </span>
      )
    },
    {
      title: '认证状态',
      dataIndex: 'isAuthenticated',
      key: 'isAuthenticated',
      render: (authenticated) => (
        <Tag
          icon={authenticated ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
          color={authenticated ? 'green' : 'red'}
        >
          {authenticated ? '已认证' : '未认证'}
        </Tag>
      )
    },
    {
      title: '连接时间',
      dataIndex: 'connectedAt',
      key: 'connectedAt',
      render: (time) => (
        <span>
          <ClockCircleOutlined style={{ marginRight: 4 }} />
          {new Date(time).toLocaleString()}
        </span>
      )
    },
    {
      title: '最后心跳',
      dataIndex: 'lastHeartbeat',
      key: 'lastHeartbeat',
      render: (time) => {
        const now = Date.now();
        const heartbeatTime = new Date(time).getTime();
        const diff = now - heartbeatTime;
        const seconds = Math.floor(diff / 1000);
        
        let color = 'green';
        if (seconds > 60) color = 'red';
        else if (seconds > 30) color = 'orange';
        
        return (
          <Tag color={color}>
            {seconds}秒前
          </Tag>
        );
      }
    },
    {
      title: '在线时长',
      dataIndex: 'connectedAt',
      key: 'duration',
      render: (connectedAt) => {
        const now = Date.now();
        const connectTime = new Date(connectedAt).getTime();
        const duration = now - connectTime;
        
        const hours = Math.floor(duration / (1000 * 60 * 60));
        const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((duration % (1000 * 60)) / 1000);
        
        return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      }
    },    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => {
        console.log('🔍 渲染操作列，记录:', record);
        
        return (
          <Space>
            <Button
              type="link"
              danger
              icon={<DisconnectOutlined />}
              size="small"
              loading={loading}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('🎯 踢出按钮被点击，客户端ID:', record.id);
                kickClient(record.id);
              }}
              style={{ 
                color: '#ff4d4f',
                border: 'none',
                padding: '4px 8px'
              }}
            >
              踢出
            </Button>
          </Space>
        );
      }
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <h1>WebSocket 连接管理</h1>
      
      {/* 状态统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="服务状态"
              value={status.isRunning ? '运行中' : '已停止'}
              prefix={
                <Badge 
                  status={status.isRunning ? 'processing' : 'error'} 
                />
              }
              valueStyle={{ 
                color: status.isRunning ? '#52c41a' : '#ff4d4f' 
              }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="监听端口"
              value={status.port || '--'}
              prefix={<GlobalOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总连接数"
              value={status.clientCount || 0}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已认证连接"
              value={status.authenticatedCount || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 客户端列表 */}
      <Card
        title="客户端连接列表"
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadData}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Table
          columns={columns}
          dataSource={clients}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 服务信息 */}
      <Card title="服务信息" style={{ marginTop: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <div style={{ marginBottom: 16 }}>
              <h4>连接状态</h4>
              <p>
                <Badge 
                  status={status.isRunning ? 'processing' : 'error'} 
                  text={status.isRunning ? '服务正常运行' : '服务已停止'}
                />
              </p>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <h4>端口信息</h4>
              <p>监听端口: <code>{status.port || '未知'}</code></p>
            </div>
          </Col>
          
          <Col span={12}>
            <div style={{ marginBottom: 16 }}>
              <h4>连接统计</h4>
              <p>总连接数: {status.clientCount || 0}</p>
              <p>已认证连接: {status.authenticatedCount || 0}</p>
              <p>未认证连接: {(status.clientCount || 0) - (status.authenticatedCount || 0)}</p>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 使用说明 */}
      <Card title="使用说明" style={{ marginTop: 16 }}>
        <div>
          <h4>WebSocket连接</h4>
          <p>客户端可以通过以下地址连接到风控服务：</p>
          <code>ws://localhost:{status.port || '12042'}</code>
          
          <h4 style={{ marginTop: 16 }}>认证流程</h4>
          <ol>
            <li>客户端连接到WebSocket服务</li>
            <li>发送认证消息，包含有效的auth_key</li>
            <li>认证成功后可以发送风控检测请求</li>
            <li>服务端返回检测结果</li>
          </ol>
          
          <h4 style={{ marginTop: 16 }}>消息格式</h4>
          <p>认证消息：</p>
          <pre style={{ background: '#f6f8fa', padding: 8, borderRadius: 4 }}>
{`{
  "type": "auth",
  "data": {
    "authKey": "your_auth_key"
  }
}`}
          </pre>
          
          <p>风控检测消息：</p>
          <pre style={{ background: '#f6f8fa', padding: 8, borderRadius: 4 }}>
{`{
  "type": "risk_check",
  "requestId": "unique_request_id",
  "data": {
    "content": "待检测的内容",
    "clientInfo": {
      "username": "用户名",
      "device_id": "设备ID",
      "ip_address": "IP地址",
      "uin": "QQ号"
    }
  }
}`}
          </pre>
        </div>
      </Card>
    </div>
  );
};

export default WebSocketManagement;
