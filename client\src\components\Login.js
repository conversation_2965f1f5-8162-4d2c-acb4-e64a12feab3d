import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, Modal } from 'antd';
import { UserOutlined, LockOutlined, SafetyCertificateTwoTone } from '@ant-design/icons';
import { authAPI } from '../services/api';
import api from '../services/api';
import NotificationHelper from './NotificationHelper';
import './NewLogin.css';

const { Title, Paragraph, Text } = Typography;

const Login = ({ onLogin }) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [show2fa, setShow2fa] = useState(false);
  const [userId, setUserId] = useState(null);
  const [input2fa, setInput2fa] = useState('');

  useEffect(() => {
    console.log('登录页面已加载');
    
    // 延迟显示欢迎通知
    const timer = setTimeout(() => {
      NotificationHelper.info('欢迎使用 QSignHook', '本系统已启用二次元风格认证，登录更安全更萌~ 🌸');
    }, 1000);

    // 监听全局2FA弹窗事件
    const handler = (e) => {
      if (e.detail && e.detail.data && e.detail.data.user_id) setUserId(e.detail.data.user_id);
      setShow2fa(true);
      setInput2fa('');
      setTimeout(() => { window.__SHOW_2FA_MODAL__ = false; }, 1000); // 允许后续弹窗
    };
    window.addEventListener('show-2fa-modal', handler);
    return () => {
      clearTimeout(timer);
      window.removeEventListener('show-2fa-modal', handler);
    };
  }, []);
  // 登录表单提交
  const onFinish = async (values) => {
    console.log('开始登录:', values.username);
    setLoading(true);
    
    try {
      const response = await authAPI.login({
        username: values.username,
        password: values.password
      });
      
      console.log('登录响应:', response);
      
      // 正确处理axios响应格式
      const res = response.data;
      
      if (res.code === 0) {
        // 无需2FA，直接登录
        handleLoginSuccess(res.data);
        NotificationHelper.loginNotification('success', `欢迎回来，${res.data.user?.username || '用户'}！正在跳转到控制台...`);
      } else if (res.code === 1001 && res.data && res.data.need2fa) {
        // 需要2FA
        setUserId(res.data.user_id);
        setShow2fa(true);
        NotificationHelper.loginNotification('need_2fa');
      } else if (res.code === 1001) {
        // 兼容后端未返回need2fa字段但code=1001
        NotificationHelper.loginNotification('need_2fa');
      } else {
        NotificationHelper.loginNotification('invalid_credentials', res.msg);
      }
    } catch (e) {
      console.error('登录错误:', e);
      NotificationHelper.networkError(500, '网络连接失败或服务器错误，请稍后重试');
    }
    setLoading(false);
  };
  // 2FA验证码提交
  const handle2fa = async () => {
    console.log('提交验证码:', input2fa, '长度:', input2fa.length);
    if (!input2fa) {
      NotificationHelper.twoFactorError('input');
      return;
    }
    if (input2fa.length !== 6 || !/^\d{6}$/.test(input2fa)) {
      NotificationHelper.twoFactorError('format_error', '验证码必须是6位数字，请重新输入');
      return;
    }
    setLoading(true);
    try {
      const response = await api.post('/auth/login/2fa', {
        user_id: userId,
        code: input2fa,
        remember: form.getFieldValue('remember') || false
      });
      
      // 正确处理axios响应格式
      const res = response.data;
      
      if (res.code === 0) {
        handleLoginSuccess(res.data);
        setShow2fa(false);
        setInput2fa('');
        NotificationHelper.loginNotification('success', '验证通过，欢迎进入管理控制台！');
      } else {
        // 根据不同错误码提供不同的提示        let errorType = 'verify';
        let errorMessage = res.msg || '验证码错误，请重新输入';
        
        if (res.code === 401) {
          errorType = 'invalid_code';
          errorMessage = '验证码错误，请检查您的身份验证器应用中的最新6位数字代码';
        } else if (res.code === 400) {
          errorType = 'format_error';
          errorMessage = res.msg || '验证码格式不正确';
        } else if (res.code === 403) {
          errorType = 'not_bound';
          errorMessage = '您的账户未绑定二步验证，请联系管理员';
        } else if (res.code === 500) {
          errorType = 'general';
          errorMessage = '服务器暂时不可用，请稍后重试';
        }
        
        NotificationHelper.twoFactorError(errorType, errorMessage);
        
        // 如果是验证码错误，清空输入框让用户重新输入
        if (res.code === 401) {
          setInput2fa('');
        }
      }
    } catch (e) {
      console.error('2FA验证错误:', e);
        let errorType = 'general';
      let errorMessage = '网络连接失败，请检查网络后重试';
      
      if (e.response) {
        const status = e.response.status;
        const data = e.response.data;
        
        if (status === 401) {
          errorType = 'invalid_code';
          errorMessage = data?.msg || '验证码错误，请检查您的身份验证器中的代码';
        } else if (status === 400) {
          errorType = 'format_error';
          errorMessage = data?.msg || '请求参数错误';
        } else if (status === 500) {
          errorType = 'general';
          errorMessage = '服务器内部错误，请稍后重试';
        }
        
        // 验证码错误时清空输入框
        if (status === 401) {
          setInput2fa('');
        }
      }
      
      NotificationHelper.twoFactorError(errorType, errorMessage);
    }
    setLoading(false);
  };

  // 登录成功处理
  const handleLoginSuccess = (data) => {
    localStorage.setItem('auth_token', data.token);
    localStorage.setItem('user_info', JSON.stringify(data.user));
    localStorage.setItem('login_time', new Date().toISOString());
    
    // 延迟跳转，让用户看到成功提示
    setTimeout(() => {
      onLogin(true);
      window.location.href = '/settings';
    }, 2000);
  };

  return (
    <div className="login-container">
      <div className="background-animation">
        <div className="floating-hearts">
          {[...Array(12)].map((_, i) => (
            <span key={i} className={`heart heart-${i}`}>💙</span>
          ))}
        </div>
        <div className="sakura-petals">
          {[...Array(8)].map((_, i) => (
            <span key={i} className={`petal petal-${i}`}>🌸</span>
          ))}
        </div>
      </div>
      <div className="login-content">
        <Card className="login-card" bordered={false}>
          <div className="login-header">
            <div className="login-logo">
              <div className="login-logo-circle">
                <SafetyCertificateTwoTone 
                  twoToneColor={["#667eea", "#764ba2"]} 
                  style={{ fontSize: 42 }} 
                />
              </div>
            </div>
            <div className="welcome-text">
              <Title level={2} className="login-title">
                QSignHook 管理控制台
              </Title>
              <Paragraph className="login-subtitle">
                二次元风格签名服务管理后台
              </Paragraph>
            </div>
          </div>
          <Form
            name="login_form"
            form={form}
            initialValues={{ remember: true }}
            onFinish={onFinish}
            className="login-form"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名!' }]}
            >
              <Input
                prefix={<UserOutlined className="input-icon" />}
                placeholder="请输入用户名"
                size="large"
                className="login-input"
                autoFocus
              />
            </Form.Item>
            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码!' }]}
            >
              <Input.Password
                prefix={<LockOutlined className="input-icon" />}
                placeholder="请输入密码"
                size="large"
                className="login-input"
              />
            </Form.Item>
            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                className="login-button"
                size="large"
                loading={loading}
                block
                disabled={loading}
              >
                {loading ? '登录中...' : '登 录'}
              </Button>
            </Form.Item>
          </Form>
        </Card>
        <div className="login-footer">
          <div style={{ marginBottom: 8 }}>
            <Button
              type="link"
              onClick={() => window.location.hash = 'public-status'}
              style={{ padding: 0, height: 'auto', color: '#667eea' }}
            >
              🌸 查看节点状态（无需登录）
            </Button>
          </div>
          <span>© 2024 QSignHook · 二次元萌系管理控制台</span>
        </div>
      </div>      <Modal
        open={show2fa}
        onCancel={() => {
          setShow2fa(false);
          setInput2fa('');
        }}
        footer={null}
        centered
        title={
          <div className="modal-title">
            <SafetyCertificateTwoTone style={{ marginRight: 8, fontSize: 18 }} />
            二步验证
          </div>
        }
        className="twofa-modal"
        destroyOnClose
      >
        <div className="twofa-content">
          <Paragraph className="twofa-description">
            🔐 请输入您的6位二步验证码
          </Paragraph>
          <div style={{ marginBottom: 12 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              💡 打开您的身份验证器应用（如 Google Authenticator、Microsoft Authenticator）获取最新验证码
            </Text>
          </div>
          <Input
            placeholder="000000"
            maxLength={6}
            value={input2fa}
            onChange={e => {
              const value = e.target.value.replace(/\D/g, '');
              console.log('输入内容:', value, '长度:', value.length);
              setInput2fa(value);
            }}
            onPressEnter={handle2fa}
            className="twofa-input"
            size="large"
            autoFocus
            style={{
              marginBottom: 8,
              fontSize: 18,
              textAlign: 'center',
              letterSpacing: '0.5em',
              fontFamily: 'monospace'
            }}
            status={input2fa.length > 0 && input2fa.length < 6 ? 'warning' : ''}
          />
          {input2fa.length > 0 && input2fa.length < 6 && (
            <div style={{ marginBottom: 8, textAlign: 'center' }}>
              <Text type="warning" style={{ fontSize: 12 }}>
                ⚠️ 验证码需要6位数字 ({input2fa.length}/6)
              </Text>
            </div>
          )}
          {input2fa.length === 6 && (
            <div style={{ marginBottom: 8, textAlign: 'center' }}>
              <Text type="success" style={{ fontSize: 12 }}>
                ✅ 验证码格式正确
              </Text>
            </div>
          )}
          <Button 
            type="primary" 
            onClick={handle2fa} 
            className="twofa-button"
            loading={loading}
            disabled={input2fa.length !== 6 || loading}
            block
            style={{ marginTop: 8 }}
          >
            {loading ? '验证中...' : '确认验证'}
          </Button>
          <div style={{ marginTop: 12, textAlign: 'center' }}>
            <Text type="secondary" style={{ fontSize: 11 }}>
              验证码每30秒刷新一次，请使用最新的代码
            </Text>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default Login;
