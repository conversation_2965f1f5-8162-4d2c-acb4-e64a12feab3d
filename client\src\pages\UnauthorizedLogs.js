import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Input, 
  DatePicker, 
  Button, 
  Space, 
  Tag,
  Row,
  Col,
  Tooltip,
  Typography,
  Alert,
  Badge
} from 'antd';
import { 
  SearchOutlined, 
  ReloadOutlined,
  SecurityScanOutlined,
  WarningOutlined,
  SafetyOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { unauthorizedLogsAPI } from '../services/api';
import NotificationHelper from '../components/NotificationHelper';
import { normalizeResponse, isResponseSuccess, getResponseData } from '../utils/responseHelper';

const { RangePicker } = DatePicker;
const { Title, Paragraph } = Typography;

const UnauthorizedLogs = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [filters, setFilters] = useState({
    ip: '',
    dateRange: null,
  });

  useEffect(() => {
    fetchData();
  }, [pagination.current, pagination.pageSize]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters,
      };

      // 处理日期范围
      if (filters.dateRange && filters.dateRange.length === 2) {
        params.startDate = filters.dateRange[0].format('YYYY-MM-DD');
        params.endDate = filters.dateRange[1].format('YYYY-MM-DD');
      }

      const response = await unauthorizedLogsAPI.getUnauthorizedLogs(params);
      const normalizedResponse = normalizeResponse(response);
      
      if (isResponseSuccess(normalizedResponse)) {
        const responseData = getResponseData(normalizedResponse);
        setData(responseData.logs || []);
        setPagination(prev => ({
          ...prev,
          total: responseData.pagination?.total || 0,
        }));
      } else {
        console.error('❌ UnauthorizedLogs - API返回错误:', normalizedResponse);
        NotificationHelper.networkError(normalizedResponse?.code || 500, '获取未授权日志数据失败');
      }
    } catch (error) {
      console.error('❌ UnauthorizedLogs - 请求异常:', error);
      NotificationHelper.networkError(error.response?.status || 500, '获取未授权日志失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchData();
  };

  const handleReset = () => {
    setFilters({
      ip: '',
      dateRange: null,
    });
    setPagination(prev => ({ ...prev, current: 1 }));
    setTimeout(fetchData, 100);
  };

  const handleTableChange = (paginationConfig) => {
    setPagination(paginationConfig);
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 140,
      render: (text) => (
        <Tag color="red" icon={<WarningOutlined />}>
          {text}
        </Tag>
      ),
    },
    {
      title: '请求内容',
      dataIndex: 'request_body',
      key: 'request_body',
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          <div style={{ 
            maxWidth: 300, 
            overflow: 'hidden', 
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {text || '-'}
          </div>
        </Tooltip>
      ),
    },
    {
      title: 'User Agent',
      dataIndex: 'user_agent',
      key: 'user_agent',
      width: 200,
      ellipsis: {
        showTitle: false,
      },
      render: (text) => (
        <Tooltip placement="topLeft" title={text}>
          <div style={{ 
            maxWidth: 200, 
            overflow: 'hidden', 
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {text || '-'}
          </div>
        </Tooltip>
      ),
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 160,
    },
  ];

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        {/* 页面标题 */}
        <div style={{ marginBottom: '24px' }}>
          <Title level={2} style={{ margin: 0, color: '#ff4d4f' }}>
            <SafetyOutlined style={{ marginRight: '12px' }} />
            未授权访问日志
          </Title>
          <Paragraph style={{ margin: '8px 0 0 0', color: '#666' }}>
            监控和记录所有未授权的API访问尝试
          </Paragraph>
        </div>

        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '24px', color: '#ff4d4f', marginBottom: '8px' }}>
                  🚨
                </div>
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#ff4d4f' }}>
                  {pagination.total}
                </div>
                <div style={{ color: '#666', fontSize: '14px' }}>总威胁次数</div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '24px', color: '#fa8c16', marginBottom: '8px' }}>
                  🌐
                </div>
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#fa8c16' }}>
                  {new Set(data.map(item => item.ip)).size}
                </div>
                <div style={{ color: '#666', fontSize: '14px' }}>唯一IP数</div>
              </div>
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '24px', color: '#1890ff', marginBottom: '8px' }}>
                  📊
                </div>
                <div style={{ fontSize: '20px', fontWeight: 'bold', color: '#1890ff' }}>
                  {data.length > 0 ? new Date(data[0].timestamp).toLocaleDateString() : '--'}
                </div>
                <div style={{ color: '#666', fontSize: '14px' }}>最新攻击</div>
              </div>
            </Card>
          </Col>
        </Row>

        {/* 搜索过滤器 */}
        <Card style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Input
                placeholder="🔍 搜索IP地址"
                value={filters.ip}
                onChange={(e) => setFilters(prev => ({ ...prev, ip: e.target.value }))}
                allowClear
                prefix={<SearchOutlined />}
                style={{ borderRadius: '6px' }}
              />
            </Col>
            <Col xs={24} sm={12} md={8}>
              <RangePicker
                value={filters.dateRange}
                onChange={(dates) => setFilters(prev => ({ ...prev, dateRange: dates }))}
                style={{ width: '100%', borderRadius: '6px' }}
                placeholder={['📅 开始日期', '📅 结束日期']}
              />
            </Col>
            <Col xs={24} md={8}>
              <Space>
                <Button
                  type="primary"
                  icon={<SearchOutlined />}
                  onClick={handleSearch}
                  style={{ 
                    borderRadius: '6px',
                    background: 'linear-gradient(135deg, #ff6b6b 0%, #ff4757 100%)',
                    border: 'none'
                  }}
                >
                  搜索
                </Button>
                <Button 
                  onClick={handleReset}
                  style={{ borderRadius: '6px' }}
                >
                  重置
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchData}
                  style={{ borderRadius: '6px' }}
                >
                  刷新
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 威胁等级提示 */}
        <Alert
          message={
            <Space>
              <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
              <span style={{ fontWeight: 'bold' }}>安全威胁监控</span>
            </Space>
          }
          description={
            <div>
              <p style={{ margin: '4px 0' }}>
                🛡️ 系统已自动记录所有未授权访问尝试，包括错误的API密钥、JWT Token等
              </p>
              <p style={{ margin: '4px 0' }}>
                ⚠️ 如果发现大量来自同一IP的攻击，建议添加IP黑名单或配置防火墙规则
              </p>
            </div>
          }
          type="warning"
          showIcon
          style={{ 
            marginBottom: '16px',
            borderRadius: '8px',
            border: '1px solid #ffadd2'
          }}
        />

        {/* 日志表格 */}
        <Card 
          title={
            <Space>
              <SecurityScanOutlined style={{ color: '#ff4d4f' }} />
              <span>未授权访问记录</span>
              <Badge 
                count={pagination.total} 
                style={{ 
                  backgroundColor: '#ff4d4f',
                  boxShadow: '0 0 8px rgba(255, 77, 79, 0.3)'
                }} 
              />
            </Space>
          }
          extra={
            <Space>
              <Tag color="orange" icon={<WarningOutlined />}>
                高风险监控
              </Tag>
              <Tag color="red">
                总计: {pagination.total} 条威胁
              </Tag>
            </Space>
          }
          style={{
            borderRadius: '8px',
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'
          }}
          styles={{
            header: { 
              background: 'linear-gradient(135deg, #fff2f0 0%, #fff1f0 100%)',
              borderBottom: '1px solid #ffccc7'
            }
          }}
        >
          <Table
            columns={[
              {
                title: 'ID',
                dataIndex: 'id',
                key: 'id',
                width: 80,
                render: (text) => (
                  <Badge count={text} style={{ backgroundColor: '#ff4d4f' }} />
                ),
              },
              {
                title: '⚠️ 威胁IP',
                dataIndex: 'ip',
                key: 'ip',
                width: 140,
                render: (text) => (
                  <Tag 
                    color="red" 
                    icon={<WarningOutlined />}
                    style={{ 
                      fontWeight: 'bold',
                      padding: '4px 8px',
                      borderRadius: '6px'
                    }}
                  >
                    {text}
                  </Tag>
                ),
              },
              {
                title: '🎯 目标端点',
                dataIndex: 'endpoint',
                key: 'endpoint',
                width: 150,
                render: (text) => (
                  <code style={{ 
                    background: '#fff2f0', 
                    padding: '2px 6px', 
                    borderRadius: '4px',
                    color: '#ff4d4f',
                    fontSize: '12px'
                  }}>
                    {text}
                  </code>
                ),
              },
              {
                title: '🔑 使用密钥',
                dataIndex: 'auth_key',
                key: 'auth_key',
                width: 200,
                ellipsis: {
                  showTitle: false,
                },
                render: (text) => (
                  <Tooltip placement="topLeft" title={text}>
                    <div style={{ 
                      maxWidth: 180, 
                      overflow: 'hidden', 
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      background: '#f6ffed',
                      padding: '2px 6px',
                      borderRadius: '4px',
                      border: '1px solid #d9f7be',
                      fontSize: '11px',
                      fontFamily: 'monospace'
                    }}>
                      {text || '无密钥'}
                    </div>
                  </Tooltip>
                ),
              },
              {
                title: '📱 User Agent',
                dataIndex: 'user_agent',
                key: 'user_agent',
                width: 200,
                ellipsis: {
                  showTitle: false,
                },
                render: (text) => (
                  <Tooltip placement="topLeft" title={text}>
                    <div style={{ 
                      maxWidth: 180, 
                      overflow: 'hidden', 
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      fontSize: '12px',
                      color: '#666'
                    }}>
                      {text || '未知客户端'}
                    </div>
                  </Tooltip>
                ),
              },
              {
                title: '🕒 攻击时间',
                dataIndex: 'timestamp',
                key: 'timestamp',
                width: 160,
                render: (text) => (
                  <div style={{ fontSize: '12px' }}>
                    <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
                      {new Date(text).toLocaleDateString()}
                    </div>
                    <div style={{ color: '#666' }}>
                      {new Date(text).toLocaleTimeString()}
                    </div>
                  </div>
                ),
              },
            ]}
            dataSource={data}
            rowKey="id"
            loading={loading}
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条威胁记录，共 ${total} 条`,
              pageSizeOptions: ['10', '20', '50', '100'],
            }}
            onChange={handleTableChange}
            scroll={{ x: 800 }}
            rowClassName={() => 'threat-row'}
            size="small"
          />
        </Card>

        <style jsx>{`
          .threat-row {
            background-color: #fff2f0 !important;
            border-left: 3px solid #ff4d4f;
          }
          .threat-row:hover {
            background-color: #ffebe6 !important;
            box-shadow: 0 2px 8px rgba(255, 77, 79, 0.15);
          }
          .ant-table-tbody > tr.threat-row > td {
            border-bottom: 1px solid #ffccc7;
          }
        `}</style>
      </div>
    </div>
  );
};

export default UnauthorizedLogs;
