-- 数据库迁移脚本：添加token_mode字段
-- 用于支持独享和共享token模式

-- 检查并添加token_mode字段
ALTER TABLE api_configs 
ADD COLUMN IF NOT EXISTS token_mode ENUM('exclusive', 'shared') DEFAULT 'exclusive' COMMENT 'Token模式：exclusive=独享，shared=共享'
AFTER auth_key;

-- 添加索引
ALTER TABLE api_configs 
ADD INDEX IF NOT EXISTS idx_token_mode (token_mode);

-- 更新现有记录的token_mode
-- 如果多个节点使用相同的auth_key，则设置为shared模式
UPDATE api_configs a1 
SET token_mode = 'shared' 
WHERE a1.auth_key IS NOT NULL 
  AND a1.auth_key != '' 
  AND LENGTH(a1.auth_key) = 128
  AND EXISTS (
    SELECT 1 FROM api_configs a2 
    WHERE a2.auth_key = a1.auth_key 
      AND a2.id != a1.id
  );

-- 其他记录保持exclusive模式（默认值）
UPDATE api_configs 
SET token_mode = 'exclusive' 
WHERE token_mode IS NULL 
  AND (auth_key IS NULL OR auth_key = '' OR LENGTH(auth_key) != 128);

-- 显示迁移结果
SELECT 
    token_mode,
    COUNT(*) as count,
    COUNT(DISTINCT auth_key) as unique_tokens
FROM api_configs 
WHERE auth_key IS NOT NULL AND auth_key != ''
GROUP BY token_mode;
