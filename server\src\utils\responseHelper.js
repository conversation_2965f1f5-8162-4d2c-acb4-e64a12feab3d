/**
 * 后端API响应格式统一标准
 * 
 * 为了解决系统中存在多种不同的API响应格式问题，
 * 制定统一的响应格式标准，并提供工具函数来确保一致性。
 * 
 * 支持两种格式：
 * 1. 新标准格式：{success: boolean, data: any, message: string, timestamp: string}
 * 2. 兼容旧格式：{code: number, msg: string, data: any}
 */

const { getBeijingTimestamp } = require('./helpers');

/**
 * 标准API响应格式
 * @param {boolean} success - 操作是否成功
 * @param {*} data - 响应数据
 * @param {string} message - 响应消息
 * @param {number} code - 状态码 (可选，用于兼容旧系统)
 * @returns {Object} 标准化的响应对象
 */
function createResponse(success, data = null, message = '', code = null) {
  const response = {
    success,
    data,
    message: message || (success ? '操作成功' : '操作失败'),
    timestamp: getBeijingTimestamp()
  };

  // 为了向后兼容，也包含 code 字段
  if (code !== null) {
    response.code = code;
  } else {
    response.code = success ? 0 : 1;
  }

  // 为了兼容一些旧的API，也包含 msg 字段
  response.msg = response.message;

  return response;
}

/**
 * 创建成功响应
 * @param {*} data - 响应数据
 * @param {string} message - 成功消息
 * @returns {Object} 成功响应对象
 */
function successResponse(data = null, message = '操作成功') {
  return createResponse(true, data, message, 0);
}

/**
 * 创建失败响应
 * @param {string} message - 错误消息
 * @param {number} code - 错误代码
 * @param {*} data - 错误相关数据
 * @returns {Object} 失败响应对象
 */
function errorResponse(message = '操作失败', code = 1, data = null) {
  return createResponse(false, data, message, code);
}

/**
 * 创建分页响应
 * @param {Array} items - 数据项数组
 * @param {number} total - 总数量
 * @param {number} page - 当前页码
 * @param {number} limit - 每页数量
 * @param {string} message - 响应消息
 * @returns {Object} 分页响应对象
 */
function paginatedResponse(items = [], total = 0, page = 1, limit = 20, message = '获取成功') {
  const data = {
    items,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    }
  };

  return successResponse(data, message);
}

/**
 * 网络错误响应
 * @param {number} statusCode - HTTP状态码
 * @param {string} message - 错误消息
 * @returns {Object} 网络错误响应对象
 */
function networkErrorResponse(statusCode = 500, message = '网络错误') {
  return createResponse(false, null, message, statusCode);
}

/**
 * 验证错误响应
 * @param {Object} errors - 验证错误详情
 * @param {string} message - 错误消息
 * @returns {Object} 验证错误响应对象
 */
function validationErrorResponse(errors = {}, message = '数据验证失败') {
  return createResponse(false, { validation_errors: errors }, message, 400);
}

/**
 * 权限错误响应
 * @param {string} message - 错误消息
 * @returns {Object} 权限错误响应对象
 */
function unauthorizedResponse(message = '权限不足') {
  return createResponse(false, null, message, 401);
}

/**
 * 资源不存在响应
 * @param {string} message - 错误消息
 * @returns {Object} 资源不存在响应对象
 */
function notFoundResponse(message = '资源不存在') {
  return createResponse(false, null, message, 404);
}

/**
 * Express中间件：统一响应格式
 * 在res对象上添加便捷的响应方法
 */
function responseMiddleware(req, res, next) {
  // 成功响应
  res.success = (data, message) => {
    return res.json(successResponse(data, message));
  };

  // 错误响应
  res.error = (message, code, data) => {
    const statusCode = code >= 400 && code < 600 ? code : 500;
    return res.status(statusCode).json(errorResponse(message, code, data));
  };

  // 分页响应
  res.paginated = (items, total, page, limit, message) => {
    return res.json(paginatedResponse(items, total, page, limit, message));
  };

  // 验证错误
  res.validationError = (errors, message) => {
    return res.status(400).json(validationErrorResponse(errors, message));
  };

  // 权限错误
  res.unauthorized = (message) => {
    return res.status(401).json(unauthorizedResponse(message));
  };

  // 资源不存在
  res.notFound = (message) => {
    return res.status(404).json(notFoundResponse(message));
  };

  // 网络错误
  res.networkError = (statusCode, message) => {
    return res.status(statusCode).json(networkErrorResponse(statusCode, message));
  };

  next();
}

/**
 * 包装异步路由处理器，自动处理错误
 * @param {Function} handler - 异步路由处理器
 * @returns {Function} 包装后的处理器
 */
function asyncHandler(handler) {
  return async (req, res, next) => {
    try {
      await handler(req, res, next);
    } catch (error) {
      console.error('Async handler error:', error);
      
      // 根据错误类型返回相应的响应
      if (error.name === 'ValidationError') {
        return res.validationError(error.errors, error.message);
      } else if (error.name === 'UnauthorizedError') {
        return res.unauthorized(error.message);
      } else if (error.name === 'NotFoundError') {
        return res.notFound(error.message);
      } else {
        return res.error('服务器内部错误', 500);
      }
    }
  };
}

/**
 * 兼容旧格式的响应转换器
 * 将旧的 {code, msg, data} 格式转换为新的标准格式
 * @param {Object} oldResponse - 旧格式响应
 * @returns {Object} 新格式响应
 */
function convertLegacyResponse(oldResponse) {
  if (!oldResponse || typeof oldResponse !== 'object') {
    return errorResponse('无效的响应格式');
  }

  // 如果已经是新格式，直接返回
  if (oldResponse.success !== undefined) {
    return oldResponse;
  }

  // 转换旧格式
  const success = oldResponse.code === 0;
  const message = oldResponse.msg || oldResponse.message || (success ? '操作成功' : '操作失败');
  const data = oldResponse.data;
  const code = oldResponse.code;

  return createResponse(success, data, message, code);
}

/**
 * 响应格式标准化中间件
 * 拦截所有响应并确保格式统一
 */
function responseFormatterMiddleware(req, res, next) {
  // 保存原始的 json 方法
  const originalJson = res.json;
  
  // 重写 json 方法
  res.json = function(data) {
    // 如果已经是标准格式，直接返回
    if (data && typeof data === 'object' && 
        (data.hasOwnProperty('success') || data.hasOwnProperty('code'))) {
      
      // 如果是旧格式 {code, msg, data}，转换为新格式
      if (data.hasOwnProperty('code') && data.hasOwnProperty('msg')) {
        const standardizedData = {
          success: data.code === 0,
          code: data.code,
          message: data.msg,
          data: data.data,
          timestamp: getBeijingTimestamp()
        };
        return originalJson.call(this, standardizedData);
      }
      
      // 如果是新格式但缺少字段，补充字段
      if (data.hasOwnProperty('success')) {
        const completeData = {
          success: data.success,
          code: data.code || (data.success ? 0 : 1),
          message: data.message || (data.success ? '操作成功' : '操作失败'),
          data: data.data,
          timestamp: data.timestamp || getBeijingTimestamp()
        };
        return originalJson.call(this, completeData);
      }
      
      return originalJson.call(this, data);
    }
    
    // 如果不是标准格式，包装成成功响应
    return originalJson.call(this, successResponse(data));
  };
  
  // 添加便捷的响应方法
  res.success = function(data, message, code) {
    return this.json(successResponse(data, message, code));
  };
  
  res.error = function(message, code, data) {
    return this.json(errorResponse(message, code, data));
  };
  
  res.paginated = function(items, pagination, message) {
    return this.json(paginatedResponse(items, pagination, message));
  };
  
  next();
}

/**
 * 增强版错误处理中间件
 * 确保错误也使用统一的响应格式
 */
function enhancedErrorHandlerMiddleware(err, req, res, next) {
  console.error('API错误:', {
    url: req.url,
    method: req.method,
    error: err.message,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
    timestamp: getBeijingTimestamp()
  });
  
  // 如果响应已经发送，则跳过
  if (res.headersSent) {
    return next(err);
  }
  
  // 根据错误类型设置状态码和消息
  let statusCode = 500;
  let message = '服务器内部错误';
  let code = 500;
  
  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = '请求参数验证失败';
    code = 400;
  } else if (err.name === 'UnauthorizedError' || err.message.includes('unauthorized')) {
    statusCode = 401;
    message = '未授权访问';
    code = 401;
  } else if (err.name === 'NotFoundError' || err.message.includes('not found')) {
    statusCode = 404;
    message = '资源不存在';
    code = 404;
  } else if (err.message) {
    message = err.message;
  }
  
  // 开发环境下返回详细错误信息
  const errorData = process.env.NODE_ENV === 'development' ? {
    stack: err.stack,
    details: err
  } : null;
  
  res.status(statusCode).json(errorResponse(message, code, errorData));
}

module.exports = {
  createResponse,
  successResponse,
  errorResponse,
  paginatedResponse,
  networkErrorResponse,
  validationErrorResponse,
  unauthorizedResponse,
  notFoundResponse,
  responseMiddleware,
  asyncHandler,
  convertLegacyResponse,
  responseFormatterMiddleware,
  enhancedErrorHandlerMiddleware
};
