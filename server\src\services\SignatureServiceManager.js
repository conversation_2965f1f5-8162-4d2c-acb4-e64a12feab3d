const OptimizedSignatureService = require('./OptimizedSignatureService');
const colors = require('colors');

/**
 * 签名服务管理器 - 单例模式
 * 统一管理签名服务实例，提供全局访问点
 */
class SignatureServiceManager {
    constructor() {
        if (SignatureServiceManager.instance) {
            return SignatureServiceManager.instance;
        }
        
        this.signatureService = null;
        this.isInitialized = false;
        this.dataModel = null;
        
        SignatureServiceManager.instance = this;
    }
    
    /**
     * 初始化签名服务
     */
    async initialize(dataModel, options = {}) {
        if (this.isInitialized) {
            console.log('⚠️  签名服务已经初始化'.yellow);
            return this.signatureService;
        }
        
        try {
            this.dataModel = dataModel;
            
            // 创建优化的签名服务实例
            this.signatureService = new OptimizedSignatureService({
                maxConcurrency: options.maxConcurrency || 100,
                maxQueueSize: options.maxQueueSize || 2000,
                requestTimeout: options.requestTimeout || 30000,
                retryAttempts: options.retryAttempts || 3,
                retryDelay: options.retryDelay || 1000,
                healthCheckInterval: options.healthCheckInterval || 30000,
                nodeTimeout: options.nodeTimeout || 15000,
                maxConnectionsPerNode: options.maxConnectionsPerNode || 20,
                loadBalanceStrategy: options.loadBalanceStrategy || 'least_connections'
            });
            
            // 加载节点配置
            const nodeCount = await this.signatureService.loadNodesFromConfig(dataModel);
            
            if (nodeCount === 0) {
                console.log('⚠️  没有可用的签名节点，请先配置节点'.yellow);
            }
            
            // 设置事件监听
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log(`🚀 签名服务管理器初始化完成 - 加载了 ${nodeCount} 个节点`.green);
            
            return this.signatureService;
            
        } catch (error) {
            console.error('签名服务初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 设置事件监听
     */
    setupEventListeners() {
        if (!this.signatureService) return;
        
        // 监听请求完成事件
        this.signatureService.on('requestCompleted', (data) => {
            console.log(`✅ 签名请求完成: ${data.requestId}`.green);
        });
        
        // 监听请求失败事件
        this.signatureService.on('requestFailed', (data) => {
            console.log(`❌ 签名请求失败: ${data.requestId} - ${data.error.message}`.red);
        });
        
        // 监听队列状态变化
        this.signatureService.on('requestAdded', (data) => {
            if (data.queueLength > 100) {
                console.log(`⚠️  队列长度较高: ${data.queueLength}`.yellow);
            }
        });
    }
    
    /**
     * 获取签名服务实例
     */
    getService() {
        if (!this.isInitialized) {
            throw new Error('签名服务未初始化，请先调用 initialize()');
        }
        return this.signatureService;
    }
    
    /**
     * 请求签名 - 支持原有数据格式
     */
    async requestSignature(requestData, originalFormat = 'form', priority = 'normal') {
        const service = this.getService();

        // 提取基本信息用于统计和路由
        const { extractUin, extractCmd } = require('../utils/helpers');
        const uin = extractUin(requestData);
        const cmd = extractCmd(requestData);

        const signatureData = {
            cmd,
            uin,
            requestData,
            originalFormat
        };

        return await service.addRequest(signatureData, priority);
    }

    /**
     * 请求签名 - 新格式（向后兼容）
     */
    async requestSignatureNew(cmd, uin, qua, buffer, priority = 'normal') {
        // 构造类似原有格式的数据
        const params = new URLSearchParams();
        params.append('uin', uin);
        params.append('cmd', cmd);
        if (qua) params.append('qua', qua);
        if (buffer) params.append('buffer', buffer);

        return await this.requestSignature(params.toString(), 'json', priority);
    }
    
    /**
     * 重新加载节点配置
     */
    async reloadNodes() {
        if (!this.isInitialized || !this.dataModel) {
            throw new Error('服务未初始化');
        }
        
        try {
            // 清空现有节点
            const service = this.getService();
            const currentNodes = Array.from(service.nodes.keys());
            currentNodes.forEach(nodeId => service.removeNode(nodeId));
            
            // 重新加载
            const nodeCount = await service.loadNodesFromConfig(this.dataModel);
            console.log(`🔄 重新加载了 ${nodeCount} 个签名节点`.green);
            
            return nodeCount;
        } catch (error) {
            console.error('重新加载节点失败:', error);
            throw error;
        }
    }
    
    /**
     * 添加单个节点
     */
    async addNode(nodeConfig) {
        const service = this.getService();
        service.addNode(nodeConfig);
    }
    
    /**
     * 移除节点
     */
    async removeNode(nodeId) {
        const service = this.getService();
        service.removeNode(nodeId);
    }
    
    /**
     * 获取服务状态
     */
    getStatus() {
        if (!this.isInitialized) {
            return {
                initialized: false,
                message: '服务未初始化'
            };
        }
        
        const service = this.getService();
        return {
            initialized: true,
            ...service.getServiceStatus()
        };
    }
    
    /**
     * 获取性能统计
     */
    getPerformanceStats() {
        const status = this.getStatus();
        if (!status.initialized) {
            return null;
        }
        
        return {
            queue: {
                totalRequests: status.totalRequests,
                completedRequests: status.completedRequests,
                failedRequests: status.failedRequests,
                currentQueueLength: status.queueLength,
                averageResponseTime: status.averageResponseTime,
                successRate: status.totalRequests > 0 ? 
                    ((status.completedRequests / status.totalRequests) * 100).toFixed(2) : '0.00'
            },
            nodes: status.nodes.map(node => ({
                nodeId: node.nodeId,
                baseUrl: node.baseUrl,
                isHealthy: node.isHealthy,
                connectionUsage: `${node.connectionCount}/${node.maxConnections}`,
                totalRequests: node.stats.totalRequests || 0,
                successRate: node.stats.totalRequests > 0 ? 
                    (100 - parseFloat(node.stats.errorRate || 0)).toFixed(2) : '0.00',
                averageResponseTime: node.stats.averageResponseTime || 0
            })),
            recommendations: this.getService().getNodeRecommendation()
        };
    }
    
    /**
     * 健康检查
     */
    async healthCheck() {
        if (!this.isInitialized) {
            return { healthy: false, message: '服务未初始化' };
        }
        
        const service = this.getService();
        await service.performHealthCheck();
        
        const status = this.getStatus();
        const healthyNodeCount = status.healthyNodes;
        const totalNodeCount = status.totalNodes;
        
        return {
            healthy: healthyNodeCount > 0,
            healthyNodes: healthyNodeCount,
            totalNodes: totalNodeCount,
            healthRatio: totalNodeCount > 0 ? (healthyNodeCount / totalNodeCount * 100).toFixed(2) : '0.00',
            message: healthyNodeCount > 0 ? '服务正常' : '没有健康的节点'
        };
    }
    
    /**
     * 优雅关闭
     */
    async shutdown() {
        if (this.signatureService) {
            console.log('🛑 正在关闭签名服务...'.yellow);
            this.signatureService.clear();
            this.signatureService = null;
        }
        
        this.isInitialized = false;
        console.log('✅ 签名服务已关闭'.green);
    }
    
    /**
     * 获取单例实例
     */
    static getInstance() {
        if (!SignatureServiceManager.instance) {
            SignatureServiceManager.instance = new SignatureServiceManager();
        }
        return SignatureServiceManager.instance;
    }
}

// 导出单例实例
module.exports = SignatureServiceManager.getInstance();
