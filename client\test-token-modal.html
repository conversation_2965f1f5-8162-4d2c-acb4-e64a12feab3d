<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token模态框测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 24px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .token-display {
            padding: 16px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            word-break: break-all;
            font-family: Consolas, Monaco, "Courier New", monospace;
            font-size: 13px;
            line-height: 1.5;
            color: #495057;
            user-select: all;
            cursor: text;
            margin: 16px 0;
        }
        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 8px;
        }
        .btn:hover {
            background: #40a9ff;
        }
        .alert {
            padding: 12px;
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            color: #0050b3;
            margin: 16px 0;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 24px;
            border-radius: 8px;
            width: 90%;
            max-width: 700px;
            max-height: 80vh;
            overflow-y: auto;
        }
        .modal-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 16px;
            font-size: 16px;
            font-weight: bold;
        }
        .close-btn {
            position: absolute;
            top: 16px;
            right: 16px;
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Token模态框功能测试</h1>
        
        <p>这个页面用于测试Token显示模态框的功能，模拟前端TokenManager组件的行为。</p>
        
        <h2>测试Token示例</h2>
        <div>
            <strong>节点类型:</strong> QQ_测试节点
        </div>
        <div>
            <strong>Token预览:</strong> a1b2c3d4...xyz9
        </div>
        
        <button class="btn" onclick="showTokenModal()">
            📋 查看完整Token
        </button>
        
        <h2>功能说明</h2>
        <ul>
            <li>✅ 点击按钮显示Token模态框</li>
            <li>✅ Token文本可以手动选择复制</li>
            <li>✅ 美观的界面设计</li>
            <li>✅ 响应式布局</li>
        </ul>
        
        <div class="alert">
            💡 <strong>提示:</strong> 如果这个测试页面正常工作，说明模态框功能本身没有问题，可能是React组件的导入或状态管理问题。
        </div>
    </div>

    <!-- 模态框 -->
    <div id="tokenModal" class="modal">
        <div class="modal-content">
            <button class="close-btn" onclick="closeModal()">&times;</button>
            <div class="modal-header">
                <span>📋</span>
                <span>Token详情 - QQ_测试节点</span>
            </div>
            
            <div>
                <p style="margin: 0; font-weight: bold; color: #333;">
                    节点类型: <span style="color: #1890ff;">QQ_测试节点</span>
                </p>
            </div>
            
            <div style="margin: 16px 0;">
                <p style="margin: 0 0 8px 0; font-weight: bold; color: #333;">
                    Token:
                </p>
                <div class="token-display">
                    a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2g3h4i5j6k7l8m9n0o1p2q3r4s5t6u7v8w9x0y1z2a3b4c5d6e7f8g9h0i1j2k3l4m5n6o7p8q9r0s1t2u3v4w5x6y7z8a9b0c1d2e3f4g5h6i7j8k9l0m1n2o3p4q5r6s7t8u9v0w1x2y3z4
                </div>
            </div>
            
            <div class="alert">
                <strong>使用说明:</strong> 请手动选择上面的Token文本并复制（Ctrl+C），然后在需要的地方粘贴使用。
            </div>
            
            <button class="btn" onclick="closeModal()">关闭</button>
        </div>
    </div>

    <script>
        function showTokenModal() {
            console.log('显示Token模态框');
            document.getElementById('tokenModal').style.display = 'block';
        }
        
        function closeModal() {
            console.log('关闭Token模态框');
            document.getElementById('tokenModal').style.display = 'none';
        }
        
        // 点击模态框背景关闭
        document.getElementById('tokenModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
        
        // ESC键关闭
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
        
        console.log('Token模态框测试页面已加载');
    </script>
</body>
</html>
