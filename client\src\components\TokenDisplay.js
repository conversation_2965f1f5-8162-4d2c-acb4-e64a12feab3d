import { Button } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import Swal from 'sweetalert2';

const TokenDisplay = ({ token, nodeType, onShow }) => {
  const handleShowToken = () => {
    console.log('TokenDisplay: 显示Token', { nodeType, hasToken: !!token });
    
    if (!token) {
      Swal.fire({
        title: '错误',
        text: 'Token不存在',
        icon: 'error',
        confirmButtonText: '确定'
      });
      return;
    }

    Swal.fire({
      title: `🔐 ${nodeType} - Token详情`,
      html: `
        <div style="text-align: left; margin: 20px 0;">
          <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 8px 0; color: #333;">节点类型:</h4>
            <div style="padding: 8px 12px; background: #f0f8ff; border-radius: 4px; color: #1890ff; font-weight: bold;">
              ${nodeType}
            </div>
          </div>
          
          <div style="margin-bottom: 20px;">
            <h4 style="margin: 0 0 8px 0; color: #333;">Token:</h4>
            <textarea readonly style="
              width: 100%; 
              height: 120px;
              padding: 12px; 
              background: #f8f9fa; 
              border: 1px solid #dee2e6;
              border-radius: 4px; 
              font-family: 'Courier New', Consolas, monospace;
              font-size: 11px;
              line-height: 1.4;
              color: #495057;
              resize: none;
              word-break: break-all;
              box-sizing: border-box;
            ">${token}</textarea>
          </div>
          
          <div style="
            padding: 12px; 
            background: #e6f7ff; 
            border: 1px solid #91d5ff; 
            border-radius: 4px; 
            color: #0050b3;
            font-size: 13px;
          ">
            <strong>💡 使用说明:</strong><br>
            1. 选中上面文本框中的所有内容<br>
            2. 使用 Ctrl+C (Windows) 或 Cmd+C (Mac) 复制<br>
            3. 在需要的地方粘贴使用
          </div>
        </div>
      `,
      width: 600,
      confirmButtonText: '关闭',
      confirmButtonColor: '#1890ff',
      allowOutsideClick: true,
      allowEscapeKey: true,
      didOpen: () => {
        console.log('SweetAlert2 Token模态框已打开');
        // 自动选中textarea中的文本
        setTimeout(() => {
          const textarea = document.querySelector('.swal2-popup textarea');
          if (textarea) {
            textarea.focus();
            textarea.select();
          }
        }, 100);
      },
      willClose: () => {
        console.log('SweetAlert2 Token模态框即将关闭');
      }
    });
  };

  return (
    <Button
      type="text"
      size="small"
      icon={<CopyOutlined />}
      onClick={handleShowToken}
      title="查看完整Token"
    />
  );
};

export default TokenDisplay;
