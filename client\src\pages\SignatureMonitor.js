import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Progress,
  Tag,
  Button,
  Space,
  Alert,
  Tooltip,
  message
} from 'antd';
import {
  ReloadOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ApiOutlined
} from '@ant-design/icons';
import api from '../services/api';

const SignatureMonitor = () => {
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState(null);
  const [performance, setPerformance] = useState(null);
  const [health, setHealth] = useState(null);

  // 获取服务状态
  const fetchStatus = async () => {
    try {
      const response = await api.get('/sign/status');
      setStatus(response.data.data);
    } catch (error) {
      console.error('获取服务状态失败:', error);
    }
  };

  // 获取性能统计
  const fetchPerformance = async () => {
    try {
      const response = await api.get('/sign/performance');
      setPerformance(response.data.data);
    } catch (error) {
      console.error('获取性能统计失败:', error);
    }
  };

  // 获取健康状态
  const fetchHealth = async () => {
    try {
      const response = await api.get('/sign/health');
      setHealth(response.data.data);
    } catch (error) {
      console.error('获取健康状态失败:', error);
    }
  };

  // 重新加载节点
  const handleReloadNodes = async () => {
    try {
      setLoading(true);
      const response = await api.post('/sign/reload-nodes');
      message.success(response.data.msg);
      await fetchAll();
    } catch (error) {
      message.error('重新加载节点失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取所有数据
  const fetchAll = async () => {
    setLoading(true);
    try {
      await Promise.all([
        fetchStatus(),
        fetchPerformance(),
        fetchHealth()
      ]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAll();
    
    // 定期刷新数据
    const interval = setInterval(fetchAll, 5000);
    return () => clearInterval(interval);
  }, []);

  // 节点表格列定义
  const nodeColumns = [
    {
      title: '节点ID',
      dataIndex: 'nodeId',
      key: 'nodeId',
      render: (text) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '节点地址',
      dataIndex: 'baseUrl',
      key: 'baseUrl',
      ellipsis: true
    },
    {
      title: '健康状态',
      dataIndex: 'isHealthy',
      key: 'isHealthy',
      render: (healthy) => (
        <Tag color={healthy ? 'green' : 'red'} icon={healthy ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}>
          {healthy ? '健康' : '异常'}
        </Tag>
      )
    },
    {
      title: '连接使用率',
      dataIndex: 'connectionUsage',
      key: 'connectionUsage',
      render: (usage) => {
        const [current, max] = usage.split('/').map(Number);
        const percent = max > 0 ? (current / max * 100) : 0;
        return (
          <div style={{ width: 120 }}>
            <Progress 
              percent={percent} 
              size="small" 
              status={percent > 80 ? 'exception' : 'normal'}
              format={() => usage}
            />
          </div>
        );
      }
    },
    {
      title: '总请求数',
      dataIndex: 'totalRequests',
      key: 'totalRequests',
      render: (num) => num.toLocaleString()
    },
    {
      title: '成功率',
      dataIndex: 'successRate',
      key: 'successRate',
      render: (rate) => (
        <Tag color={rate >= 95 ? 'green' : rate >= 90 ? 'orange' : 'red'}>
          {rate}%
        </Tag>
      )
    },
    {
      title: '平均响应时间',
      dataIndex: 'averageResponseTime',
      key: 'averageResponseTime',
      render: (time) => `${time}ms`
    }
  ];

  if (!status || !performance || !health) {
    return (
      <div style={{ padding: 24, textAlign: 'center' }}>
        <div>加载中...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2 style={{ margin: 0 }}>签名服务监控</h2>
        <Space>
          <Button 
            type="primary" 
            icon={<ReloadOutlined />} 
            onClick={handleReloadNodes}
            loading={loading}
          >
            重新加载节点
          </Button>
          <Button 
            icon={<ReloadOutlined />} 
            onClick={fetchAll}
            loading={loading}
          >
            刷新数据
          </Button>
        </Space>
      </div>

      {/* 健康状态警告 */}
      {!health.healthy && (
        <Alert
          message="签名服务异常"
          description={health.message}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {/* 总体统计 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="队列长度"
              value={status.queueLength}
              prefix={<ApiOutlined />}
              valueStyle={{ color: status.queueLength > 100 ? '#cf1322' : '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="当前并发"
              value={status.currentConcurrency}
              suffix={`/ ${status.maxConcurrency || 100}`}
              prefix={<ThunderboltOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均响应时间"
              value={performance.queue.averageResponseTime}
              suffix="ms"
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="成功率"
              value={performance.queue.successRate}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: performance.queue.successRate >= 95 ? '#3f8600' : '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 队列统计 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Card title="队列统计" size="small">
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="总请求数"
                  value={performance.queue.totalRequests}
                  valueStyle={{ fontSize: 16 }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="成功请求"
                  value={performance.queue.completedRequests}
                  valueStyle={{ fontSize: 16, color: '#3f8600' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="失败请求"
                  value={performance.queue.failedRequests}
                  valueStyle={{ fontSize: 16, color: '#cf1322' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
        <Col span={12}>
          <Card title="节点健康" size="small">
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="总节点数"
                  value={health.totalNodes}
                  valueStyle={{ fontSize: 16 }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="健康节点"
                  value={health.healthyNodes}
                  valueStyle={{ fontSize: 16, color: '#3f8600' }}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="健康率"
                  value={health.healthRatio}
                  suffix="%"
                  valueStyle={{ fontSize: 16, color: health.healthRatio >= 80 ? '#3f8600' : '#cf1322' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* 节点详情表格 */}
      <Card title="节点详情" size="small">
        <Table
          columns={nodeColumns}
          dataSource={performance.nodes}
          rowKey="nodeId"
          size="small"
          pagination={false}
          scroll={{ x: 800 }}
        />
      </Card>

      {/* 推荐信息 */}
      {performance.recommendations && (
        <Card title="性能建议" size="small" style={{ marginTop: 16 }}>
          {performance.recommendations.recommendation === 'no_healthy_nodes' ? (
            <Alert
              message="没有健康的节点"
              description="请检查节点配置和网络连接"
              type="error"
              showIcon
            />
          ) : (
            <div>
              <p><strong>最佳节点:</strong> {performance.recommendations.bestNode?.nodeId}</p>
              <p><strong>性能分数:</strong> {performance.recommendations.bestNode?.performance}/100</p>
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default SignatureMonitor;
