import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Badge, Tag, Button, Spin, message, Typography, Space, Divider } from 'antd';
import {
  ReloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ApiOutlined,
  ClockCircleOutlined,
  SafetyOutlined,
  GlobalOutlined
} from '@ant-design/icons';
import axios from 'axios';
import { getApiUrl } from '../services/api';

// 添加CSS动画
const styles = `
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
  }

  @keyframes shimmer {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
  }

  .floating-element {
    animation: float 6s ease-in-out infinite;
  }

  .pulse-element {
    animation: pulse 2s ease-in-out infinite;
  }
`;

// 注入样式
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.type = 'text/css';
  styleSheet.innerText = styles;
  document.head.appendChild(styleSheet);
}

const { Title, Text } = Typography;

const PublicNodeStatus = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(null);

  // 获取节点状态数据
  const fetchNodeStatus = async () => {
    try {
      setLoading(true);

      // 获取正确的API基础URL
      const apiBaseUrl = await getApiUrl();

      // 构建正确的公开状态API URL
      let baseUrl;
      if (apiBaseUrl.endsWith('/api')) {
        // 如果以/api结尾，去掉/api
        baseUrl = apiBaseUrl.slice(0, -4);
      } else if (apiBaseUrl.includes('/api/')) {
        // 如果包含/api/，取/api之前的部分
        baseUrl = apiBaseUrl.split('/api/')[0];
      } else {
        // 其他情况，直接使用
        baseUrl = apiBaseUrl;
      }

      const apiUrl = `${baseUrl}/api/public/nodes/status`;

      console.log('API基础URL:', apiBaseUrl);
      console.log('构建的请求URL:', apiUrl);
      const response = await axios.get(apiUrl);

      if (response.data && response.data.code === 0) {
        setData(response.data.data);
        setLastUpdate(new Date());
        message.success('节点状态更新成功');
      } else {
        message.error('获取节点状态失败');
      }
    } catch (error) {
      console.error('获取节点状态失败:', error);
      message.error('网络错误，无法获取节点状态');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    fetchNodeStatus();
    
    // 设置自动刷新（每30秒）
    const interval = setInterval(fetchNodeStatus, 30000);
    
    return () => clearInterval(interval);
  }, []);

  // 获取状态颜色
  const getStatusColor = (status, isHealthy) => {
    // 优先使用健康检查结果
    if (isHealthy !== undefined) {
      return isHealthy ? 'success' : 'error';
    }
    // 兼容旧版本API
    return status === 'active' ? 'success' : 'error';
  };

  // 获取状态文本
  const getStatusText = (status, isHealthy) => {
    // 优先使用健康检查结果
    if (isHealthy !== undefined) {
      return isHealthy ? '运行中' : '已停用';
    }
    // 兼容旧版本API
    return status === 'active' ? '运行中' : '已停用';
  };

  // 获取Token模式标签
  const getTokenModeTag = (hasToken, tokenMode) => {
    if (!hasToken) {
      return <Tag color="red">无Token</Tag>;
    }

    if (tokenMode === 'exclusive') {
      return <Tag color="orange">独享模式</Tag>;
    } else {
      return <Tag color="blue">共享模式</Tag>;
    }
  };

  if (loading && !data) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>正在加载节点状态...</Text>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Text type="danger">无法获取节点状态数据</Text>
        <div style={{ marginTop: 16 }}>
          <Button type="primary" icon={<ReloadOutlined />} onClick={fetchNodeStatus}>
            重新加载
          </Button>
        </div>
      </div>
    );
  }

  const { nodes, stats } = data;

  return (
    <div style={{
      padding: 0,
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      position: 'relative',
      overflow: 'hidden'
    }}>
      {/* 背景装饰 */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%)
        `,
        zIndex: 1
      }} />

      {/* 浮动元素装饰 */}
      <div style={{
        position: 'absolute',
        top: '10%',
        left: '5%',
        width: '100px',
        height: '100px',
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '50%',
        animation: 'float 6s ease-in-out infinite',
        zIndex: 1
      }} />
      <div style={{
        position: 'absolute',
        top: '60%',
        right: '10%',
        width: '150px',
        height: '150px',
        background: 'rgba(255, 255, 255, 0.05)',
        borderRadius: '50%',
        animation: 'float 8s ease-in-out infinite reverse',
        zIndex: 1
      }} />

      <div style={{
        position: 'relative',
        zIndex: 2,
        padding: '40px 24px'
      }}>
        {/* 页面标题 */}
        <div style={{
          marginBottom: 40,
          textAlign: 'center',
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          borderRadius: '20px',
          padding: '40px 20px',
          boxShadow: '0 20px 40px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)',
          border: '1px solid rgba(255, 255, 255, 0.3)'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            fontSize: '48px',
            fontWeight: 'bold',
            marginBottom: '16px',
            textShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            🌸 签名服务节点状态
          </div>
          <Text style={{
            fontSize: '18px',
            color: '#666',
            display: 'block',
            marginBottom: '24px'
          }}>
            实时监控所有签名服务节点的运行状态
          </Text>
          <Space size="large">
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={fetchNodeStatus}
              loading={loading}
              size="large"
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '12px',
                height: '48px',
                padding: '0 32px',
                fontSize: '16px',
                fontWeight: '500',
                boxShadow: '0 8px 16px rgba(102, 126, 234, 0.3)',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 12px 24px rgba(102, 126, 234, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 8px 16px rgba(102, 126, 234, 0.3)';
              }}
            >
              刷新状态
            </Button>
            {lastUpdate && (
              <div style={{
                background: 'rgba(102, 126, 234, 0.1)',
                padding: '8px 16px',
                borderRadius: '20px',
                border: '1px solid rgba(102, 126, 234, 0.2)'
              }}>
                <Text style={{ color: '#667eea', fontSize: '14px' }}>
                  最后更新: {lastUpdate.toLocaleString('zh-CN')}
                </Text>
              </div>
            )}
          </Space>
        </div>

        {/* 统计卡片 */}
        <Row gutter={[24, 24]} style={{ marginBottom: 40 }}>
          <Col xs={12} sm={8} md={6} lg={4}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(20px)',
              borderRadius: '20px',
              padding: '24px',
              textAlign: 'center',
              boxShadow: '0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)',
              border: '1px solid rgba(24, 144, 255, 0.2)',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)';
              e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)';
            }}
            >
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '4px',
                background: 'linear-gradient(90deg, #1890ff, #40a9ff)',
                borderRadius: '20px 20px 0 0'
              }} />
              <ApiOutlined style={{
                fontSize: '32px',
                color: '#1890ff',
                marginBottom: '12px',
                display: 'block'
              }} />
              <div style={{
                fontSize: '32px',
                fontWeight: 'bold',
                color: '#1890ff',
                marginBottom: '8px'
              }}>
                {stats.totalNodes}
              </div>
              <div style={{ color: '#666', fontSize: '14px', fontWeight: '500' }}>
                总节点数
              </div>
            </div>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(20px)',
              borderRadius: '20px',
              padding: '24px',
              textAlign: 'center',
              boxShadow: '0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)',
              border: '1px solid rgba(82, 196, 26, 0.2)',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)';
              e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)';
            }}
            >
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '4px',
                background: 'linear-gradient(90deg, #52c41a, #73d13d)',
                borderRadius: '20px 20px 0 0'
              }} />
              <CheckCircleOutlined style={{
                fontSize: '32px',
                color: '#52c41a',
                marginBottom: '12px',
                display: 'block'
              }} />
              <div style={{
                fontSize: '32px',
                fontWeight: 'bold',
                color: '#52c41a',
                marginBottom: '8px'
              }}>
                {stats.healthyNodes !== undefined ? stats.healthyNodes : stats.activeNodes}
              </div>
              <div style={{ color: '#666', fontSize: '14px', fontWeight: '500' }}>
                健康节点
              </div>
            </div>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(20px)',
              borderRadius: '20px',
              padding: '24px',
              textAlign: 'center',
              boxShadow: '0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)',
              border: '1px solid rgba(114, 46, 209, 0.2)',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)';
              e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)';
            }}
            >
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '4px',
                background: 'linear-gradient(90deg, #722ed1, #9254de)',
                borderRadius: '20px 20px 0 0'
              }} />
              <ClockCircleOutlined style={{
                fontSize: '32px',
                color: '#722ed1',
                marginBottom: '12px',
                display: 'block'
              }} />
              <div style={{
                fontSize: '32px',
                fontWeight: 'bold',
                color: '#722ed1',
                marginBottom: '8px'
              }}>
                {stats.totalTodayCalls || 0}
              </div>
              <div style={{ color: '#666', fontSize: '14px', fontWeight: '500' }}>
                今日调用
              </div>
            </div>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(20px)',
              borderRadius: '20px',
              padding: '24px',
              textAlign: 'center',
              boxShadow: '0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)',
              border: '1px solid rgba(250, 140, 22, 0.2)',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)';
              e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)';
            }}
            >
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '4px',
                background: 'linear-gradient(90deg, #fa8c16, #ffa940)',
                borderRadius: '20px 20px 0 0'
              }} />
              <GlobalOutlined style={{
                fontSize: '32px',
                color: '#fa8c16',
                marginBottom: '12px',
                display: 'block'
              }} />
              <div style={{
                fontSize: '32px',
                fontWeight: 'bold',
                color: '#fa8c16',
                marginBottom: '8px'
              }}>
                {stats.totalAllCalls || 0}
              </div>
              <div style={{ color: '#666', fontSize: '14px', fontWeight: '500' }}>
                总调用数
              </div>
            </div>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(20px)',
              borderRadius: '20px',
              padding: '24px',
              textAlign: 'center',
              boxShadow: '0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)',
              border: `1px solid ${(stats.avgSuccessRate || 0) >= 95 ? 'rgba(82, 196, 26, 0.2)' :
                                 (stats.avgSuccessRate || 0) >= 80 ? 'rgba(250, 173, 20, 0.2)' : 'rgba(255, 77, 79, 0.2)'}`,
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)';
              e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)';
            }}
            >
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '4px',
                background: (stats.avgSuccessRate || 0) >= 95
                  ? 'linear-gradient(90deg, #52c41a, #73d13d)'
                  : (stats.avgSuccessRate || 0) >= 80
                  ? 'linear-gradient(90deg, #faad14, #ffc53d)'
                  : 'linear-gradient(90deg, #ff4d4f, #ff7875)',
                borderRadius: '20px 20px 0 0'
              }} />
              <SafetyOutlined style={{
                fontSize: '32px',
                color: (stats.avgSuccessRate || 0) >= 95 ? '#52c41a' :
                       (stats.avgSuccessRate || 0) >= 80 ? '#faad14' : '#ff4d4f',
                marginBottom: '12px',
                display: 'block'
              }} />
              <div style={{
                fontSize: '32px',
                fontWeight: 'bold',
                color: (stats.avgSuccessRate || 0) >= 95 ? '#52c41a' :
                       (stats.avgSuccessRate || 0) >= 80 ? '#faad14' : '#ff4d4f',
                marginBottom: '8px'
              }}>
                {stats.avgSuccessRate || 0}%
              </div>
              <div style={{ color: '#666', fontSize: '14px', fontWeight: '500' }}>
                平均成功率
              </div>
            </div>
          </Col>
          <Col xs={12} sm={8} md={6} lg={4}>
            <div style={{
              background: 'rgba(255, 255, 255, 0.9)',
              backdropFilter: 'blur(20px)',
              borderRadius: '20px',
              padding: '24px',
              textAlign: 'center',
              boxShadow: '0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)',
              border: '1px solid rgba(19, 194, 194, 0.2)',
              transition: 'all 0.3s ease',
              cursor: 'pointer',
              position: 'relative',
              overflow: 'hidden'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-8px)';
              e.currentTarget.style.boxShadow = '0 20px 40px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.2)';
            }}
            >
              <div style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '4px',
                background: 'linear-gradient(90deg, #13c2c2, #36cfc9)',
                borderRadius: '20px 20px 0 0'
              }} />
              <SafetyOutlined style={{
                fontSize: '32px',
                color: '#13c2c2',
                marginBottom: '12px',
                display: 'block'
              }} />
              <div style={{
                fontSize: '32px',
                fontWeight: 'bold',
                color: '#13c2c2',
                marginBottom: '8px'
              }}>
                {stats.nodesWithToken}
              </div>
              <div style={{ color: '#666', fontSize: '14px', fontWeight: '500' }}>
                已配置Token
              </div>
            </div>
          </Col>
        </Row>

        {/* 节点列表 */}
        <Row gutter={[24, 24]}>
          {nodes.map((node, index) => (
            <Col xs={24} sm={12} lg={8} xl={6} key={index}>
              <div style={{
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(20px)',
                borderRadius: '24px',
                padding: '24px',
                height: '100%',
                boxShadow: '0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.3)',
                border: `2px solid ${node.status === 'active' ? 'rgba(82, 196, 26, 0.3)' : 'rgba(255, 77, 79, 0.3)'}`,
                transition: 'all 0.4s ease',
                cursor: 'pointer',
                position: 'relative',
                overflow: 'hidden'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-12px) scale(1.02)';
                e.currentTarget.style.boxShadow = '0 25px 50px rgba(0,0,0,0.15), 0 0 0 1px rgba(255,255,255,0.4)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0) scale(1)';
                e.currentTarget.style.boxShadow = '0 15px 35px rgba(0,0,0,0.1), 0 0 0 1px rgba(255,255,255,0.3)';
              }}
              >
                {/* 状态指示条 */}
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  height: '6px',
                  background: (node.isHealthy !== undefined ? node.isHealthy : node.status === 'active')
                    ? 'linear-gradient(90deg, #52c41a, #73d13d, #95de64)'
                    : 'linear-gradient(90deg, #ff4d4f, #ff7875, #ffa39e)',
                  borderRadius: '24px 24px 0 0'
                }} />

                {/* 节点标题 */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  marginBottom: '20px',
                  marginTop: '8px'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{
                      background: (node.isHealthy !== undefined ? node.isHealthy : node.status === 'active')
                        ? 'linear-gradient(135deg, #52c41a, #73d13d)'
                        : 'linear-gradient(135deg, #ff4d4f, #ff7875)',
                      borderRadius: '12px',
                      padding: '8px',
                      marginRight: '12px'
                    }}>
                      <ApiOutlined style={{ color: 'white', fontSize: '18px' }} />
                    </div>
                    <div>
                      <div style={{
                        fontSize: '18px',
                        fontWeight: 'bold',
                        color: '#333',
                        marginBottom: '4px'
                      }}>
                        {node.nodeType}
                      </div>
                      <div style={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        background: (node.isHealthy !== undefined ? node.isHealthy : node.status === 'active')
                          ? 'rgba(82, 196, 26, 0.1)'
                          : 'rgba(255, 77, 79, 0.1)',
                        color: (node.isHealthy !== undefined ? node.isHealthy : node.status === 'active')
                          ? '#52c41a' : '#ff4d4f',
                        padding: '4px 12px',
                        borderRadius: '12px',
                        fontSize: '12px',
                        fontWeight: '500'
                      }}>
                        <div style={{
                          width: '6px',
                          height: '6px',
                          borderRadius: '50%',
                          background: (node.isHealthy !== undefined ? node.isHealthy : node.status === 'active')
                            ? '#52c41a' : '#ff4d4f',
                          marginRight: '6px'
                        }} />
                        {getStatusText(node.status, node.isHealthy)}
                      </div>
                    </div>
                  </div>
                </div>
                {/* 节点信息 */}
                <div style={{ marginBottom: '20px' }}>
                  <div style={{
                    background: 'rgba(102, 126, 234, 0.05)',
                    padding: '12px',
                    borderRadius: '12px',
                    border: '1px solid rgba(102, 126, 234, 0.1)'
                  }}>
                    <div style={{ fontSize: '12px', color: '#666', marginBottom: '4px' }}>节点ID</div>
                    <div style={{
                      fontSize: '14px',
                      fontWeight: '500',
                      color: '#333',
                      fontFamily: 'monospace'
                    }}>
                      {node.nodeId}
                    </div>
                  </div>
                  {node.description && (
                    <div style={{
                      marginTop: '12px',
                      padding: '8px 0',
                      fontSize: '14px',
                      color: '#666',
                      lineHeight: '1.4'
                    }}>
                      {node.description}
                    </div>
                  )}
                </div>

                {/* 调用统计 - 重新设计 */}
                <div style={{ marginBottom: '20px' }}>
                  <Row gutter={12}>
                    <Col span={12}>
                      <div style={{
                        textAlign: 'center',
                        padding: '16px 8px',
                        background: 'linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(64, 169, 255, 0.05))',
                        borderRadius: '16px',
                        border: '1px solid rgba(24, 144, 255, 0.2)',
                        position: 'relative',
                        overflow: 'hidden'
                      }}>
                        <div style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          height: '2px',
                          background: 'linear-gradient(90deg, #1890ff, #40a9ff)'
                        }} />
                        <div style={{
                          color: '#1890ff',
                          fontSize: '24px',
                          fontWeight: 'bold',
                          marginBottom: '4px'
                        }}>
                          {node.todayCalls || 0}
                        </div>
                        <div style={{ fontSize: '11px', color: '#666', fontWeight: '500' }}>
                          今日调用
                        </div>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div style={{
                        textAlign: 'center',
                        padding: '16px 8px',
                        background: 'linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(115, 209, 61, 0.05))',
                        borderRadius: '16px',
                        border: '1px solid rgba(82, 196, 26, 0.2)',
                        position: 'relative',
                        overflow: 'hidden'
                      }}>
                        <div style={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          height: '2px',
                          background: 'linear-gradient(90deg, #52c41a, #73d13d)'
                        }} />
                        <div style={{
                          color: '#52c41a',
                          fontSize: '24px',
                          fontWeight: 'bold',
                          marginBottom: '4px'
                        }}>
                          {node.totalCalls || 0}
                        </div>
                        <div style={{ fontSize: '11px', color: '#666', fontWeight: '500' }}>
                          总调用数
                        </div>
                      </div>
                    </Col>
                  </Row>

                  {/* 成功率显示 */}
                  <div style={{
                    textAlign: 'center',
                    marginTop: '12px',
                    padding: '16px',
                    background: (node.successRate || 0) >= 95
                      ? 'linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(115, 209, 61, 0.05))'
                      : (node.successRate || 0) >= 80
                      ? 'linear-gradient(135deg, rgba(250, 173, 20, 0.1), rgba(255, 197, 61, 0.05))'
                      : 'linear-gradient(135deg, rgba(255, 77, 79, 0.1), rgba(255, 120, 117, 0.05))',
                    borderRadius: '16px',
                    border: `1px solid ${(node.successRate || 0) >= 95 ? 'rgba(82, 196, 26, 0.2)' :
                                       (node.successRate || 0) >= 80 ? 'rgba(250, 173, 20, 0.2)' : 'rgba(255, 77, 79, 0.2)'}`,
                    position: 'relative',
                    overflow: 'hidden'
                  }}>
                    <div style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      height: '2px',
                      background: (node.successRate || 0) >= 95
                        ? 'linear-gradient(90deg, #52c41a, #73d13d)'
                        : (node.successRate || 0) >= 80
                        ? 'linear-gradient(90deg, #faad14, #ffc53d)'
                        : 'linear-gradient(90deg, #ff4d4f, #ff7875)'
                    }} />
                    <div style={{
                      color: (node.successRate || 0) >= 95 ? '#52c41a' :
                             (node.successRate || 0) >= 80 ? '#faad14' : '#ff4d4f',
                      fontSize: '28px',
                      fontWeight: 'bold',
                      marginBottom: '4px'
                    }}>
                      {node.successRate || 0}%
                    </div>
                    <div style={{ fontSize: '12px', color: '#666', fontWeight: '500' }}>
                      成功率 (24h)
                    </div>
                  </div>
                </div>

                {/* Token状态和配置信息 */}
                <div style={{ marginBottom: '16px' }}>
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    marginBottom: '12px'
                  }}>
                    <span style={{ fontSize: '12px', color: '#666', fontWeight: '500' }}>Token状态</span>
                    {getTokenModeTag(node.hasToken, node.tokenMode)}
                  </div>

                  <Row gutter={12}>
                    <Col span={12}>
                      <div style={{
                        textAlign: 'center',
                        padding: '12px 8px',
                        background: 'rgba(24, 144, 255, 0.05)',
                        borderRadius: '12px',
                        border: '1px solid rgba(24, 144, 255, 0.1)'
                      }}>
                        <ClockCircleOutlined style={{
                          color: '#1890ff',
                          fontSize: '16px',
                          marginBottom: '6px',
                          display: 'block'
                        }} />
                        <div style={{ fontSize: '11px', color: '#666', marginBottom: '4px' }}>
                          超时时间
                        </div>
                        <div style={{ fontSize: '14px', fontWeight: 'bold', color: '#1890ff' }}>
                          {node.timeout}ms
                        </div>
                      </div>
                    </Col>
                    <Col span={12}>
                      <div style={{
                        textAlign: 'center',
                        padding: '12px 8px',
                        background: 'rgba(82, 196, 26, 0.05)',
                        borderRadius: '12px',
                        border: '1px solid rgba(82, 196, 26, 0.1)'
                      }}>
                        <SafetyOutlined style={{
                          color: '#52c41a',
                          fontSize: '16px',
                          marginBottom: '6px',
                          display: 'block'
                        }} />
                        <div style={{ fontSize: '11px', color: '#666', marginBottom: '4px' }}>
                          请求限制
                        </div>
                        <div style={{ fontSize: '14px', fontWeight: 'bold', color: '#52c41a' }}>
                          {node.requestLimit === 0 ? '无限制' : `${node.requestLimit}/分钟`}
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>

                {/* 更新时间 */}
                {node.updatedAt && (
                  <div style={{
                    textAlign: 'center',
                    padding: '8px',
                    background: 'rgba(102, 126, 234, 0.05)',
                    borderRadius: '8px',
                    border: '1px solid rgba(102, 126, 234, 0.1)'
                  }}>
                    <div style={{ fontSize: '10px', color: '#999' }}>
                      最后更新: {new Date(node.updatedAt).toLocaleString('zh-CN')}
                    </div>
                  </div>
                )}
              </div>
            </Col>
          ))}
        </Row>

        {nodes.length === 0 && (
          <div style={{
            textAlign: 'center',
            padding: '80px 20px',
            background: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(20px)',
            borderRadius: '24px',
            boxShadow: '0 15px 35px rgba(0,0,0,0.1)',
            border: '1px solid rgba(255,255,255,0.3)'
          }}>
            <div style={{
              background: 'linear-gradient(135deg, #ff4d4f, #ff7875)',
              borderRadius: '50%',
              width: '80px',
              height: '80px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px',
              boxShadow: '0 10px 30px rgba(255, 77, 79, 0.3)'
            }}>
              <CloseCircleOutlined style={{ fontSize: '40px', color: 'white' }} />
            </div>
            <div style={{ fontSize: '18px', color: '#666', fontWeight: '500' }}>
              暂无节点数据
            </div>
            <div style={{ fontSize: '14px', color: '#999', marginTop: '8px' }}>
              请稍后刷新页面查看最新状态
            </div>
          </div>
        )}

        {/* 页面底部信息 */}
        <div style={{
          marginTop: 40,
          textAlign: 'center',
          padding: '32px 24px',
          background: 'rgba(255, 255, 255, 0.9)',
          backdropFilter: 'blur(20px)',
          borderRadius: '20px',
          boxShadow: '0 10px 30px rgba(0,0,0,0.1)',
          border: '1px solid rgba(102, 126, 234, 0.2)',
          position: 'relative',
          overflow: 'hidden'
        }}>
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '4px',
            background: 'linear-gradient(90deg, #667eea, #764ba2, #667eea)',
            backgroundSize: '200% 100%',
            animation: 'shimmer 3s ease-in-out infinite'
          }} />

          <div style={{
            fontSize: '16px',
            color: '#333',
            marginBottom: '16px',
            fontWeight: '500'
          }}>
            🌸 签名服务监控中心
          </div>

          <Space size="large" wrap>
            <div style={{
              background: 'rgba(102, 126, 234, 0.1)',
              padding: '8px 16px',
              borderRadius: '20px',
              border: '1px solid rgba(102, 126, 234, 0.2)',
              fontSize: '14px',
              color: '#667eea'
            }}>
              页面每30秒自动刷新
            </div>

            <a
              href="/docs"
              target="_blank"
              rel="noopener noreferrer"
              style={{
                color: '#667eea',
                textDecoration: 'none',
                padding: '8px 16px',
                borderRadius: '20px',
                border: '1px solid rgba(102, 126, 234, 0.2)',
                background: 'rgba(102, 126, 234, 0.05)',
                transition: 'all 0.3s ease',
                fontSize: '14px'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'rgba(102, 126, 234, 0.1)';
                e.target.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'rgba(102, 126, 234, 0.05)';
                e.target.style.transform = 'translateY(0)';
              }}
            >
              📚 API文档
            </a>

            <a
              href="/health"
              target="_blank"
              rel="noopener noreferrer"
              style={{
                color: '#667eea',
                textDecoration: 'none',
                padding: '8px 16px',
                borderRadius: '20px',
                border: '1px solid rgba(102, 126, 234, 0.2)',
                background: 'rgba(102, 126, 234, 0.05)',
                transition: 'all 0.3s ease',
                fontSize: '14px'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'rgba(102, 126, 234, 0.1)';
                e.target.style.transform = 'translateY(-2px)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'rgba(102, 126, 234, 0.05)';
                e.target.style.transform = 'translateY(0)';
              }}
            >
              💚 健康检查
            </a>
          </Space>

          <div style={{
            marginTop: '16px',
            fontSize: '12px',
            color: '#999'
          }}>
            Powered by QSignHook · 现代化签名服务管理平台
          </div>
        </div>
      </div>
    </div>
  );
};

export default PublicNodeStatus;
