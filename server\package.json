{"name": "server", "version": "1.0.0", "description": "签名服务器后端", "main": "src/app.js", "type": "commonjs", "private": true, "scripts": {"start": "node src/app.js", "dev": "nodemon --ignore 'database/*' src/app.js", "config:init": "node src/init-config.js", "db:init": "node src/scripts/mysql-database-init.js", "db:reset": "node src/scripts/mysql-database-init.js --reset", "db:check": "node src/scripts/mysql-database-init.js --check"}, "dependencies": {"node-fetch": "^2.6.12", "mysql2": "^3.14.1", "bcryptjs": "^2.4.3", "colors": "^1.4.0", "node-cron": "^4.2.0", "express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2"}}