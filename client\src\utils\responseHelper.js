import { useState, useEffect, useCallback } from 'react';

/**
 * API响应格式统一处理工具
 * 处理系统中的两种响应格式：
 * 1. 旧格式：{code: 0, msg: "success", data: ...}
 * 2. 新格式：{success: true, data: ...}
 */

/**
 * 统一处理API响应格式
 * @param {Object} response - axios响应对象
 * @returns {Object} 统一的响应格式
 */
export function normalizeResponse(response) {
  if (!response || !response.data) {
    throw new Error('响应数据为空');
  }

  const data = response.data;
  
  // 检测响应格式并统一处理
  if (data.success !== undefined) {
    // 新格式：{success: true/false, data: ..., message?: string}
    return {
      success: data.success,
      data: data.data,
      message: data.message || (data.success ? '操作成功' : '操作失败'),
      code: data.success ? 0 : (data.code || 1),
      originalResponse: response
    };
  } else if (data.code !== undefined) {
    // 旧格式：{code: 0/1, msg: string, data: ...}
    return {
      success: data.code === 0,
      data: data.data,
      message: data.msg || (data.code === 0 ? '操作成功' : '操作失败'),
      code: data.code,
      originalResponse: response
    };
  } else {
    // 无法识别的格式，尝试直接使用数据
    console.warn('⚠️ 无法识别的API响应格式:', data);
    return {
      success: true,
      data: data,
      message: '数据获取成功',
      code: 0,
      originalResponse: response
    };
  }
}

/**
 * 检查响应是否成功
 * @param {Object} response - axios响应对象或标准化后的响应
 * @returns {boolean} 是否成功
 */
export function isResponseSuccess(response) {
  try {
    const normalized = response.success !== undefined ? 
      response : normalizeResponse(response);
    return normalized.success;
  } catch (error) {
    console.error('检查响应状态失败:', error);
    return false;
  }
}

/**
 * 获取响应数据
 * @param {Object} response - axios响应对象或标准化后的响应
 * @returns {*} 响应数据
 */
export function getResponseData(response) {
  try {
    const normalized = response.success !== undefined ? 
      response : normalizeResponse(response);
    return normalized.data;
  } catch (error) {
    console.error('获取响应数据失败:', error);
    return null;
  }
}

/**
 * 获取响应消息
 * @param {Object} response - axios响应对象或标准化后的响应
 * @returns {string} 响应消息
 */
export function getResponseMessage(response) {
  try {
    const normalized = response.success !== undefined ? 
      response : normalizeResponse(response);
    return normalized.message;
  } catch (error) {
    console.error('获取响应消息失败:', error);
    return '未知错误';
  }
}

/**
 * 获取响应状态码
 * @param {Object} response - axios响应对象或标准化后的响应
 * @returns {number} 状态码
 */
export function getResponseCode(response) {
  try {
    const normalized = response.success !== undefined ? 
      response : normalizeResponse(response);
    return normalized.code;
  } catch (error) {
    console.error('获取响应状态码失败:', error);
    return -1;
  }
}

/**
 * API调用包装器，自动处理响应格式
 * @param {Function} apiCall - API调用函数
 * @returns {Promise<Object>} 标准化的响应
 */
export async function callApi(apiCall) {
  try {
    const response = await apiCall();
    return normalizeResponse(response);
  } catch (error) {
    console.error('API调用失败:', error);
    
    // 处理网络错误或HTTP错误
    if (error.response) {
      // 服务器返回了错误状态码
      const errorData = error.response.data;
      return {
        success: false,
        data: null,
        message: errorData?.msg || errorData?.message || `服务器错误 (${error.response.status})`,
        code: errorData?.code || error.response.status,
        originalResponse: error.response
      };
    } else if (error.request) {
      // 请求发出但没有收到响应
      return {
        success: false,
        data: null,
        message: '网络连接失败，请检查服务器状态',
        code: -1,
        originalResponse: null
      };
    } else {
      // 其他错误
      return {
        success: false,
        data: null,
        message: error.message || '请求配置错误',
        code: -1,
        originalResponse: null
      };
    }
  }
}

/**
 * 创建API响应处理钩子
 * @param {Function} apiCall - API调用函数
 * @param {Object} options - 选项
 * @returns {Array} [response, loading, error, refresh]
 */
export function useApiCall(apiCall, options = {}) {
  const [response, setResponse] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const refresh = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await callApi(apiCall);
      setResponse(result);
      
      if (!result.success && options.throwOnError) {
        setError(new Error(result.message));
      }
    } catch (err) {
      setError(err);
      setResponse({
        success: false,
        data: null,
        message: err.message,
        code: -1
      });
    } finally {
      setLoading(false);
    }
  }, [apiCall, options.throwOnError]);

  useEffect(() => {
    if (options.immediate !== false) {
      refresh();
    }
  }, [refresh, options.immediate]);

  return [response, loading, error, refresh];
}

export default {
  normalizeResponse,
  isResponseSuccess,
  getResponseData,
  getResponseMessage,
  getResponseCode,
  callApi,
  useApiCall
};
