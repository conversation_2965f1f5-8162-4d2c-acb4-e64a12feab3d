// JWT Token 管理工具
import { message, notification } from 'antd';
import { authAPI } from '../services/api';

class AuthManager {
  constructor() {
    this.token = localStorage.getItem('auth_token') || null;
    this.refreshTimer = null;
    this.isRefreshing = false;
    
    // 启动自动刷新检查
    this.startTokenRefreshCheck();
  }

  // 设置Token
  setToken(token) {
    this.token = token;
    localStorage.setItem('auth_token', token);
    
    // 重新启动自动刷新
    this.startTokenRefreshCheck();
  }

  // 获取Token
  getToken() {
    return this.token || localStorage.getItem('auth_token');
  }

  // 清除Token
  clearToken() {
    this.token = null;
    localStorage.removeItem('auth_token');
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  // 解析JWT Token
  parseJWT(token) {
    try {
      if (!token) return null;
      
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('解析JWT失败:', error);
      return null;
    }
  }

  // 检查Token是否过期
  isTokenExpired(token = null) {
    const currentToken = token || this.getToken();
    if (!currentToken) return true;

    const payload = this.parseJWT(currentToken);
    if (!payload || !payload.exp) return true;

    // 提前5分钟判断为即将过期
    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();
    const fiveMinutes = 5 * 60 * 1000;

    return currentTime >= (expirationTime - fiveMinutes);
  }

  // 获取Token剩余时间
  getTokenRemainingTime(token = null) {
    const currentToken = token || this.getToken();
    if (!currentToken) return 0;

    const payload = this.parseJWT(currentToken);
    if (!payload || !payload.exp) return 0;

    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();
    
    return Math.max(0, expirationTime - currentTime);
  }

  // 格式化剩余时间
  formatRemainingTime(ms) {
    if (ms <= 0) return '已过期';
    
    const minutes = Math.floor(ms / (60 * 1000));
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}小时${minutes % 60}分钟`;
    } else {
      return `${minutes}分钟`;
    }
  }

  // 刷新Token
  async refreshToken() {
    if (this.isRefreshing) {
      console.log('Token刷新中，跳过重复请求');
      return false;
    }

    this.isRefreshing = true;
    
    try {
      console.log('🔄 开始刷新JWT Token...');
      
      // 调用刷新API
      const response = await authAPI.refreshToken();
      
      if (response.code === 0 && response.data.token) {
        const newToken = response.data.token;
        this.setToken(newToken);
        
        const remainingTime = this.getTokenRemainingTime(newToken);
        
        notification.success({
          message: 'Token刷新成功',
          description: `新Token有效期: ${this.formatRemainingTime(remainingTime)}`,
          duration: 3,
          placement: 'topRight'
        });
        
        console.log('✅ JWT Token刷新成功');
        return true;
      } else {
        throw new Error(response.msg || '刷新Token失败');
      }
    } catch (error) {
      console.error('❌ JWT Token刷新失败:', error);
      
      notification.error({
        message: 'Token刷新失败',
        description: '请重新登录',
        duration: 5,
        placement: 'topRight'
      });
      
      // 刷新失败，清除Token并跳转到登录页
      this.clearToken();
      setTimeout(() => {
        window.location.href = '/login';
      }, 2000);
      
      return false;
    } finally {
      this.isRefreshing = false;
    }
  }

  // 启动Token自动刷新检查
  startTokenRefreshCheck() {
    // 清除现有定时器
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
    }

    const token = this.getToken();
    if (!token) return;

    const remainingTime = this.getTokenRemainingTime(token);
    
    if (remainingTime <= 0) {
      // Token已过期，立即刷新
      this.refreshToken();
    } else {
      // 计算下次检查时间：剩余时间的一半，但不超过30分钟，不少于1分钟
      const checkInterval = Math.min(
        Math.max(remainingTime / 2, 60 * 1000), // 最少1分钟
        30 * 60 * 1000 // 最多30分钟
      );
      
      console.log(`⏰ 下次Token检查时间: ${this.formatRemainingTime(checkInterval)}后`);
      
      this.refreshTimer = setTimeout(() => {
        this.checkAndRefreshToken();
      }, checkInterval);
    }
  }

  // 检查并刷新Token
  async checkAndRefreshToken() {
    const token = this.getToken();
    if (!token) return;

    if (this.isTokenExpired(token)) {
      console.log('🔔 Token即将过期，开始自动刷新...');
      await this.refreshToken();
    } else {
      // Token还有效，继续定时检查
      this.startTokenRefreshCheck();
    }
  }

  // 显示Token状态
  showTokenStatus() {
    const token = this.getToken();
    if (!token) {
      message.warning('未找到Token');
      return;
    }

    const payload = this.parseJWT(token);
    const remainingTime = this.getTokenRemainingTime(token);
    
    notification.info({
      message: 'Token状态信息',
      description: (
        <div>
          <div>用户: {payload?.username || '未知'}</div>
          <div>角色: {payload?.role || '未知'}</div>
          <div>剩余时间: {this.formatRemainingTime(remainingTime)}</div>
          <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
            过期时间: {new Date(payload?.exp * 1000).toLocaleString()}
          </div>
        </div>
      ),
      duration: 6,
      placement: 'topRight'
    });
  }
}

// 创建全局实例
const authManager = new AuthManager();

export default authManager;
