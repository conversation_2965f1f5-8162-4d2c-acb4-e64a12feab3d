-- MySQL数据库初始化脚本
-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS qsign_hook CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE qsign_hook;

-- 签名日志表（主要表）
CREATE TABLE IF NOT EXISTS sign_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    ip VARCHAR(45) NOT NULL COMMENT 'IP地址',
    uin VARCHAR(20) COMMENT 'QQ号',
    cmd VARCHAR(50) COMMENT '命令类型',
    type VARCHAR(50) NOT NULL COMMENT '客户端类型',
    endpoint VARCHAR(100) NOT NULL COMMENT '请求端点',
    success TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否成功',
    error_msg TEXT COMMENT '错误信息',
    request_data LONGTEXT COMMENT '请求数据',
    response_data LONGTEXT COMMENT '响应数据',
    request_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
    response_time_ms INT DEFAULT 0 COMMENT '响应时间(毫秒)',
    user_agent TEXT COMMENT '用户代理',
    INDEX idx_request_time (request_time),
    INDEX idx_ip (ip),
    INDEX idx_uin (uin),
    INDEX idx_type (type),
    INDEX idx_success (success),
    INDEX idx_endpoint (endpoint)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='签名请求日志表';

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码哈希',
    email VARCHAR(100) COMMENT '邮箱',
    role ENUM('admin', 'user') DEFAULT 'user' COMMENT '角色',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '状态',
    last_login TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    totp_secret VARCHAR(32) COMMENT 'TOTP密钥',
    INDEX idx_username (username),
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 系统设置表
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    key_name VARCHAR(100) UNIQUE NOT NULL COMMENT '设置键名',
    key_value TEXT COMMENT '设置值',
    description TEXT COMMENT '描述',
    type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string' COMMENT '值类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_key_name (key_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统设置表';

-- API配置表
CREATE TABLE IF NOT EXISTS api_configs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_type VARCHAR(50) NOT NULL COMMENT '客户端类型',
    base_url VARCHAR(255) NOT NULL COMMENT '基础URL',
    timeout INT DEFAULT 10000 COMMENT '超时时间(毫秒)',
    retry_count INT DEFAULT 3 COMMENT '重试次数',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '状态',
    description TEXT COMMENT '描述',
    auth_key VARCHAR(255) COMMENT '认证密钥',
    token_mode ENUM('exclusive', 'shared') DEFAULT 'exclusive' COMMENT 'Token模式：exclusive=独享，shared=共享',
    request_limit INT DEFAULT 0 COMMENT '请求限制',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_client_type (client_type),
    INDEX idx_status (status),
    INDEX idx_auth_key (auth_key),
    INDEX idx_token_mode (token_mode)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API配置表';

-- 黑名单表
CREATE TABLE IF NOT EXISTS blacklist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) COMMENT '用户名',
    device_id VARCHAR(100) COMMENT '设备ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    uin VARCHAR(20) COMMENT 'QQ号',
    risk_content TEXT COMMENT '风险内容',
    ban_reason TEXT COMMENT '封禁原因',
    ban_type ENUM('permanent', 'temporary') DEFAULT 'permanent' COMMENT '封禁类型',
    ban_duration INT COMMENT '封禁时长(小时)',
    expire_time TIMESTAMP NULL COMMENT '过期时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    creator_id INT COMMENT '创建者ID',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_ip_address (ip_address),
    INDEX idx_uin (uin),
    INDEX idx_device_id (device_id),
    INDEX idx_is_active (is_active),
    INDEX idx_expire_time (expire_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='黑名单表';

-- 白名单表
CREATE TABLE IF NOT EXISTS whitelist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) COMMENT '用户名',
    device_id VARCHAR(100) COMMENT '设备ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    uin VARCHAR(20) COMMENT 'QQ号',
    whitelist_type ENUM('manual', 'auto') DEFAULT 'manual' COMMENT '白名单类型',
    reason TEXT COMMENT '原因',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    creator_id INT COMMENT '创建者ID',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_ip_address (ip_address),
    INDEX idx_uin (uin),
    INDEX idx_device_id (device_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='白名单表';

-- 风控关键词表
CREATE TABLE IF NOT EXISTS risk_keywords (
    id INT AUTO_INCREMENT PRIMARY KEY,
    keyword VARCHAR(255) NOT NULL COMMENT '关键词',
    keyword_type ENUM('strict', 'fuzzy', 'regex') DEFAULT 'strict' COMMENT '匹配类型',
    risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium' COMMENT '风险等级',
    description TEXT COMMENT '描述',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否激活',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_keyword (keyword),
    INDEX idx_is_active (is_active),
    INDEX idx_risk_level (risk_level)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='风控关键词表';

-- 风控记录表
CREATE TABLE IF NOT EXISTS risk_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) COMMENT '用户名',
    device_id VARCHAR(100) COMMENT '设备ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    uin VARCHAR(20) COMMENT 'QQ号',
    risk_content TEXT NOT NULL COMMENT '风险内容',
    matched_keywords JSON COMMENT '匹配的关键词',
    risk_score INT DEFAULT 0 COMMENT '风险分数',
    risk_level ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium' COMMENT '风险等级',
    status ENUM('pending', 'approved', 'rejected', 'auto_blocked') DEFAULT 'pending' COMMENT '状态',
    reviewer_id INT COMMENT '审核者ID',
    review_reason TEXT COMMENT '审核原因',
    review_time TIMESTAMP NULL COMMENT '审核时间',
    auto_blocked TINYINT(1) DEFAULT 0 COMMENT '是否自动拦截',
    client_info JSON COMMENT '客户端信息',
    request_data TEXT COMMENT '请求数据',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_ip_address (ip_address),
    INDEX idx_uin (uin),
    INDEX idx_risk_level (risk_level),
    INDEX idx_status (status),
    INDEX idx_created_time (created_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='风控记录表';

-- 未授权访问日志表
CREATE TABLE IF NOT EXISTS unauthorized_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    ip VARCHAR(45) NOT NULL COMMENT 'IP地址',
    endpoint VARCHAR(255) NOT NULL COMMENT '请求端点',
    method VARCHAR(10) NOT NULL COMMENT '请求方法',
    user_agent TEXT COMMENT '用户代理',
    auth_key VARCHAR(255) COMMENT '认证密钥',
    reason VARCHAR(255) COMMENT '拒绝原因',
    request_data TEXT COMMENT '请求数据',
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '尝试时间',
    INDEX idx_ip (ip),
    INDEX idx_attempt_time (attempt_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='未授权访问日志表';

-- 每日统计表
CREATE TABLE IF NOT EXISTS daily_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL COMMENT '日期',
    total_requests INT DEFAULT 0 COMMENT '总请求数',
    success_requests INT DEFAULT 0 COMMENT '成功请求数',
    failed_requests INT DEFAULT 0 COMMENT '失败请求数',
    unique_ips INT DEFAULT 0 COMMENT '独立IP数',
    unique_uins INT DEFAULT 0 COMMENT '独立UIN数',
    avg_response_time DECIMAL(10,2) DEFAULT 0.00 COMMENT '平均响应时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY idx_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='每日统计表';

-- 消息日志表
CREATE TABLE IF NOT EXISTS message_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    sign_log_id BIGINT COMMENT '关联的签名日志ID',
    uin VARCHAR(20) COMMENT 'QQ号',
    cmd VARCHAR(50) COMMENT '命令',
    message_type VARCHAR(50) DEFAULT 'unknown' COMMENT '消息类型',
    main_content TEXT COMMENT '主要内容',
    all_messages LONGTEXT COMMENT '所有消息',
    image_urls JSON COMMENT '图片URL列表',
    multimedia_content LONGTEXT COMMENT '多媒体内容',
    parse_success TINYINT(1) DEFAULT 0 COMMENT '解析是否成功',
    parse_error TEXT COMMENT '解析错误',
    buffer_length INT COMMENT '缓冲区长度',
    parse_time_ms INT COMMENT '解析时间(毫秒)',
    parsing_method VARCHAR(50) DEFAULT 'icqq_pb' COMMENT '解析方法',
    quality_score DECIMAL(5,2) DEFAULT 0.00 COMMENT '质量分数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_sign_log_id (sign_log_id),
    INDEX idx_uin (uin),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (sign_log_id) REFERENCES sign_logs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='消息日志表';

-- 插入默认管理员用户（密码：admin123）
INSERT IGNORE INTO users (username, password, role) VALUES
('admin', '$2a$10$YcbCphqhBzA5U.f2S0e9Qe0DYlmB8UmuQn8XbvHfQusirHiIR5kfO', 'admin');

-- 插入xiya管理员用户（密码：xiya50491）
INSERT IGNORE INTO users (username, password, role) VALUES
('xiya', '$2a$10$tPUZVNhaKVYRue92NoFZ9eIC37wYdIbUE4j/dQbLsgiuG/kZqLP7C', 'admin');

-- 插入默认系统设置
INSERT IGNORE INTO settings (key_name, key_value, description, type) VALUES
('auth_key', '1145141919810', '默认认证密钥', 'string'),
('auto_clean_logs', 'true', '自动清理日志', 'boolean'),
('clean_logs_days', '7', '日志保留天数', 'number'),
('enable_logging', 'true', '启用日志记录', 'boolean'),
('max_requests_per_minute', '60', '每分钟最大请求数', 'number'),
('enable_rate_limit', 'true', '启用速率限制', 'boolean');

-- 插入默认管理员用户（密码：admin123）
INSERT IGNORE INTO users (username, password, role) VALUES
('admin', '$2a$10$YcbCphqhBzA5U.f2S0e9Qe0DYlmB8UmuQn8XbvHfQusirHiIR5kfO', 'admin');

-- 插入xiya管理员用户（密码：xiya50491）
INSERT IGNORE INTO users (username, password, role) VALUES
('xiya', '$2a$10$tPUZVNhaKVYRue92NoFZ9eIC37wYdIbUE4j/dQbLsgiuG/kZqLP7C', 'admin');

-- 插入默认系统设置
INSERT IGNORE INTO settings (key_name, key_value, description, type) VALUES
('auth_key', '1145141919810', '默认认证密钥', 'string'),
('auto_clean_logs', 'true', '自动清理日志', 'boolean'),
('clean_logs_days', '7', '日志保留天数', 'number'),
('enable_logging', 'true', '启用日志记录', 'boolean'),
('max_requests_per_minute', '60', '每分钟最大请求数', 'number'),
('enable_rate_limit', 'true', '启用速率限制', 'boolean');

-- 黑名单记录表（风控系统使用）
CREATE TABLE IF NOT EXISTS blacklist_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) COMMENT '用户名',
    device_id VARCHAR(100) COMMENT '设备ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    uin VARCHAR(20) COMMENT 'QQ号',
    risk_content TEXT COMMENT '风险内容',
    ban_reason TEXT NOT NULL COMMENT '封禁原因',
    ban_type ENUM('permanent', 'temporary') DEFAULT 'permanent' COMMENT '封禁类型',
    ban_end_time TIMESTAMP NULL COMMENT '封禁结束时间',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否生效',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_ip_address (ip_address),
    INDEX idx_uin (uin),
    INDEX idx_device_id (device_id),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='黑名单记录表';

-- 白名单记录表（风控系统使用）
CREATE TABLE IF NOT EXISTS whitelist_records (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) COMMENT '用户名',
    device_id VARCHAR(100) COMMENT '设备ID',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    uin VARCHAR(20) COMMENT 'QQ号',
    whitelist_type ENUM('username', 'device', 'ip', 'uin', 'comprehensive') DEFAULT 'comprehensive' COMMENT '白名单类型',
    description TEXT COMMENT '描述',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否生效',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_ip_address (ip_address),
    INDEX idx_uin (uin),
    INDEX idx_device_id (device_id),
    INDEX idx_whitelist_type (whitelist_type),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='白名单记录表';

-- 插入一些示例API配置
INSERT IGNORE INTO api_configs (client_type, base_url, timeout, retry_count, description) VALUES
('android', 'http://localhost:8080', 10000, 3, 'Android客户端默认配置'),
('ipad', 'http://localhost:8080', 10000, 3, 'iPad客户端默认配置'),
('watch', 'http://localhost:8080', 10000, 3, 'Watch客户端默认配置'),
('macos', 'http://localhost:8080', 10000, 3, 'macOS客户端默认配置');

-- 插入一些示例风控关键词
INSERT IGNORE INTO risk_keywords (keyword, keyword_type, risk_level, description) VALUES
('测试关键词', 'strict', 'low', '测试用的低风险关键词'),
('敏感词汇', 'fuzzy', 'medium', '中等风险的敏感词汇'),
('高危内容', 'strict', 'high', '高风险内容关键词');

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_sign_logs_uin ON sign_logs(uin);
CREATE INDEX IF NOT EXISTS idx_sign_logs_ip ON sign_logs(ip);
CREATE INDEX IF NOT EXISTS idx_sign_logs_request_time ON sign_logs(request_time);
CREATE INDEX IF NOT EXISTS idx_sign_logs_success ON sign_logs(success);

-- 完成初始化
SELECT 'MySQL数据库初始化完成！' AS message;
