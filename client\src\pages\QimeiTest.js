import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Input,
  Form,
  Typography,
  Space,
  Row,
  Col,
  Spin,
  Alert,
  Collapse,
  Tag,
  Table
} from 'antd';
import {
  PlayCircleOutlined,
  ReloadOutlined,
  SettingOutlined,
  ExperimentOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { qimeiAPI } from '../services/api';
import NotificationHelper from '../components/NotificationHelper';
import { normalizeResponse, isResponseSuccess, getResponseData, getResponseMessage } from '../utils/responseHelper';

const { Title, Paragraph, Text } = Typography;
const { Panel } = Collapse;

const QimeiTest = () => {
  const [loading, setLoading] = useState(false);
  const [template, setTemplate] = useState(null);
  const [results, setResults] = useState([]);
  const [customParams, setCustomParams] = useState({});
  const [form] = Form.useForm();
  // 获取模板数据
  const fetchTemplate = async () => {
    try {
      setLoading(true);
      const response = await qimeiAPI.getTemplate();
      const normalizedResponse = normalizeResponse(response);
      
      if (isResponseSuccess(normalizedResponse)) {
        const templateData = getResponseData(normalizedResponse);
        setTemplate(templateData);
        setCustomParams(templateData);
        form.setFieldsValue(templateData);
        NotificationHelper.operationSuccess('create', 'QIMEI模板加载成功');
      } else {
        console.error('❌ QimeiTest - 获取模板失败:', normalizedResponse);
        NotificationHelper.error('加载失败', getResponseMessage(normalizedResponse));
      }
    } catch (error) {
      console.error('❌ QimeiTest - 获取模板失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '获取QIMEI模板失败');
    } finally {
      setLoading(false);
    }
  };
  // 生成单个QIMEI
  const generateSingle = async () => {
    try {
      setLoading(true);
      const values = form.getFieldsValue();
      const response = await qimeiAPI.generate({ params: values });
      const normalizedResponse = normalizeResponse(response);
      
      if (isResponseSuccess(normalizedResponse)) {
        const responseData = getResponseData(normalizedResponse);
        const newResult = {
          id: Date.now(),
          type: '单个生成',
          timestamp: new Date().toLocaleString(),
          qimei: responseData.qimei,
          qimei36: responseData.qimei36,
          raw: responseData.raw,
          params: values
        };
        setResults(prev => [newResult, ...prev]);
        NotificationHelper.operationSuccess('create', 'QIMEI生成成功');
      } else {
        console.error('❌ QimeiTest - 生成失败:', normalizedResponse);
        NotificationHelper.error('生成失败', getResponseMessage(normalizedResponse));
      }
    } catch (error) {
      console.error('❌ QimeiTest - 生成失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '生成QIMEI失败');
    } finally {
      setLoading(false);
    }
  };
  // 批量生成QIMEI
  const generateBatch = async (count) => {
    try {
      setLoading(true);
      const values = form.getFieldsValue();
      const response = await qimeiAPI.batch({ count, params: values });
      const normalizedResponse = normalizeResponse(response);
      
      if (isResponseSuccess(normalizedResponse)) {
        const responseData = getResponseData(normalizedResponse);
        const newResults = responseData.map((item, index) => ({
          id: Date.now() + index,
          type: `批量生成 ${index + 1}/${count}`,
          timestamp: new Date().toLocaleString(),
          qimei: item.qimei || '生成失败',
          qimei36: item.qimei36 || '生成失败',
          raw: item.raw || null,
          error: item.error || null,
          params: values
        }));
        setResults(prev => [...newResults, ...prev]);
        NotificationHelper.operationSuccess('create', `批量生成完成，共 ${count} 个QIMEI`);
      } else {
        console.error('❌ QimeiTest - 批量生成失败:', normalizedResponse);
        NotificationHelper.error('批量生成失败', getResponseMessage(normalizedResponse));
      }
    } catch (error) {
      console.error('❌ QimeiTest - 批量生成失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '批量生成QIMEI失败');
    } finally {
      setLoading(false);
    }  };

  // 重置表单
  const resetForm = () => {
    if (template) {
      form.setFieldsValue(template);
      setCustomParams(template);
      NotificationHelper.info('参数重置', '参数已重置为默认值');
    }
  };

  // 清空结果
  const clearResults = () => {
    setResults([]);
    NotificationHelper.info('结果清空', '生成结果已清空');
  };

  useEffect(() => {
    fetchTemplate();
  }, []);

  // 结果表格列定义
  const resultColumns = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (text) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 150
    },
    {
      title: 'QIMEI',
      dataIndex: 'qimei',
      key: 'qimei',
      render: (text) => (
        <Space>
          <Text code copyable={{ text }}>{text}</Text>
        </Space>
      )
    },
    {
      title: 'QIMEI36',
      dataIndex: 'qimei36',
      key: 'qimei36',
      render: (text) => (
        <Space>
          <Text code copyable={{ text }}>{text}</Text>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'error',
      key: 'status',
      width: 80,
      render: (error) => (
        <Tag color={error ? 'red' : 'green'}>
          {error ? '失败' : '成功'}
        </Tag>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[24, 24]}>
        {/* 左侧控制面板 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <ExperimentOutlined />
                QIMEI生成测试
              </Space>
            }
            extra={
              <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchTemplate}
                loading={loading}
              >
                重新加载模板
              </Button>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              {/* 快速操作 */}
              <Card size="small" title="快速操作">
                <Row gutter={[8, 8]}>
                  <Col span={12}>
                    <Button 
                      type="primary" 
                      block 
                      icon={<PlayCircleOutlined />}
                      onClick={generateSingle}
                      loading={loading}
                    >
                      生成单个
                    </Button>
                  </Col>
                  <Col span={12}>
                    <Button 
                      block 
                      icon={<PlayCircleOutlined />}
                      onClick={() => generateBatch(3)}
                      loading={loading}
                    >
                      批量生成(3个)
                    </Button>
                  </Col>
                  <Col span={12}>
                    <Button 
                      block 
                      onClick={() => generateBatch(5)}
                      loading={loading}
                    >
                      批量生成(5个)
                    </Button>
                  </Col>
                  <Col span={12}>
                    <Button 
                      block 
                      onClick={resetForm}
                    >
                      重置参数
                    </Button>
                  </Col>
                </Row>
              </Card>

              {/* 参数配置 */}
              <Collapse defaultActiveKey={['1']}>
                <Panel 
                  header={
                    <Space>
                      <SettingOutlined />
                      参数配置
                    </Space>
                  } 
                  key="1"
                >
                  {template && (
                    <Form
                      form={form}
                      layout="vertical"
                      initialValues={template}
                      onValuesChange={(changedValues, allValues) => {
                        setCustomParams(allValues);
                      }}
                    >                      <Row gutter={[16, 0]}>
                        <Col span={12}>
                          <Form.Item label="设备品牌" name="brand">
                            <Input placeholder="例如: Xiaomi, OPPO, Vivo" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label="设备型号" name="model">
                            <Input placeholder="例如: 24071PN0DG, CPH2399" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label="应用版本" name="appVersion">
                            <Input placeholder="例如: 9.1.15.4500" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item label="系统版本" name="osVersion">
                            <Input placeholder="例如: Android 14,level 34" />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Form>
                  )}
                </Panel>
              </Collapse>              {/* 使用说明 */}
              <Alert
                message="使用说明"
                description={
                  <ul style={{ marginBottom: 0, paddingLeft: 16 }}>
                    <li>QIMEI是QQ设备标识符，用于设备识别</li>
                    <li>默认使用2025年最新设备信息(小米14 Pro)</li>
                    <li>支持单个生成和批量生成(最多10个)</li>
                    <li>生成结果包含16位和36位两种格式</li>
                  </ul>
                }
                type="info"
                showIcon
              />
            </Space>
          </Card>
        </Col>

        {/* 右侧结果展示 */}
        <Col xs={24} lg={12}>
          <Card 
            title={
              <Space>
                <InfoCircleOutlined />
                生成结果 ({results.length})
              </Space>
            }
            extra={
              <Button 
                onClick={clearResults}
                disabled={results.length === 0}
              >
                清空结果
              </Button>
            }
          >
            {loading && (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Spin size="large" />
                <div style={{ marginTop: 16 }}>正在生成QIMEI...</div>
              </div>
            )}

            {!loading && results.length === 0 && (
              <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
                暂无生成结果
                <br />
                点击上方按钮开始生成QIMEI
              </div>
            )}

            {!loading && results.length > 0 && (
              <div style={{ maxHeight: '600px', overflowY: 'auto' }}>
                <Table
                  dataSource={results}
                  columns={resultColumns}
                  pagination={{ pageSize: 10 }}
                  size="small"
                  rowKey="id"
                  expandable={{
                    expandedRowRender: (record) => (
                      <div>
                        {record.error && (
                          <Alert 
                            message="生成失败" 
                            description={record.error} 
                            type="error" 
                            style={{ marginBottom: 16 }}
                          />
                        )}
                        {record.raw && (
                          <>
                            <Title level={5}>完整数据:</Title>
                            <pre style={{ 
                              background: '#f5f5f5', 
                              padding: '12px', 
                              borderRadius: '4px',
                              maxHeight: '200px',
                              overflow: 'auto',
                              fontSize: '12px'
                            }}>
                              {JSON.stringify(record.raw, null, 2)}
                            </pre>
                          </>
                        )}
                      </div>
                    ),
                  }}
                />
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default QimeiTest;