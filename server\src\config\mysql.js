const mysql = require('mysql2/promise');
const colors = require('colors');
const fs = require('fs');
const path = require('path');

/**
 * MySQL数据库连接配置
 */
class MySQLConnection {
    constructor() {
        this.pool = null;
        this.config = null;
        this.loadConfig();
    }

    /**
     * 从配置文件加载MySQL配置
     */
    loadConfig() {
        try {
            // 读取配置文件
            const configPath = path.join(__dirname, '../../database/config.json');
            const configData = JSON.parse(fs.readFileSync(configPath, 'utf8'));

            // 获取MySQL配置，优先使用环境变量
            const mysqlConfig = configData.mysql || {};

            this.config = {
                host: process.env.MYSQL_HOST || mysqlConfig.host || 'localhost',
                port: parseInt(process.env.MYSQL_PORT || mysqlConfig.port || 3306),
                user: process.env.MYSQL_USER || mysqlConfig.user || 'root',
                password: process.env.MYSQL_PASSWORD || mysqlConfig.password || '',
                database: process.env.MYSQL_DATABASE || mysqlConfig.database || 'qsign_hook',
                charset: mysqlConfig.charset || 'utf8mb4',
                connectionLimit: mysqlConfig.connectionLimit || 10,
                acquireTimeout: mysqlConfig.acquireTimeout || 60000,
                multipleStatements: true,
                waitForConnections: true,
                queueLimit: 0
            };

            console.log('📋 MySQL配置加载成功'.green);

        } catch (error) {
            console.error('❌ 加载MySQL配置失败:'.red, error.message);

            // 使用默认配置
            this.config = {
                host: process.env.MYSQL_HOST || 'localhost',
                port: parseInt(process.env.MYSQL_PORT || 3306),
                user: process.env.MYSQL_USER || 'root',
                password: process.env.MYSQL_PASSWORD || '',
                database: process.env.MYSQL_DATABASE || 'qsign_hook',
                charset: 'utf8mb4',
                connectionLimit: 10,
                acquireTimeout: 60000,
                multipleStatements: true,
                waitForConnections: true,
                queueLimit: 0
            };

            console.log('⚠️  使用默认MySQL配置'.yellow);
        }
    }

    /**
     * 初始化连接池
     */
    async init() {
        try {
            console.log('🔗 正在连接MySQL数据库...'.cyan);
            console.log(`📋 连接配置: ${this.config.user}@${this.config.host}:${this.config.port}/${this.config.database}`.gray);
            console.log(`🔐 密码状态: ${this.config.password ? '已设置' : '未设置'}`.gray);

            // 创建连接池
            this.pool = mysql.createPool(this.config);

            // 测试连接
            const connection = await this.pool.getConnection();
            console.log(`✅ MySQL数据库连接成功: ${this.config.host}:${this.config.port}/${this.config.database}`.green);
            connection.release();

            return true;
        } catch (error) {
            console.error('❌ MySQL数据库连接失败:'.red);
            console.error('错误详情:'.red, error.message);
            console.error('错误代码:'.red, error.code || 'UNKNOWN');
            console.error('连接配置:'.red, {
                host: this.config.host,
                port: this.config.port,
                user: this.config.user,
                database: this.config.database,
                passwordSet: !!this.config.password
            });

            // 提供常见错误的解决建议
            if (error.code === 'ECONNREFUSED') {
                console.error('💡 建议: MySQL服务可能未启动，请检查MySQL服务状态'.yellow);
                console.error('   检查命令: telnet ' + this.config.host + ' ' + this.config.port);
            } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
                console.error('💡 建议: 用户名或密码错误，请检查MySQL凭据'.yellow);
                console.error('   测试命令: mysql -h ' + this.config.host + ' -P ' + this.config.port + ' -u ' + this.config.user + ' -p');
            } else if (error.code === 'ER_BAD_DB_ERROR') {
                console.error('💡 建议: 数据库不存在，请先创建数据库'.yellow);
                console.error('   创建命令: CREATE DATABASE ' + this.config.database + ' CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;');
            } else if (error.code === 'ENOTFOUND') {
                console.error('💡 建议: 主机名无法解析，请检查网络连接和主机名'.yellow);
            } else if (error.code === 'ETIMEDOUT') {
                console.error('💡 建议: 连接超时，请检查网络连接和防火墙设置'.yellow);
            }

            throw error;
        }
    }

    /**
     * 执行查询
     */
    async query(sql, params = []) {
        try {
            if (!this.pool) {
                await this.init();
            }
            
            const [rows] = await this.pool.execute(sql, params);
            return rows;
        } catch (error) {
            console.error('数据库查询错误:'.red, error.message);
            console.error('SQL:'.yellow, sql);
            console.error('参数:'.yellow, params);
            throw error;
        }
    }

    /**
     * 执行更新/插入/删除操作
     */
    async run(sql, params = []) {
        try {
            if (!this.pool) {
                await this.init();
            }
            
            const [result] = await this.pool.execute(sql, params);
            return {
                insertId: result.insertId,
                affectedRows: result.affectedRows,
                changedRows: result.changedRows
            };
        } catch (error) {
            console.error('数据库执行错误:'.red, error.message);
            console.error('SQL:'.yellow, sql);
            console.error('参数:'.yellow, params);
            throw error;
        }
    }

    /**
     * 开始事务
     */
    async beginTransaction() {
        if (!this.pool) {
            await this.init();
        }
        
        const connection = await this.pool.getConnection();
        await connection.beginTransaction();
        return connection;
    }

    /**
     * 提交事务
     */
    async commit(connection) {
        await connection.commit();
        connection.release();
    }

    /**
     * 回滚事务
     */
    async rollback(connection) {
        await connection.rollback();
        connection.release();
    }

    /**
     * 关闭连接池
     */
    async close() {
        if (this.pool) {
            await this.pool.end();
            console.log('🔌 MySQL连接池已关闭'.gray);
        }
    }

    /**
     * 获取连接池状态
     */
    getStatus() {
        if (!this.pool) {
            return { connected: false };
        }
        
        return {
            connected: true,
            totalConnections: this.pool.pool._allConnections.length,
            freeConnections: this.pool.pool._freeConnections.length,
            acquiringConnections: this.pool.pool._acquiringConnections.length
        };
    }
}

// 创建单例实例
const mysqlConnection = new MySQLConnection();

module.exports = mysqlConnection;
