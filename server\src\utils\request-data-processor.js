/**
 * 请求数据处理器
 * 用于处理和格式化请求数据，确保敏感信息安全
 */
class RequestDataProcessor {
    
    /**
     * 敏感字段列表
     */
    static SENSITIVE_FIELDS = [
        'password', 'passwd', 'pwd', 'secret', 'token', 'key', 'auth',
        'authorization', 'credential', 'private', 'confidential',
        'sign', 'signature', 'hash', 'encrypt', 'decrypt'
    ];
    
    /**
     * 最大字段长度
     */
    static MAX_FIELD_LENGTH = 10000;
    
    /**
     * 最大请求体大小（字节）
     */
    static MAX_REQUEST_SIZE = 100 * 1024; // 100KB
    
    /**
     * 处理请求数据
     * @param {Object} req - Express请求对象
     * @returns {Object} 处理后的请求数据
     */
    static processRequestData(req) {
        const startTime = Date.now();
        
        try {
            const requestData = {
                // 基础信息
                method: req.method || 'GET',
                url: req.originalUrl || req.url || '/',
                protocol: req.protocol || 'http',
                
                // 请求头（过滤敏感信息）
                headers: this.sanitizeHeaders(req.headers || {}),
                
                // 查询参数
                query: this.sanitizeObject(req.query || {}),
                
                // 请求体
                body: this.sanitizeRequestBody(req.body),
                
                // 文件上传信息
                files: this.processFiles(req.files),
                
                // 请求统计
                stats: {
                    content_length: parseInt(req.get('content-length') || '0'),
                    param_count: Object.keys(req.query || {}).length,
                    body_size: this.calculateBodySize(req.body),
                    processing_time: Date.now() - startTime
                },
                
                // 请求特征
                features: this.extractRequestFeatures(req),
                
                // 时间戳
                timestamp: new Date().toISOString(),
                processed_at: Date.now()
            };
            
            // 检查请求大小
            const totalSize = JSON.stringify(requestData).length;
            if (totalSize > this.MAX_REQUEST_SIZE) {
                requestData.body = this.truncateData(requestData.body, 'Request body too large');
                requestData.stats.truncated = true;
                requestData.stats.original_size = totalSize;
            }
            
            return requestData;
            
        } catch (error) {
            console.error('处理请求数据失败:', error);
            return {
                error: 'Failed to process request data',
                error_message: error.message,
                timestamp: new Date().toISOString()
            };
        }
    }
    
    /**
     * 清理请求头，移除敏感信息
     * @param {Object} headers - 原始请求头
     * @returns {Object} 清理后的请求头
     */
    static sanitizeHeaders(headers) {
        const sanitized = {};
        const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];
        
        Object.keys(headers).forEach(key => {
            const lowerKey = key.toLowerCase();
            if (sensitiveHeaders.includes(lowerKey)) {
                sanitized[key] = this.maskSensitiveValue(headers[key]);
            } else {
                sanitized[key] = this.truncateString(headers[key], 500);
            }
        });
        
        return sanitized;
    }
    
    /**
     * 清理请求体
     * @param {*} body - 原始请求体
     * @returns {*} 清理后的请求体
     */
    static sanitizeRequestBody(body) {
        if (!body) return null;
        
        try {
            if (typeof body === 'string') {
                return this.sanitizeString(body);
            }
            
            if (typeof body === 'object') {
                return this.sanitizeObject(body);
            }
            
            return body;
        } catch (error) {
            return { error: 'Failed to sanitize body', original_type: typeof body };
        }
    }
    
    /**
     * 清理对象，处理敏感字段
     * @param {Object} obj - 原始对象
     * @returns {Object} 清理后的对象
     */
    static sanitizeObject(obj) {
        if (!obj || typeof obj !== 'object') return obj;
        
        const sanitized = {};
        
        Object.keys(obj).forEach(key => {
            const value = obj[key];
            const lowerKey = key.toLowerCase();
            
            // 检查是否为敏感字段
            const isSensitive = this.SENSITIVE_FIELDS.some(field => 
                lowerKey.includes(field)
            );
            
            if (isSensitive) {
                sanitized[key] = this.maskSensitiveValue(value);
            } else if (typeof value === 'object') {
                sanitized[key] = this.sanitizeObject(value);
            } else if (typeof value === 'string') {
                sanitized[key] = this.truncateString(value, this.MAX_FIELD_LENGTH);
            } else {
                sanitized[key] = value;
            }
        });
        
        return sanitized;
    }
    
    /**
     * 清理字符串
     * @param {string} str - 原始字符串
     * @returns {string} 清理后的字符串
     */
    static sanitizeString(str) {
        if (typeof str !== 'string') return str;
        
        // 检查是否包含敏感模式
        const sensitivePatterns = [
            /password\s*[:=]\s*\S+/gi,
            /token\s*[:=]\s*\S+/gi,
            /key\s*[:=]\s*\S+/gi,
            /secret\s*[:=]\s*\S+/gi
        ];
        
        let sanitized = str;
        sensitivePatterns.forEach(pattern => {
            sanitized = sanitized.replace(pattern, match => {
                const parts = match.split(/[:=]/);
                return parts[0] + (parts[1] ? ':***' : '');
            });
        });
        
        return this.truncateString(sanitized, this.MAX_FIELD_LENGTH);
    }
    
    /**
     * 掩码敏感值
     * @param {*} value - 敏感值
     * @returns {string} 掩码后的值
     */
    static maskSensitiveValue(value) {
        if (!value) return value;
        
        const str = String(value);
        if (str.length <= 4) {
            return '***';
        } else if (str.length <= 8) {
            return str.substring(0, 2) + '***';
        } else {
            return str.substring(0, 4) + '***' + str.substring(str.length - 2);
        }
    }
    
    /**
     * 截断字符串
     * @param {string} str - 原始字符串
     * @param {number} maxLength - 最大长度
     * @returns {string} 截断后的字符串
     */
    static truncateString(str, maxLength) {
        if (typeof str !== 'string') return str;
        
        if (str.length <= maxLength) {
            return str;
        }
        
        return str.substring(0, maxLength) + '...[truncated]';
    }
    
    /**
     * 截断数据
     * @param {*} data - 原始数据
     * @param {string} reason - 截断原因
     * @returns {Object} 截断信息
     */
    static truncateData(data, reason) {
        return {
            truncated: true,
            reason: reason,
            preview: typeof data === 'string' ? 
                data.substring(0, 500) + '...' : 
                JSON.stringify(data).substring(0, 500) + '...',
            original_type: typeof data,
            timestamp: new Date().toISOString()
        };
    }
    
    /**
     * 计算请求体大小
     * @param {*} body - 请求体
     * @returns {number} 大小（字节）
     */
    static calculateBodySize(body) {
        if (!body) return 0;
        
        try {
            if (typeof body === 'string') {
                return Buffer.byteLength(body, 'utf8');
            }
            
            return Buffer.byteLength(JSON.stringify(body), 'utf8');
        } catch (error) {
            return 0;
        }
    }
    
    /**
     * 处理文件上传信息
     * @param {*} files - 上传的文件
     * @returns {Array} 文件信息
     */
    static processFiles(files) {
        if (!files) return [];
        
        const fileInfos = [];
        
        try {
            if (Array.isArray(files)) {
                files.forEach(file => {
                    fileInfos.push(this.extractFileInfo(file));
                });
            } else if (typeof files === 'object') {
                Object.keys(files).forEach(key => {
                    const file = files[key];
                    if (Array.isArray(file)) {
                        file.forEach(f => fileInfos.push(this.extractFileInfo(f)));
                    } else {
                        fileInfos.push(this.extractFileInfo(file));
                    }
                });
            }
        } catch (error) {
            console.error('处理文件信息失败:', error);
        }
        
        return fileInfos;
    }
    
    /**
     * 提取文件信息
     * @param {Object} file - 文件对象
     * @returns {Object} 文件信息
     */
    static extractFileInfo(file) {
        if (!file) return null;
        
        return {
            fieldname: file.fieldname || 'unknown',
            originalname: file.originalname || 'unknown',
            mimetype: file.mimetype || 'unknown',
            size: file.size || 0,
            encoding: file.encoding || 'unknown',
            filename: file.filename || null,
            path: file.path ? '[file_path_hidden]' : null
        };
    }
    
    /**
     * 提取请求特征
     * @param {Object} req - Express请求对象
     * @returns {Object} 请求特征
     */
    static extractRequestFeatures(req) {
        return {
            has_query_params: !!(req.query && Object.keys(req.query).length > 0),
            has_body: !!(req.body && Object.keys(req.body).length > 0),
            has_files: !!(req.files && Object.keys(req.files).length > 0),
            content_type: req.get('content-type') || null,
            is_json: (req.get('content-type') || '').includes('application/json'),
            is_form: (req.get('content-type') || '').includes('application/x-www-form-urlencoded'),
            is_multipart: (req.get('content-type') || '').includes('multipart/form-data'),
            user_agent_length: (req.get('user-agent') || '').length,
            header_count: Object.keys(req.headers || {}).length
        };
    }
    
    /**
     * 生成请求摘要
     * @param {Object} requestData - 处理后的请求数据
     * @returns {string} 请求摘要
     */
    static generateRequestSummary(requestData) {
        const parts = [
            `${requestData.method} ${requestData.url}`,
            `Headers: ${Object.keys(requestData.headers || {}).length}`,
            `Body: ${requestData.stats?.body_size || 0}B`
        ];
        
        if (requestData.query && Object.keys(requestData.query).length > 0) {
            parts.push(`Query: ${Object.keys(requestData.query).length} params`);
        }
        
        if (requestData.files && requestData.files.length > 0) {
            parts.push(`Files: ${requestData.files.length}`);
        }
        
        return parts.join(' | ');
    }
}

module.exports = RequestDataProcessor;
