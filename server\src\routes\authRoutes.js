const express = require('express');
const jwt = require('jsonwebtoken');
const colors = require('colors');
const dataModel = require('../models/mysql-real-model');
const { authenticator } = require('otplib');
const qrcode = require('qrcode');
const configManager = require('../utils/config');
const mysqlDataModel = require('../models/mysql-real-model');

const router = express.Router();

// JWT 密钥和过期时间从配置读取
let JWT_SECRET, JWT_EXPIRES_IN;
(async () => {
  JWT_SECRET = await configManager.get('api.jwtSecret', 'xiya_signature_server_secret_key_2024');
  JWT_EXPIRES_IN = await configManager.get('api.jwtExpiresIn', '24h');
})();

// 登录接口 (增强版)
router.post('/login', async (req, res) => {
    try {
        const { username, password, remember = false } = req.body;
        const ip = req.ip || req.connection.remoteAddress;
        const userAgent = req.headers['user-agent'] || 'Unknown';

        if (!username || !password) {
            console.log(`❌ 登录参数不完整 - IP: ${ip}`.red);
            return res.status(400).json({
                code: 400,
                msg: '用户名和密码不能为空'
            });
        }

        console.log(`🔐 登录尝试 - 用户: ${username}, IP: ${ip}, UA: ${userAgent.substring(0, 50)}...`.yellow);

        // 验证用户
        const user = await dataModel.verifyUser(username, password);

        if (!user) {
            console.log(`❌ 登录失败 - 用户: ${username}, IP: ${ip}`.red);
            
            // 记录失败的登录尝试到MySQL数据库
            try {
                await mysqlDataModel.logSignRequest({
                    ip, uin: username, cmd: 'LOGIN', type: 'login', endpoint: '/api/auth/login',
                    success: false, error_msg: '用户名或密码错误',
                    request_data: JSON.stringify({ username }), response_data: null, user_agent: userAgent,
                    response_time_ms: 0
                });
            } catch (logError) {
                console.error('记录失败登录日志时出错:', logError);
            }
            
            return res.status(401).json({
                code: 401,
                msg: '用户名或密码错误'
            });
        }

        // 检查用户是否被禁用
        if (user.status === 'disabled') {
            console.log(`❌ 账户被禁用 - 用户: ${username}, IP: ${ip}`.red);
            return res.status(403).json({
                code: 403,
                msg: '账户已被禁用，请联系管理员'
            });
        }

        // 2FA判断
        if (user.totp_secret) {
            console.log('2FA分支 user对象:', user);
            const userId = user.id || user.ID || user.user_id || user.userid || user.uid || null;
            return res.json({
                code: 1001,
                msg: '需要二次验证码',
                data: {
                    need2fa: true,
                    user_id: userId
                }
            });
        }

        // 根据remember参数设置token过期时间
        const expiresIn = remember ? '7d' : JWT_EXPIRES_IN;

        // 生成 JWT token
        const token = jwt.sign(
            {
                id: user.id,
                username: user.username,
                role: user.role,
                loginTime: new Date().toISOString(),
                ip: ip
            },
            JWT_SECRET,
            { expiresIn }
        );

        console.log(`✅ 登录成功 - 用户: ${username}, IP: ${ip}, Token有效期: ${expiresIn}`.green);

        // 记录成功的登录到MySQL数据库
        try {
            await mysqlDataModel.logSignRequest({
                ip, uin: user.username, cmd: 'LOGIN', type: 'login', endpoint: '/api/auth/login',
                success: true, error_msg: null,
                request_data: JSON.stringify({ username: user.username }),
                response_data: JSON.stringify({ user_id: user.id, expires_in: expiresIn }),
                user_agent: userAgent, response_time_ms: 0
            });
        } catch (logError) {
            console.error('记录成功登录日志时出错:', logError);
        }

        // 返回响应
        res.json({
            code: 0,
            msg: '登录成功',
            data: {
                token,
                user: {
                    id: user.id,
                    username: user.username,
                    role: user.role,
                    loginTime: new Date().toISOString()
                },
                expiresIn,
                remember
            }
        });

    } catch (error) {
        console.error('登录接口错误:'.red, error);
        res.status(500).json({
            code: 500,
            msg: '服务器内部错误，请稍后重试'
        });
    }
});

// 2FA验证码校验接口
router.post('/login/2fa', async (req, res) => {
    try {
        const { user_id, code, remember = false } = req.body;
        const ip = req.ip || req.connection.remoteAddress;
        const userAgent = req.headers['user-agent'] || 'Unknown';

        console.log(`🔐 2FA验证尝试 - 用户ID: ${user_id}, IP: ${ip}`.yellow);

        if (!user_id) {
            console.log(`❌ 2FA验证参数不完整 - 用户ID: ${user_id}`.red);
            return res.error('用户信息缺失，请重新登录', 400);
        }
        if (!code) {
            console.log(`❌ 2FA验证参数不完整 - 用户ID: ${user_id}, 验证码: 未提供`.red);
            return res.error('请输入6位验证码', 400);
        }
        if (code.length !== 6 || !/^[0-9]{6}$/.test(code)) {
            console.log(`❌ 2FA验证码格式错误 - 用户ID: ${user_id}, 验证码长度: ${code.length}`.red);
            return res.error('验证码必须是6位数字，请重新输入', 400);
        }

        const user = await dataModel.getUserById(user_id);
        if (!user) {
            console.log(`❌ 2FA验证失败 - 用户不存在: ${user_id}`.red);
            return res.error('用户不存在', 403);
        }

        if (!user.totp_secret) {
            console.log(`❌ 2FA验证失败 - 用户未绑定2FA: ${user.username}`.red);
            return res.error('用户未绑定2FA', 403);
        }

        console.log(`🔍 验证2FA - 用户: ${user.username}, 验证码: ${code}`.cyan);
        
        const valid = dataModel.verifyTotpCode(user.totp_secret, code);
        if (!valid) {
            console.log(`❌ 2FA验证失败 - 验证码错误: ${user.username}, IP: ${ip}`.red);
            // 记录失败的2FA验证尝试到MySQL数据库
            try {
                await mysqlDataModel.logSignRequest({
                    ip, uin: user.username, cmd: '2FA', type: 'login', endpoint: '/api/auth/verify-2fa',
                    success: false, error_msg: '2FA验证码错误',
                    request_data: JSON.stringify({ username: user.username }), response_data: null,
                    user_agent: userAgent, response_time_ms: 0
                });
            } catch (logError) {
                console.error('记录2FA失败日志时出错:', logError);
            }
            return res.error('验证码错误，请检查您的身份验证器中的6位数字代码', 401);
        }

        // 生成token
        const expiresIn = remember ? '7d' : JWT_EXPIRES_IN;
        const token = jwt.sign(
            { 
                id: user.id, 
                username: user.username, 
                role: user.role, 
                loginTime: new Date().toISOString(), 
                ip: ip 
            }, 
            JWT_SECRET, 
            { expiresIn }
        );

        console.log(`✅ 2FA验证成功 - 用户: ${user.username}, IP: ${ip}`.green);

        try {
            await mysqlDataModel.logSignRequest({
                ip, uin: user.username, cmd: '2FA', type: 'login', endpoint: '/api/auth/verify-2fa',
                success: true, error_msg: null,
                request_data: JSON.stringify({ username: user.username }),
                response_data: JSON.stringify({ user_id: user.id, expires_in: expiresIn }),
                user_agent: userAgent, response_time_ms: 0
            });
        } catch (logError) {
            console.error('记录2FA成功登录日志时出错:', logError);
        }

        return res.success({
            token,
            user: {
                id: user.id,
                username: user.username,
                role: user.role,
                loginTime: new Date().toISOString()
            },
            expiresIn,
            remember
        }, '登录成功');
        
    } catch (error) {
        console.error('2FA验证接口错误:'.red, error);
        return res.error('服务器内部错误，请稍后重试', 500);
    }
});

// 验证 token 中间件 (增强版)
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
        console.log(`❌ 访问令牌缺失 - IP: ${req.ip}`.red);
        return res.status(401).json({
            code: 401,
            msg: '访问令牌缺失'
        });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            console.log(`❌ 访问令牌验证失败 - IP: ${req.ip}, Error: ${err.message}`.red);
            
            // 根据错误类型返回不同的状态码
            if (err.name === 'TokenExpiredError') {
                return res.status(401).json({
                    code: 401,
                    msg: '访问令牌已过期',
                    expired: true
                });
            } else if (err.name === 'JsonWebTokenError') {
                return res.status(403).json({
                    code: 403,
                    msg: '访问令牌格式错误'
                });
            } else {
                return res.status(403).json({
                    code: 403,
                    msg: '访问令牌无效'
                });
            }
        }

        req.user = user;
        next();
    });
}

// Token 刷新接口
router.post('/refresh', authenticateToken, async (req, res) => {
    try {
        const user = req.user;
        const ip = req.ip || req.connection.remoteAddress;

        console.log(`🔄 Token刷新请求 - 用户: ${user.username}, IP: ${ip}`.cyan);

        // 验证用户是否仍然存在且有效
        const currentUser = await dataModel.getUserById(user.id);
        if (!currentUser) {
            console.log(`❌ Token刷新失败 - 用户不存在: ${user.username}`.red);
            return res.status(401).json({
                code: 401,
                msg: '用户不存在'
            });
        }

        // 生成新的 JWT token
        const newToken = jwt.sign(
            {
                id: currentUser.id,
                username: currentUser.username,
                role: currentUser.role
            },
            JWT_SECRET,
            { expiresIn: JWT_EXPIRES_IN }
        );

        console.log(`✅ Token刷新成功 - 用户: ${user.username}, IP: ${ip}`.green);

        res.json({
            code: 0,
            msg: 'Token刷新成功',
            data: {
                token: newToken,
                user: {
                    id: currentUser.id,
                    username: currentUser.username,
                    role: currentUser.role
                }
            }
        });

    } catch (error) {
        console.error('Token刷新接口错误:'.red, error);
        res.status(500).json({
            code: 500,
            msg: '服务器内部错误'
        });
    }
});

// 验证当前用户信息
router.get('/verify', authenticateToken, (req, res) => {
    res.json({
        code: 0,
        msg: '验证成功',
        data: {
            user: req.user
        }
    });
});

// 获取用户列表 (需要管理员权限)
router.get('/users', authenticateToken, async (req, res) => {
    try {
        if (req.user.role !== 'admin') {
            return res.status(403).json({
                code: 403,
                msg: '权限不足'
            });
        }

        const users = await dataModel.getAllUsers();
        
        res.json({
            code: 0,
            msg: '查询成功',
            data: users
        });

    } catch (error) {
        console.error('获取用户列表错误:'.red, error);
        res.status(500).json({
            code: 500,
            msg: '服务器内部错误'
        });
    }
});

// 登出接口 (客户端处理，服务端记录日志)
router.post('/logout', authenticateToken, (req, res) => {
    const ip = req.ip || req.connection.remoteAddress;
    console.log(`👋 用户登出 - 用户: ${req.user.username}, IP: ${ip}`.cyan);
    
    res.json({
        code: 0,
        msg: '登出成功'
    });
});

// 获取当前用户2FA状态
router.get('/2fa/status', authenticateToken, async (req, res) => {
    try {
        const user = await dataModel.getUserById(req.user.id);
        if (!user) return res.status(401).json({ code: 401, msg: '用户不存在' });
        res.json({
            code: 0,
            msg: 'ok',
            data: {
                enabled: !!user.totp_secret,
                secret: user.totp_secret ? user.totp_secret.replace(/.(?=.{4})/g, '*') : null // 脱敏
            }
        });
    } catch (error) {
        res.status(500).json({ code: 500, msg: '服务器内部错误' });
    }
});

// 生成2FA绑定二维码和密钥
router.get('/2fa/generate', authenticateToken, async (req, res) => {
    try {
        const user = await dataModel.getUserById(req.user.id);
        if (!user) return res.status(401).json({ code: 401, msg: '用户不存在' });
        if (user.totp_secret) return res.status(400).json({ code: 400, msg: '已绑定2FA' });
        const secret = authenticator.generateSecret();
        const otpauth = authenticator.keyuri(user.username, 'QsignHook', secret);
        const qr = await qrcode.toDataURL(otpauth);
        res.json({ code: 0, msg: 'ok', data: { secret, otpauth, qr } });
    } catch (error) {
        res.status(500).json({ code: 500, msg: '服务器内部错误' });
    }
});

// 绑定2FA
router.post('/2fa/bind', authenticateToken, async (req, res) => {
    try {
        const { secret, code } = req.body;
        const ip = req.ip || req.connection.remoteAddress;
        const now = new Date();
        console.log(`[2FA调试] 绑定尝试 - 用户: ${req.user.username}, IP: ${ip}`.yellow);
        console.log(`[2FA调试] 收到参数: secret(前4)=${secret?.slice(0,4)}..., code=${code}, 服务器时间=${now.toISOString()}`);
        if (!secret || !code) {
            console.log(`[2FA调试] 参数不完整 - secret: ${!!secret}, code: ${!!code}`.red);
            return res.status(400).json({ code: 400, msg: '参数不完整' });
        }
        if (code.length !== 6 || !/^\d{6}$/.test(code)) {
            console.log(`[2FA调试] 验证码格式错误 - code: ${code}`.red);
            return res.status(400).json({ code: 400, msg: '验证码格式错误，请输入6位数字' });
        }
        // 生成当前TOTP及前后窗口TOTP
        const totpLib = require('otplib');
        const window = 1;
        const serverTime = Math.floor(now.getTime() / 1000);
        let debugTotps = [];
        for (let w = -window; w <= window; w++) {
          const t = serverTime + w * 30;
          const gen = totpLib.authenticator.generate(secret, { epoch: t * 1000 });
          debugTotps.push(`[${w===0?'当前':'偏移'+w}] ${gen}`);
        }
        console.log(`[2FA调试] 服务器TOTP窗口(-1/0/+1):\n  ` + debugTotps.join('\n  '));
        // 使用统一的验证方法
        const valid = dataModel.verifyTotpCode(secret, code);
        console.log(`[2FA调试] 校验结果: ${valid}`);
        if (!valid) {
            console.log(`[2FA调试] 绑定失败 - 用户: ${req.user.username}, code: ${code}, secret(前4): ${secret?.slice(0,4)}...`.red);
            return res.status(401).json({ 
                code: 401, 
                msg: '验证码错误，请检查您的身份验证器中的6位数字代码，确保时间同步正确' 
            });
        }
        await dataModel.setUserTotpSecret(req.user.id, secret);
        console.log(`[2FA调试] 绑定成功 - 用户: ${req.user.username}`.green);
        res.json({ code: 0, msg: '2FA绑定成功' });
    } catch (error) {
        console.error('[2FA调试] 绑定接口错误:'.red, error);
        res.status(500).json({ code: 500, msg: '服务器内部错误，请稍后重试' });
    }
});

// 解绑2FA
router.post('/2fa/unbind', authenticateToken, async (req, res) => {
    try {
        const { code } = req.body;
        const ip = req.ip || req.connection.remoteAddress;
        const now = new Date();
        const user = await dataModel.getUserById(req.user.id);
        console.log(`[2FA调试] 解绑尝试 - 用户: ${req.user.username}, IP: ${ip}`.yellow);
        console.log(`[2FA调试] 收到参数: code=${code}, 服务器时间=${now.toISOString()}`);
        if (!code) {
            console.log(`[2FA调试] 参数不完整 - code: ${!!code}`.red);
            return res.status(400).json({ code: 400, msg: '请提供验证码' });
        }
        if (code.length !== 6 || !/^\d{6}$/.test(code)) {
            console.log(`[2FA调试] 验证码格式错误 - code: ${code}`.red);
            return res.status(400).json({ code: 400, msg: '验证码格式错误，请输入6位数字' });
        }
        if (!user || !user.totp_secret) {
            console.log(`[2FA调试] 未绑定2FA - 用户: ${req.user.username}`.red);
            return res.status(400).json({ code: 400, msg: '未绑定2FA' });
        }
        // 生成当前TOTP及前后窗口TOTP
        const totpLib = require('otplib');
        const window = 1;
        const serverTime = Math.floor(now.getTime() / 1000);
        let debugTotps = [];
        for (let w = -window; w <= window; w++) {
          const t = serverTime + w * 30;
          const gen = totpLib.authenticator.generate(user.totp_secret, { epoch: t * 1000 });
          debugTotps.push(`[${w===0?'当前':'偏移'+w}] ${gen}`);
        }
        console.log(`[2FA调试] 服务器TOTP窗口(-1/0/+1):\n  ` + debugTotps.join('\n  '));
        // 使用统一的验证方法
        const valid = dataModel.verifyTotpCode(user.totp_secret, code);
        console.log(`[2FA调试] 校验结果: ${valid}`);
        if (!valid) {
            console.log(`[2FA调试] 解绑失败 - 用户: ${req.user.username}, code: ${code}`.red);
            return res.status(401).json({ 
                code: 401, 
                msg: '验证码错误，请检查您的身份验证器中的6位数字代码' 
            });
        }
        await dataModel.setUserTotpSecret(req.user.id, null);
        console.log(`[2FA调试] 解绑成功 - 用户: ${req.user.username}`.green);
        res.json({ code: 0, msg: '2FA已解绑' });
    } catch (error) {
        console.error('[2FA调试] 解绑接口错误:'.red, error);
        res.status(500).json({ code: 500, msg: '服务器内部错误，请稍后重试' });
    }
});

module.exports = {
    router,
    authenticateToken
};
