const MySQLDataModel = require('./src/models/mysql-real-model');

async function initRiskSettings() {
    console.log('🚀 开始初始化风控配置表...');
    
    try {
        const mysqlModel = new MySQLDataModel();
        
        // 初始化风控配置表
        await mysqlModel.initRiskControlSettingsTable();
        
        console.log('✅ 风控配置表初始化成功！');
        
        // 验证配置
        const settings = await mysqlModel.getRiskControlSettings();
        console.log('📋 当前配置项数量:', Object.keys(settings).length);
        
        console.log('🎉 初始化完成！');
        
    } catch (error) {
        console.error('❌ 初始化失败:', error);
        process.exit(1);
    } finally {
        process.exit(0);
    }
}

// 运行初始化
initRiskSettings();
