const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const multer = require('multer');
const colors = require('colors');
const path = require('path');
const fs = require('fs');
const readline = require('readline');
const cron = require('node-cron');

// 导入配置管理器
const configManager = require('./utils/config');

// 导入数据库清理工具
const DatabaseCleaner = require('./utils/database-cleaner');

// 导入路由
const signRoutes = require('./routes/signRoutes');
const logRoutes = require('./routes/logRoutes-mysql');
const statsRoutes = require('./routes/statsRoutes-mysql');
const settingsRoutes = require('./routes/settingsRoutes');
const configRoutes = require('./routes/configRoutes');
const tokenManagementRoutes = require('./routes/tokenManagementRoutes');
const { router: authRoutes } = require('./routes/authRoutes');
const { qqLoginVerify1, qqLoginVerify2, qqLoginVerify3 } = require('./routes/qqVerifyRoutes');
const qimeiRoutes = require('./routes/qimeiRoutes');
const riskRoutes = require('./routes/riskRoutes');
const userManagementRoutes = require('./routes/userManagementRoutes');
const signNodeRoutes = require('./routes/signNodeRoutes');

// 导入数据库初始化
const MySQLDatabaseInit = require('./scripts/mysql-database-init');
const mysqlModel = require('./models/mysql-real-model');

// 导入WebSocket服务
const webSocketService = require('./services/websocket-service');

// 导入签名服务管理器
const signatureServiceManager = require('./services/SignatureServiceManager');

// 导入响应格式助手
const {
  responseMiddleware,
  responseFormatterMiddleware,
  enhancedErrorHandlerMiddleware
} = require('./utils/responseHelper');

const app = express();

// 中间件
app.use(cors());

// 使用body-parser的verify选项捕获原始请求体
app.use(bodyParser.json({
  verify: (req, res, buf, encoding) => {
    req.rawBody = buf.toString('utf8');
  }
}));

app.use(bodyParser.urlencoded({
  extended: true,
  verify: (req, res, buf, encoding) => {
    if (!req.rawBody) {
      req.rawBody = buf.toString('utf8');
    }
  }
}));

app.use(bodyParser.raw({
  verify: (req, res, buf, encoding) => {
    if (!req.rawBody) {
      req.rawBody = buf.toString('utf8');
    }
  }
}));

// 配置multer处理multipart/form-data
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB限制
    fieldSize: 1024 * 1024,     // 1MB字段大小限制
    fields: 50                   // 最多50个字段
  }
});

// 使用multer中间件处理multipart/form-data
app.use(upload.any());

// 添加统一响应格式中间件
app.use(responseMiddleware);

// 添加响应格式标准化中间件
app.use(responseFormatterMiddleware);

// 请求日志中间件
app.use((req, res, next) => {
    const timestamp = new Date().toISOString();
    const method = req.method.padEnd(6);
    const path = req.path.padEnd(25);
    const ip = (req.ip || req.connection.remoteAddress).padEnd(15);
    
    // 根据方法类型使用不同颜色
    let coloredMethod;
    switch(req.method) {
        case 'GET':
            coloredMethod = method.green;
            break;
        case 'POST':
            coloredMethod = method.blue;
            break;
        case 'PUT':
            coloredMethod = method.yellow;
            break;
        case 'DELETE':
            coloredMethod = method.red;
            break;
        default:
            coloredMethod = method.white;
    }
    
    console.log(`[${timestamp.gray}] ${coloredMethod} ${path.cyan} - IP: ${ip.magenta}`);
    next();
});

// 路由
app.use('/api/auth', authRoutes);
app.use('/api/config', configRoutes);
app.use('/api', logRoutes);
app.use('/api', statsRoutes);
app.use('/api', settingsRoutes);
app.use('/api/token-management', tokenManagementRoutes);
app.use('/api/qimei', qimeiRoutes);
app.use('/api/risk', riskRoutes);
app.use('/api/user-management', userManagementRoutes(mysqlModel));
app.use('/api/sign-nodes', signNodeRoutes);

// 签名API路由 - 按照接口文档规范
// /api/sign, /api/Get0553, /api/energy
app.use('/api', signRoutes);

// QQ身份验证路由
app.post('/api/qq-verify/step1', qqLoginVerify1);
app.post('/api/qq-verify/step2', qqLoginVerify2);
app.post('/api/qq-verify/step3', qqLoginVerify3);

// 健康检查
app.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 公开的节点状态页面（不需要认证）
app.get('/status', async (req, res) => {
    try {
        // 获取所有节点配置
        const allConfigs = await mysqlModel.getAllApiConfigs();

        // 获取节点信息（隐藏敏感信息）
        const response = await fetch(`http://localhost:${PORT}/api/public/nodes/status`);
        const apiData = await response.json();
        const nodes = apiData.data.nodes;

        // 使用API返回的统计信息
        const stats = apiData.data.stats;

        const statusPage = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌸 签名服务节点状态</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.2em; opacity: 0.9; }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8f9fa;
        }
        .stat-card {
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
            border: 1px solid #91d5ff;
        }
        .stat-card:hover { transform: translateY(-5px); }
        .stat-number { font-size: 2em; font-weight: bold; color: #1890ff; }
        .stat-label { color: #666; margin-top: 5px; }
        .nodes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
            padding: 30px;
        }
        .node-card {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .node-card:hover {
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        .node-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .node-type {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .node-info { margin-bottom: 10px; }
        .info-label { font-weight: bold; color: #666; display: inline-block; width: 80px; }
        .info-value { color: #333; }
        .token-badge {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.7em;
            font-weight: bold;
            margin-left: 10px;
        }
        .token-exclusive { background: #fff3cd; color: #856404; }
        .token-shared { background: #d1ecf1; color: #0c5460; }
        .token-none { background: #f8d7da; color: #721c24; }
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            background: #f8f9fa;
        }
        .refresh-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s ease;
        }
        .refresh-btn:hover { background: #5a6fd8; }
        @media (max-width: 768px) {
            .stats { grid-template-columns: repeat(2, 1fr); }
            .nodes-grid { grid-template-columns: 1fr; }
            .header h1 { font-size: 2em; }
        }
    </style>
    <script>
        function refreshPage() {
            window.location.reload();
        }

        // 自动刷新（每30秒）
        setInterval(refreshPage, 30000);

        // 显示最后更新时间
        window.onload = function() {
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString('zh-CN');
        }
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌸 签名服务节点状态</h1>
            <p>实时监控所有签名服务节点的运行状态</p>
            <button class="refresh-btn" onclick="refreshPage()">🔄 刷新状态</button>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${stats.totalNodes}</div>
                <div class="stat-label">总节点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.activeNodes}</div>
                <div class="stat-label">活跃节点</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.totalTodayCalls || 0}</div>
                <div class="stat-label">今日调用</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.totalAllCalls || 0}</div>
                <div class="stat-label">总调用数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.avgSuccessRate || 0}%</div>
                <div class="stat-label">平均成功率</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${stats.nodesWithToken}</div>
                <div class="stat-label">已配置Token</div>
            </div>
        </div>

        <div class="nodes-grid">
            ${nodes.map(node => `
                <div class="node-card">
                    <div class="node-header">
                        <div class="node-type">${node.nodeType}</div>
                        <span class="status-badge status-${node.status}">${node.status === 'active' ? '运行中' : '已停用'}</span>
                    </div>
                    <div class="node-info">
                        <span class="info-label">节点ID:</span>
                        <span class="info-value">${node.nodeId}</span>
                    </div>
                    <div class="node-info">
                        <span class="info-label">描述:</span>
                        <span class="info-value">${node.description || '无描述'}</span>
                    </div>
                    <div class="node-info">
                        <span class="info-label">今日调用:</span>
                        <span class="info-value" style="color: #52c41a; font-weight: bold;">${node.todayCalls || 0} 次</span>
                    </div>
                    <div class="node-info">
                        <span class="info-label">总调用:</span>
                        <span class="info-value" style="color: #1890ff; font-weight: bold;">${node.totalCalls || 0} 次</span>
                    </div>
                    <div class="node-info">
                        <span class="info-label">成功率:</span>
                        <span class="info-value" style="color: ${(node.successRate || 0) >= 95 ? '#52c41a' : (node.successRate || 0) >= 80 ? '#faad14' : '#ff4d4f'}; font-weight: bold;">${node.successRate || 0}%</span>
                    </div>
                    <div class="node-info">
                        <span class="info-label">Token:</span>
                        <span class="info-value">
                            ${node.hasToken ? '已配置' : '未配置'}
                            ${node.hasToken ? `<span class="token-badge token-${node.tokenMode}">${node.tokenMode === 'exclusive' ? '独享' : '共享'}</span>` : '<span class="token-badge token-none">无Token</span>'}
                        </span>
                    </div>
                    <div class="node-info">
                        <span class="info-label">超时:</span>
                        <span class="info-value">${node.timeout}ms</span>
                    </div>
                    <div class="node-info">
                        <span class="info-label">限制:</span>
                        <span class="info-value">${node.requestLimit === 0 ? '无限制' : node.requestLimit + '/分钟'}</span>
                    </div>
                </div>
            `).join('')}
        </div>

        <div class="footer">
            <p>🌸 最后更新时间: <span id="lastUpdate"></span></p>
            <p>页面每30秒自动刷新 | <a href="/docs" style="color: #667eea;">API文档</a> | <a href="/health" style="color: #667eea;">健康检查</a></p>
        </div>
    </div>
</body>
</html>`;

        res.setHeader('Content-Type', 'text/html; charset=utf-8');
        res.send(statusPage);

    } catch (error) {
        console.error('获取节点状态失败:', error);
        res.status(500).json({
            code: 500,
            msg: '获取节点状态失败',
            error: error.message
        });
    }
});

// 公开的节点状态API（JSON格式，不需要认证）
app.get('/api/public/nodes/status', async (req, res) => {
    try {
        // 获取所有节点配置
        const allConfigs = await mysqlModel.getAllApiConfigs();

        // 获取每个节点的调用统计和实时健康状态
        const nodesWithStats = await Promise.all(allConfigs.map(async (config) => {
            try {
                // 获取今日调用次数
                const todayStats = await mysqlModel.db.query(`
                    SELECT COUNT(*) as today_calls
                    FROM sign_logs
                    WHERE type = ? AND DATE(request_time) = CURDATE()
                `, [config.client_type]);

                // 获取总调用次数
                const totalStats = await mysqlModel.db.query(`
                    SELECT COUNT(*) as total_calls
                    FROM sign_logs
                    WHERE type = ?
                `, [config.client_type]);

                // 获取成功率（最近24小时）
                const successStats = await mysqlModel.db.query(`
                    SELECT
                        COUNT(*) as total_requests,
                        SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_requests
                    FROM sign_logs
                    WHERE type = ? AND request_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                `, [config.client_type]);

                const todayCalls = todayStats[0]?.today_calls || 0;
                const totalCalls = totalStats[0]?.total_calls || 0;
                const totalRequests = successStats[0]?.total_requests || 0;
                const successfulRequests = successStats[0]?.successful_requests || 0;
                const successRate = totalRequests > 0 ? ((successfulRequests / totalRequests) * 100).toFixed(1) : 0;

                // 实时健康检查
                let healthStatus = 'unknown';
                let isHealthy = false;

                try {
                    // 检查节点健康状态
                    const axios = require('axios');
                    const healthCheckUrl = config.base_url.endsWith('/')
                        ? `${config.base_url}health`
                        : `${config.base_url}/health`;

                    const healthResponse = await axios.get(healthCheckUrl, {
                        timeout: 3000,
                        validateStatus: () => true // 接受任何状态码
                    });

                    // 如果有响应，认为节点健康
                    isHealthy = healthResponse.status >= 200 && healthResponse.status < 500;
                    healthStatus = isHealthy ? 'active' : 'inactive';
                } catch (error) {
                    // 如果请求失败，节点不健康
                    console.log(`❌ 节点 ${config.client_type} 健康检查失败: ${error.message}`);
                    isHealthy = false;
                    healthStatus = 'inactive';
                }

                return {
                    nodeType: config.client_type,
                    nodeId: config.client_type, // 显示节点ID而不是完整URL
                    status: healthStatus, // 使用实时健康状态替代数据库状态
                    dbStatus: config.status || 'active', // 保留数据库中的状态
                    description: config.description || '',
                    hasToken: !!(config.auth_key && config.auth_key.length === 128),
                    tokenMode: config.token_mode || 'exclusive',
                    timeout: config.timeout || 10000,
                    requestLimit: config.request_limit || 0,
                    updatedAt: config.updated_at,
                    // 统计信息
                    todayCalls: parseInt(todayCalls),
                    totalCalls: parseInt(totalCalls),
                    successRate: parseFloat(successRate),
                    lastCallTime: null, // 可以后续添加最后调用时间
                    isHealthy: isHealthy // 添加健康状态标志
                };
            } catch (error) {
                console.error(`获取节点 ${config.client_type} 统计失败:`, error);
                return {
                    nodeType: config.client_type,
                    nodeId: config.client_type,
                    status: config.status || 'active',
                    description: config.description || '',
                    hasToken: !!(config.auth_key && config.auth_key.length === 128),
                    tokenMode: config.token_mode || 'exclusive',
                    timeout: config.timeout || 10000,
                    requestLimit: config.request_limit || 0,
                    updatedAt: config.updated_at,
                    todayCalls: 0,
                    totalCalls: 0,
                    successRate: 0,
                    lastCallTime: null
                };
            }
        }));

        // 获取系统统计信息
        const totalTodayCalls = nodesWithStats.reduce((sum, node) => sum + node.todayCalls, 0);
        const totalAllCalls = nodesWithStats.reduce((sum, node) => sum + node.totalCalls, 0);
        const avgSuccessRate = nodesWithStats.length > 0
            ? (nodesWithStats.reduce((sum, node) => sum + node.successRate, 0) / nodesWithStats.length).toFixed(1)
            : 0;

        const stats = {
            totalNodes: nodesWithStats.length,
            activeNodes: nodesWithStats.filter(n => n.status === 'active').length,
            healthyNodes: nodesWithStats.filter(n => n.isHealthy).length,
            nodesWithToken: nodesWithStats.filter(n => n.hasToken).length,
            exclusiveTokens: nodesWithStats.filter(n => n.tokenMode === 'exclusive' && n.hasToken).length,
            sharedTokens: nodesWithStats.filter(n => n.tokenMode === 'shared' && n.hasToken).length,
            totalTodayCalls,
            totalAllCalls,
            avgSuccessRate: parseFloat(avgSuccessRate)
        };

        res.json({
            code: 0,
            msg: 'success',
            data: {
                nodes: nodesWithStats,
                stats,
                timestamp: new Date().toISOString()
            }
        });

    } catch (error) {
        console.error('获取节点状态失败:', error);
        res.status(500).json({
            code: 500,
            msg: '获取节点状态失败',
            error: error.message
        });
    }
});

// API文档
app.get('/docs', (req, res) => {
    const apiDocs = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QSign Hook Forward API 文档</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif; margin: 40px; background: #f5f7fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #2c3e50; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        h2 { color: #34495e; margin-top: 30px; }
        .endpoint { background: #ecf0f1; padding: 20px; margin: 15px 0; border-radius: 6px; border-left: 4px solid #3498db; }
        .method { background: #27ae60; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold; }
        .url { font-family: 'Courier New', monospace; background: #34495e; color: white; padding: 8px; border-radius: 4px; margin: 10px 0; }
        .example { background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 4px; font-family: 'Courier New', monospace; font-size: 14px; margin: 10px 0; overflow-x: auto; }
        .params { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .param { margin: 8px 0; }
        .param-name { font-weight: bold; color: #e74c3c; }
        .param-desc { color: #7f8c8d; margin-left: 10px; }
        .note { background: #fff3cd; color: #856404; padding: 12px; border-radius: 4px; border-left: 4px solid #ffc107; margin: 15px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 QSign Hook Forward API 文档</h1>
        
        <div class="note">
            <strong>🔑 认证说明：</strong> 所有API请求都需要在请求头中包含 <code>x-xiya-authkey</code> 认证密钥。
        </div>

        <h2>📝 签名接口</h2>
        
        <div class="endpoint">
            <h3><span class="method">POST</span> /api/sign</h3>
            <p><strong>功能：</strong>获取QQ登录签名算法</p>
            
            <div class="url">POST http://127.0.0.1:12041/api/sign?Type=QQ</div>
            
            <div class="params">
                <h4>请求参数:</h4>
                <div class="param">
                    <span class="param-name">Type</span> (查询参数) - 签名类型: QQ, qidian, qqlite, tim
                </div>
                <div class="param">
                    <span class="param-name">cmd</span> (POST Body) - 命令类型，如: wtlogin.login
                </div>
                <div class="param">
                    <span class="param-name">uin</span> (POST Body) - QQ号码
                </div>
                <div class="param">
                    <span class="param-name">data</span> (POST Body) - 请求数据
                </div>
                <div class="param">
                    <span class="param-name">seq</span> (POST Body) - 序列号
                </div>
            </div>
            
            <div class="example">POST /api/sign?Type=QQ HTTP/1.1
Host: 127.0.0.1:12041
x-xiya-authkey: 1145141919810
Content-Type: application/x-www-form-urlencoded

cmd=wtlogin.login&uin=123456789&data=1145141919810&seq=123</div>
        </div>

        <div class="endpoint">
            <h3><span class="method">POST</span> /api/Get0553</h3>
            <p><strong>功能：</strong>获取0553调试ID</p>
            
            <div class="url">POST http://127.0.0.1:12041/api/Get0553?Type=QQ</div>
            
            <div class="params">
                <h4>请求参数:</h4>
                <div class="param">
                    <span class="param-name">Type</span> (查询参数) - 签名类型: QQ, qidian, qqlite, tim
                </div>
                <div class="param">
                    <span class="param-name">uin</span> (POST Body) - QQ号码
                </div>
                <div class="param">
                    <span class="param-name">cmd</span> (POST Body) - 命令ID
                </div>
                <div class="param">
                    <span class="param-name">data</span> (POST Body) - 请求数据
                </div>
                <div class="param">
                    <span class="param-name">ssoseq</span> (POST Body) - SSO序列号
                </div>
            </div>
            
            <div class="example">POST /api/Get0553?Type=QQ HTTP/1.1
Host: 127.0.0.1:12041
x-xiya-authkey: 1145141919810
Content-Type: application/x-www-form-urlencoded

uin=114514&ssoseq=114514&data=114514&cmd=114514</div>
        </div>

        <div class="endpoint">
            <h3><span class="method">POST</span> /api/energy</h3>
            <p><strong>功能：</strong>获取Energy签名</p>
            
            <div class="url">POST http://127.0.0.1:12041/api/energy?Type=QQ</div>
            
            <div class="params">
                <h4>请求参数:</h4>
                <div class="param">
                    <span class="param-name">Type</span> (查询参数) - 签名类型: QQ, qidian, qqlite, tim
                </div>
                <div class="param">
                    <span class="param-name">data</span> (POST Body) - 命令数据，格式如: 810_9
                </div>
                <div class="param">
                    <span class="param-name">guid</span> (POST Body) - GUID标识符
                </div>
                <div class="param">
                    <span class="param-name">version</span> (POST Body) - 版本信息
                </div>
            </div>
            
            <div class="example">POST /api/energy?Type=QQ HTTP/1.1
Host: 127.0.0.1:12041
x-xiya-authkey: 1145141919810
Content-Type: application/x-www-form-urlencoded

data=810_9&guid=1145141919810&version=6.0.254</div>
        </div>

        <h2>🔧 管理接口</h2>
        
        <div class="endpoint">
            <h3><span class="method">GET</span> /health</h3>
            <p><strong>功能：</strong>健康检查</p>
            <div class="url">GET http://127.0.0.1:12041/health</div>
        </div>

        <div class="endpoint">
            <h3><span class="method">GET</span> /api/stats/overview</h3>
            <p><strong>功能：</strong>获取统计概览</p>
            <div class="url">GET http://127.0.0.1:12041/api/stats/overview</div>
        </div>

        <h2>📊 响应格式</h2>
        <div class="params">
            <h4>成功响应:</h4>
            <div class="example">{
  "code": 0,
  "msg": "success",
  "data": "签名结果或响应数据"
}</div>
            
            <h4>错误响应:</h4>
            <div class="example">{
  "code": 1,
  "msg": "错误描述",
  "data": null
}</div>
        </div>

        <div class="note">
            <strong>📝 注意事项：</strong>
            <ul>
                <li>所有POST请求都使用 <code>application/x-www-form-urlencoded</code> 格式</li>
                <li>Type参数通过URL查询参数传递，其他参数通过POST Body传递</li>
                <li>认证密钥请妥善保管，避免泄露</li>
                <li>建议在生产环境中使用HTTPS</li>
            </ul>
        </div>
    </div>
</body>
</html>`;
    
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.send(apiDocs);
});

// MySQL数据库初始化已在上方导入

// 清理旧的JSON数据库文件
async function cleanupOldJsonDatabase() {
    try {
        const oldDbPath = path.join(__dirname, '../database/signature.json');
        if (fs.existsSync(oldDbPath)) {
            const backupPath = oldDbPath + '.backup.' + Date.now();
            fs.renameSync(oldDbPath, backupPath);
            console.log('📦 旧JSON数据库已备份:'.yellow, path.basename(backupPath));
        }
    } catch (error) {
        console.warn('⚠️  清理旧数据库文件失败:'.yellow, error.message);
    }
}

// 添加增强版错误处理中间件
app.use(enhancedErrorHandlerMiddleware);

// 404 处理
app.use('*', (req, res) => {
    const timestamp = new Date().toISOString();
    console.log(`🔍 404 Not Found: ${req.method} ${req.originalUrl} - IP: ${req.ip}`.yellow);
    
    res.status(404).json({
        code: 404,
        msg: '接口不存在',
        path: req.originalUrl,
        method: req.method,
        timestamp: timestamp,
        suggestion: '请检查API路径是否正确'
    });
});

// 控制台热启动/停止 WebSocket 服务端
const rl = readline.createInterface({ input: process.stdin, output: process.stdout });
rl.on('line', async (input) => {
  const cmd = input.trim().toLowerCase();
  if (cmd === 'ws start') {
    if (!webSocketService.isRunning) {
      await webSocketService.start();
    } else {
      console.log('WebSocket服务端已在运行');
    }
  } else if (cmd === 'ws stop') {
    if (webSocketService.isRunning) {
      await webSocketService.stop();
    } else {
      console.log('WebSocket服务端未在运行');
    }
  }
});

// 启动定时清理MySQL数据库日志任务
cron.schedule('0 3 * * *', async () => {
  try {
    // 读取设置
    const autoClean = await mysqlModel.getSetting('auto_clean_logs', true);
    const keepDays = await mysqlModel.getSetting('clean_logs_days', 30);
    if (autoClean) {
      // 清理MySQL数据库中的所有旧日志（包括签名日志、未授权访问日志等）
      const result = await mysqlModel.cleanOldLogs(Number(keepDays));
      console.log(`[定时清理] 已清理${keepDays}天前的MySQL数据库日志，共删除 ${result.deleted} 条。`);
    } else {
      console.log('[定时清理] 自动清理日志已关闭');
    }
  } catch (err) {
    console.error('[定时清理] 清理日志出错:', err);
  }
}, {
  timezone: 'Asia/Shanghai'
});

// 初始化数据库并启动服务器
async function startServer() {
    // 防止重复启动
    if (global.qsignServerStarted) {
        console.log('⚠️  服务器已经启动，跳过重复启动'.yellow);
        return global.qsignServer;
    }
    
    try {
        global.qsignServerStarted = true;
        
        console.log('\n⚙️  开始启动服务器...'.cyan.bold);
        
        // 检查是否通过全栈启动器启动
        if (process.env.QSIGN_LAUNCHER === 'fullstack') {
            console.log('📋 检测到全栈启动器，使用简化启动流程'.gray);
        }
        
        // 首先初始化配置管理器
        console.log('📋 初始化配置管理器...'.cyan);
        await configManager.ensureInitialized();
        
        // 初始化配置管理器
        console.log('📋 加载配置文件...'.cyan);
        await configManager.init();
        
        // 获取服务器配置
        const serverConfig = await configManager.getServerConfig();
        const clientConfig = await configManager.getClientConfig();
        const apiConfig = await configManager.getApiConfig();
        
        console.log(`✅ 配置加载成功: ${serverConfig.host}:${serverConfig.port}`.green);
        
        // 端口优先级：环境变量 > 配置文件 > 默认值
        const PORT = process.env.PORT || serverConfig.port;
        const HOST = process.env.HOST || serverConfig.host;
        
        console.log(`✅ 配置加载成功: ${HOST}:${PORT}`.green);
        console.log(`📋 前端端口: ${clientConfig.port}, API地址: ${clientConfig.apiUrl}`.gray);
        
        // 初始化MySQL数据库
        console.log('\n📊 开始MySQL数据库初始化...'.cyan.bold);
        let dbInitialized = false;

        try {
            const dbInit = new MySQLDatabaseInit();
            const isInit = await dbInit.isInitialized();

            if (!isInit) {
                console.log('🔧 数据库未初始化，开始初始化...'.yellow);
                await dbInit.init();
            } else {
                console.log('✅ 数据库已初始化'.green);
            }

            // 使用数据模型实例
            const dataModel = mysqlModel;

            // 启动数据库清理任务
            console.log('🧹 启动数据库清理任务...'.cyan);
            const databaseCleaner = new DatabaseCleaner(dataModel);
            databaseCleaner.start();

            dbInitialized = true;
        } catch (error) {
            console.error('⚠️  MySQL数据库初始化失败:'.red, error.message);
            console.log('🔄 服务器将在无数据库模式下启动'.yellow);
            dbInitialized = false;
        }

        if (dbInitialized) {
            console.log('✅ MySQL数据库初始化完成'.green);

            // 启动WebSocket风控服务
            console.log('\n🔗 启动WebSocket风控服务...'.cyan);
            const config = require('../database/config.json');
            if (config.websocket && config.websocket.enabled) {
            try {
                await webSocketService.start();
                console.log('✅ WebSocket风控服务启动成功'.green);
            } catch (error) {
                console.error('⚠️  WebSocket服务启动失败:'.yellow, error.message);
                }
            } else {
                console.log('WebSocket服务端未启用（配置关闭）');
            }

            // 初始化优化的签名服务
            console.log('\n🎯 启动优化签名服务...'.cyan);
            try {
                await signatureServiceManager.initialize(mysqlModel, {
                    maxConcurrency: 100,
                    maxQueueSize: 2000,
                    requestTimeout: 30000,
                    retryAttempts: 3,
                    retryDelay: 1000,
                    healthCheckInterval: 30000,
                    nodeTimeout: 15000,
                    maxConnectionsPerNode: 20,
                    loadBalanceStrategy: 'least_connections'
                });
                console.log('✅ 优化签名服务启动成功'.green);
            } catch (error) {
                console.error('⚠️  优化签名服务启动失败:'.yellow, error.message);
            }
        } else {
            console.log('⚠️  数据库未初始化，部分功能可能不可用'.yellow);
        }
        
        // 启动HTTP服务器
        const server = app.listen(PORT, HOST, () => {
            // 美化的启动信息
            console.log('\n');
            console.log('╔══════════════════════════════════════════════════════════╗'.rainbow);
            console.log('║                                                          ║'.rainbow);
            console.log('║            🚀 签名算法服务端启动成功! 🚀                   ║'.green.bold);
            console.log('║                                                          ║'.rainbow);
            console.log('╚══════════════════════════════════════════════════════════╝'.rainbow);
            
            console.log('\n📋 服务信息:'.cyan.bold);
            console.log(`   🌐 后端服务: http://${HOST}:${PORT}`.cyan);
            console.log(`   🖥️  前端地址: http://localhost:${clientConfig.port}`.cyan);
            console.log(`   📡 API接口: ${clientConfig.apiUrl}`.cyan);
            console.log(`   🏥 健康检查: http://${HOST}:${PORT}/health`.cyan);
            
            console.log('\n🔗 签名服务配置:'.yellow.bold);
            // 由于这里不是async函数，我们获取已配置的API信息
            try {
                configManager.getAllBaseUrls().then(allApiConfigs => {
                    if (allApiConfigs && allApiConfigs.length > 0) {
                        allApiConfigs.forEach(config => {
                            console.log(`   ${config.client_type.padEnd(8)}: ${config.base_url}`.yellow);
                        });
                    } else {
                        console.log('   暂无签名服务配置'.gray);
                    }
                }).catch(() => {
                    console.log('   配置获取失败'.gray);
                });
            } catch (error) {
                console.log('   配置获取异常'.gray);
            }
            
            console.log('\n📊 系统状态:'.green.bold);
            console.log(`   🕒 启动时间: ${new Date().toLocaleString('zh-CN')}`.green);
            console.log(`   ⚙️  配置文件: ${path.join(__dirname, '../database/config.json')}`.green);
            console.log(`   🔑 认证密钥: ${apiConfig.defaultAuthKey}`.green);
            console.log(`   ⏱️  超时设置: ${apiConfig.timeout}ms`.green);
            console.log(`   🗄️  数据库: ${dbInitialized ? '✅ MySQL' : '❌ 未连接'}`.green);
            console.log(`   📝 签名日志: ✅ MySQL存储 (自动清理旧记录)`.green);
            
            console.log('\n🔧 可用命令:'.magenta.bold);
            console.log('   📝 查看日志: tail -f logs/app.log'.magenta);
            console.log('   🔄 重启服务: pm2 restart app'.magenta);
            console.log('   🛑 停止服务: Ctrl+C'.magenta);
            
            console.log('\n🎯 快速访问:'.blue.bold);
            console.log(`   📊 管理控制台: http://localhost:${clientConfig.port}`.blue);
            console.log(`   📖 API文档: http://${HOST}:${PORT}/docs`.blue);
            console.log(`   ❤️  健康检查: http://${HOST}:${PORT}/health`.blue);
            
            console.log('\n' + '═'.repeat(60).rainbow);
            console.log('🌟 服务器已准备就绪，等待连接... 🌟'.rainbow.bold);
            console.log('═'.repeat(60).rainbow);
            console.log('\n');
        });
        
        // 保存服务器实例供外部访问
        global.qsignServer = server;
        
        // 错误处理
        server.on('error', (error) => {
            if (error.code === 'EADDRINUSE') {
                console.error(`\n❌ 端口 ${PORT} 已被占用`.red.bold);
                console.log('\n💡 解决方案:'.yellow.bold);
                console.log(`   1. 更改端口: 修改配置文件中的server.port'.white`);
                console.log(`   2. 结束占用进程: netstat -ano | findstr ${PORT}`.white);
                console.log(`   3. 或使用其他可用端口`.white);
                process.exit(1);
            } else {
                console.error('\n❌ 服务器启动错误:'.red.bold, error.message);
                process.exit(1);
            }
        });
        
    } catch (error) {
        global.qsignServerStarted = false;
        
        console.log('\n');
        console.log('╔══════════════════════════════════════════════════════════╗'.red);
        console.log('║                    ❌ 启动失败 ❌                         ║'.red.bold);
        console.log('╚══════════════════════════════════════════════════════════╝'.red);
        console.error('错误详情:'.red.bold, error.message);
        console.log('\n💡 解决建议:'.yellow.bold);
        console.log('   1. 检查端口是否被占用'.yellow);
        console.log('   2. 检查配置文件是否正确'.yellow);
        console.log('   3. 检查网络连接'.yellow);
        console.log('   4. 检查SQLite服务是否运行'.yellow);
        console.log('   5. 重新运行配置: node start.js'.yellow);
        console.log('\n');
        process.exit(1);
    }
}

// 只有在直接运行此文件时才启动服务器
if (require.main === module) {
    console.log('🔧 直接运行app.js，开始启动服务器...'.cyan);
    startServer();
} else {
    console.log('📦 app.js被其他模块引用，导出app实例'.gray);
}

// 导出startServer函数供外部调用
module.exports = { app, startServer };
