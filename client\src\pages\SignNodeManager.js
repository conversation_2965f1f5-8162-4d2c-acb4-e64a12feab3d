import React, { useState, useEffect } from 'react';
import { Table, Button, Form, Input, Modal, Space, Popconfirm, message, Tag, Tooltip, Select } from 'antd';
import { PlusOutlined, ReloadOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import api from '../services/api';

const { Option } = Select;

const SignNodeManager = () => {
  const [signNodes, setSignNodes] = useState([]);
  const [signStatus, setSignStatus] = useState([]);
  const [signLoading, setSignLoading] = useState(false);
  const [signModalVisible, setSignModalVisible] = useState(false);
  const [signEditingNode, setSignEditingNode] = useState(null);
  const [form] = Form.useForm();

  const fetchNodes = async () => {
    setSignLoading(true);
    try {
      const res = await api.get('/sign-nodes/nodes');
      setSignNodes(res.data.data || []);
    } finally {
      setSignLoading(false);
    }
  };

  const fetchStatus = async () => {
    const res = await api.get('/sign-nodes/nodes/status');
    setSignStatus(res.data.data || []);
  };

  useEffect(() => {
    fetchNodes();
    fetchStatus();
  }, []);

  const handleEdit = (node) => {
    setSignEditingNode(node);
    setSignModalVisible(true);
    form.setFieldsValue({
      client_type: node.client_type,
      base_url: node.base_url
    });
  };

  const handleDelete = async (client_type) => {
    try {
      const response = await api.delete(`/sign-nodes/nodes/${client_type}`);

      if (response.data.success || response.data.code === 0) {
        message.success('删除成功');
        fetchNodes();
        fetchStatus();
      } else {
        message.error(response.data.message || response.data.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除节点失败:', error);
      const errorMsg = error.response?.data?.message ||
                      error.response?.data?.msg ||
                      error.message ||
                      '删除失败';
      message.error(errorMsg);
    }
  };

  const handleAdd = () => {
    setSignEditingNode(null);
    setSignModalVisible(true);
    form.resetFields();
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      if (signEditingNode) {
        // 更新现有节点
        await api.put(`/sign-nodes/nodes/${signEditingNode.client_type}`, {
          base_url: values.base_url
        });
        message.success('更新成功');
      } else {
        // 创建新节点
        const response = await api.post('/sign-nodes/nodes', {
          client_type: values.client_type,
          base_url: values.base_url,
          node_name: values.node_name
        });

        if (response.data.code === 0) {
          message.success('创建成功');
          if (response.data.data?.nodeIdentifier) {
            message.info(`节点标识: ${response.data.data.nodeIdentifier}`, 3);
          }
        } else {
          message.error(response.data.msg || '创建失败');
          return;
        }
      }
      setSignModalVisible(false);
      fetchNodes();
      fetchStatus();
    } catch (error) {
      const errorMsg = error.response?.data?.msg || (signEditingNode ? '更新失败' : '创建失败');
      message.error(errorMsg);
    }
  };



  const columns = [
    {
      title: '节点标识',
      dataIndex: 'client_type',
      key: 'client_type',
      render: (text) => {
        // 解析节点标识，显示类型和名称
        const parts = text.split('_');
        const type = parts[0];
        const name = parts.slice(1).join('_');

        const typeDisplayName = {
          'QQ': 'QQ',
          'TIM': 'TIM',
          'QQlite': 'QQ轻聊版',
          '企点QQ': '企点QQ'
        }[type] || type;

        return (
          <div>
            <Tag color="blue">{typeDisplayName}</Tag>
            {name && <span style={{ color: '#666', fontSize: '12px' }}>({name})</span>}
          </div>
        );
      }
    },
    {
      title: '节点地址',
      dataIndex: 'base_url',
      key: 'base_url',
      render: (text) => text || <span style={{ color: '#999' }}>未配置</span>
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      render: (text) => text || <span style={{ color: '#999' }}>无</span>
    },
    {
      title: 'Token状态',
      key: 'token_status',
      render: (_, record) => {
        const hasToken = record.auth_key && record.auth_key.length === 128;
        return (
          <Tag color={hasToken ? 'green' : 'orange'}>
            {hasToken ? '已配置Token' : '未配置Token'}
          </Tag>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="编辑节点">
            <Button type="text" icon={<EditOutlined />} onClick={() => handleEdit(record)} />
          </Tooltip>
          <Popconfirm
            title="确定删除此节点吗?"
            description={record.auth_key && record.auth_key.length === 128 ? "该节点已配置Token，删除前请先在Token管理中删除相关Token" : undefined}
            onConfirm={() => handleDelete(record.client_type)}
          >
            <Button type="text" danger icon={<DeleteOutlined />} />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2 style={{ margin: 0 }}>签名服务节点管理</h2>
        <Space>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            新增节点
          </Button>
          <Button icon={<ReloadOutlined />} onClick={() => { fetchNodes(); fetchStatus(); }}>
            刷新
          </Button>
        </Space>
      </div>
      <Table
        columns={columns}
        dataSource={signNodes}
        rowKey="client_type"
        loading={signLoading}
        pagination={false}
      />
      <Modal
        open={signModalVisible}
        onCancel={() => setSignModalVisible(false)}
        onOk={handleSave}
        title={signEditingNode ? '编辑节点' : '新增节点'}
        width={500}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="client_type"
            label="节点类型"
            rules={[{ required: true, message: '请选择节点类型' }]}
          >
            <Select
              placeholder="请选择节点类型"
              disabled={!!signEditingNode}
            >
              <Option value="QQ">QQ</Option>
              <Option value="TIM">TIM</Option>
              <Option value="QQlite">QQ轻聊版</Option>
              <Option value="企点QQ">企点QQ</Option>
            </Select>
          </Form.Item>

          {!signEditingNode && (
            <Form.Item
              name="node_name"
              label="节点名称"
              rules={[
                { pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, message: '节点名称只能包含字母、数字、下划线和中文' }
              ]}
            >
              <Input
                placeholder="可选，如：Production、Test、Node1 等"
                maxLength={20}
              />
            </Form.Item>
          )}

          <Form.Item
            name="base_url"
            label="节点地址"
            rules={[
              { required: true, message: '请输入节点地址' },
              { type: 'url', message: '请输入有效的URL地址' }
            ]}
          >
            <Input placeholder="http://127.0.0.1:9511" />
          </Form.Item>
          <div style={{ color: '#666', fontSize: '12px', marginTop: 8 }}>
            <p>• 其他配置（超时时间、重试次数、Token等）请在Token管理页面中配置</p>
            <p>• 可以创建多个相同类型的节点，通过节点名称区分（如：Production、Test等）</p>
            <p>• 节点名称为可选，不填写将自动生成时间戳标识</p>
            <p>• 节点地址格式：http://ip:port 或 https://domain:port</p>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default SignNodeManager; 