/**
 * WebSocket 客户端服务
 * 用于实时风控通信和监控
 */
class WebSocketService {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 5000;
    this.heartbeatInterval = null;
    this.listeners = new Map();
    this.authKey = null;
  }

  /**
   * 连接到 WebSocket 服务器
   */  connect(authKey = null) {
    if (this.ws && this.isConnected) {
      console.log('WebSocket 已连接');
      return Promise.resolve();
    }

    this.authKey = authKey || localStorage.getItem('token') || 'default_key';
    
    return new Promise((resolve, reject) => {
      try {
        // 从localStorage获取服务器配置
        const serverHost = localStorage.getItem('server_host') || 'localhost';
        const serverPort = localStorage.getItem('server_port') || '12041';
        
        // WebSocket默认端口通常是HTTP端口+1，或者使用固定的12042
        const wsPort = parseInt(serverPort) + 1 || 12042;
        const wsUrl = `ws://${serverHost}:${wsPort}`;
        
        console.log(`正在连接 WebSocket: ${wsUrl}`);
        
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
          console.log('WebSocket 连接已建立');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          // 发送认证信息
          this.send('auth', {
            auth_key: this.authKey,
            client_type: 'admin_panel',
            user_agent: navigator.userAgent
          });
          
          // 启动心跳
          this.startHeartbeat();
          
          this.emit('connected');
          resolve();
        };
        
        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
          } catch (error) {
            console.error('解析 WebSocket 消息失败:', error);
          }
        };
        
        this.ws.onclose = (event) => {
          console.log('WebSocket 连接已关闭:', event.code, event.reason);
          this.isConnected = false;
          this.stopHeartbeat();
          this.emit('disconnected', { code: event.code, reason: event.reason });
          
          // 自动重连
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
          }
        };
        
        this.ws.onerror = (error) => {
          console.error('WebSocket 错误:', error);
          this.emit('error', error);
          reject(error);
        };
        
      } catch (error) {
        console.error('创建 WebSocket 连接失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.ws) {
      this.stopHeartbeat();
      this.ws.close();
      this.ws = null;
      this.isConnected = false;
    }
  }

  /**
   * 发送消息
   */
  send(type, data = {}) {
    if (!this.isConnected || !this.ws) {
      console.warn('WebSocket 未连接，无法发送消息');
      return false;
    }

    try {
      const message = {
        type,
        data,
        timestamp: Date.now()
      };
      
      this.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error('发送 WebSocket 消息失败:', error);
      return false;
    }
  }

  /**
   * 处理接收到的消息
   */
  handleMessage(message) {
    const { type, data, status } = message;
    
    switch (type) {
      case 'auth_result':
        if (status === 'success') {
          console.log('WebSocket 认证成功');
          this.emit('authenticated', data);
        } else {
          console.error('WebSocket 认证失败:', data.message);
          this.emit('auth_failed', data);
        }
        break;
        
      case 'risk_detection':
        console.log('收到风控检测结果:', data);
        this.emit('risk_detection', data);
        break;
        
      case 'client_connected':
        console.log('新客户端连接:', data);
        this.emit('client_connected', data);
        break;
        
      case 'client_disconnected':
        console.log('客户端断开:', data);
        this.emit('client_disconnected', data);
        break;
        
      case 'pong':
        // 心跳响应
        break;
        
      default:
        console.log('收到未知类型消息:', type, data);
        this.emit('message', { type, data });
        break;
    }
  }

  /**
   * 启动心跳
   */
  startHeartbeat() {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send('ping');
      }
    }, 30000); // 每30秒发送心跳
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * 安排重连
   */
  scheduleReconnect() {
    this.reconnectAttempts++;
    console.log(`准备重连 WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
    
    setTimeout(() => {
      if (!this.isConnected) {
        this.connect(this.authKey).catch(() => {
          // 重连失败，继续尝试
        });
      }
    }, this.reconnectInterval);
  }

  /**
   * 风控内容检测
   */
  async detectRisk(content, clientInfo = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isConnected) {
        reject(new Error('WebSocket 未连接'));
        return;
      }

      const requestId = Date.now().toString();
      
      // 监听响应
      const timeout = setTimeout(() => {
        this.off(`risk_result_${requestId}`);
        reject(new Error('检测超时'));
      }, 10000);

      this.once(`risk_result_${requestId}`, (result) => {
        clearTimeout(timeout);
        resolve(result);
      });

      // 发送检测请求
      this.send('detect_risk', {
        request_id: requestId,
        content,
        client_info: {
          ip: clientInfo.ip || 'unknown',
          user_agent: clientInfo.user_agent || navigator.userAgent,
          ...clientInfo
        }
      });
    });
  }

  /**
   * 获取连接的客户端列表
   */
  getConnectedClients() {
    return new Promise((resolve, reject) => {
      if (!this.isConnected) {
        reject(new Error('WebSocket 未连接'));
        return;
      }

      const requestId = Date.now().toString();
      
      const timeout = setTimeout(() => {
        this.off(`clients_list_${requestId}`);
        reject(new Error('获取客户端列表超时'));
      }, 5000);

      this.once(`clients_list_${requestId}`, (result) => {
        clearTimeout(timeout);
        resolve(result);
      });

      this.send('get_clients', { request_id: requestId });
    });
  }

  /**
   * 事件监听
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event).push(callback);
  }

  /**
   * 单次事件监听
   */
  once(event, callback) {
    const wrapper = (...args) => {
      this.off(event, wrapper);
      callback(...args);
    };
    this.on(event, wrapper);
  }

  /**
   * 移除事件监听
   */
  off(event, callback = null) {
    if (!this.listeners.has(event)) return;
    
    if (callback) {
      const callbacks = this.listeners.get(event);
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
      }
    } else {
      this.listeners.delete(event);
    }
  }

  /**
   * 触发事件
   */
  emit(event, ...args) {
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach(callback => {
        try {
          callback(...args);
        } catch (error) {
          console.error('事件回调执行错误:', error);
        }
      });
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      maxReconnectAttempts: this.maxReconnectAttempts
    };
  }
}

// 创建单例实例
const webSocketService = new WebSocketService();

export default webSocketService;
