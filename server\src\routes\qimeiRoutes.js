const express = require('express');
const crypto = require('crypto');
const axios = require('axios');
const router = express.Router();

// 生成随机密钥 - 与Python版本完全一致
function getRandomKey() {
    const seed = Math.floor(Date.now() / 1000);
    // 使用种子初始化随机数生成器
    let random = (seed * 1103515245 + 12345) % 2147483647;
    if (random < 1) random = 1;
    if (random > 2147473647) random = 2147473647;
    
    const md5Hash = crypto.createHash('md5').update((random + 1000 + 2000 + seed).toString()).digest('hex');
    return md5Hash.substring(8, 24);
}

// RSA加密 - 与Python版本完全一致
function rsaEncrypt(strToEncrypt) {
    try {
        const NodeRSA = require('node-rsa');
        const publicKeyStr = `-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDEIxgwoutfwoJxcGQeedgP7FG9qaIuS0qzfR8gWkrkTZKM2iWHn2ajQpBRZjMSoSf6+KJGvar2ORhBfpDXyVtZCKpqLQ+FLkpncClKVIrBwv6PHyUvuCb0rIarmgDnzkfQAqVufEtR64iazGDKatvJ9y6B9NMbHddGSAUmRTCrHQIDAQAB
-----END PUBLIC KEY-----`;
        
        const key = new NodeRSA();
        key.importKey(publicKeyStr, 'public');
        // 设置加密选项以匹配Python的PKCS1_v1_5
        key.setOptions({
            encryptionScheme: 'pkcs1'
        });
        const encrypted = key.encrypt(strToEncrypt, 'base64');
        return encrypted;
    } catch (error) {
        console.error('RSA加密失败:', error);
        throw error;
    }
}

// 获取时间戳 - 与Python版本完全一致
function getTime() {
    return Math.round(Date.now()).toString();
}

// AES加密/解密 - 与Python版本完全一致
function getParams(data, key, isDecrypt = false) {
    const algorithm = 'aes-128-cbc';
    const keyBuffer = Buffer.from(key, 'utf8').subarray(0, 16); // 确保密钥长度为16字节
    const iv = keyBuffer; // 使用key作为IV
    
    if (isDecrypt) {
        try {
            const decipher = crypto.createDecipheriv(algorithm, keyBuffer, iv);
            decipher.setAutoPadding(false); // 禁用自动padding
            let decrypted = decipher.update(data, 'base64', 'utf8');
            decrypted += decipher.final('utf8');
            // 手动移除PKCS7 padding
            return decrypted.replace(/\0+$/, '').replace(/[\x01-\x10]+$/, '');
        } catch (error) {
            console.error('AES解密失败:', error);
            throw error;
        }
    } else {
        try {
            // 手动实现PKCS7 padding，与Python AES库一致
            const blockSize = 16;
            const padding = blockSize - (Buffer.byteLength(data, 'utf8') % blockSize);
            const paddedData = data + String.fromCharCode(padding).repeat(padding);
            
            const cipher = crypto.createCipheriv(algorithm, keyBuffer, iv);
            cipher.setAutoPadding(false); // 禁用自动padding，使用手动padding
            let encrypted = cipher.update(paddedData, 'utf8', 'base64');
            encrypted += cipher.final('base64');
            return encrypted;
        } catch (error) {
            console.error('AES加密失败:', error);
            throw error;
        }
    }
}

// 发送POST请求
async function curlPost(url, data, timeout = 5000) {    const headers = {
        'User-Agent': 'Dalvik/2.1.0 (Linux; U; Android 14; 24071PN0DG Build/UKQ1.231003.002)',
        'Content-Type': 'application/json'
    };
    
    try {
        const response = await axios.post(url, data, {
            headers,
            timeout,
            httpsAgent: new (require('https').Agent)({ rejectUnauthorized: false }),
            maxRedirects: 5,
            retry: 3,
            retryDelay: 1000
        });        return response.data;
    } catch (error) {
        console.error('HTTP请求失败:', error.message);
        throw new Error(`Request error: ${error.message}`);
    }
}

// 获取qimei结果
async function getImei(k, key, params, timeVal, nonce, sign) {
    const url = 'https://snowflake.qq.com/ola/android';
    const jsonArray = {
        key: key,
        params: params,
        time: parseInt(timeVal),
        nonce: nonce,
        sign: sign,
        extra: ''    };
    
    try {
        const result = await curlPost(url, JSON.stringify(jsonArray), 10000);
        
        if (result && result.code === 0) {
            const decryptedResult = getParams(result.data, k, true);
            return decryptedResult;
        } else {
            throw new Error(`获取结果失败,服务器返回错误代码:${result ? result.code : 'unknown'}`);
        }
    } catch (error) {
        throw new Error(`获取结果失败: ${error.message}`);
    }
}

// 默认参数模板 - 2025年最新设备信息
const defaultParams = {
    "androidId": "",
    "platformId": 1,
    "appKey": "0S200MNJT807V3GE",
    "appVersion": "9.1.15.4500",
    "beaconIdSrc": "k1:2025-06-09005633.150000000;k2:2024-01-15185536.244626911;k3:0000000000000000;k4:9d938f6e3829cfab;k5:4096204;k6:1024896;k7:698752;k8:262144;k9:a42e8c9f-58bf-4dfe-91e5-1a99f8872f9f;k11:0;k12:5;k13:2025-06-09005632.610000000;k14:2025-06-09000159.756000000;k15:1;k16:16;k17:2025-06-09005702.920000000;k18:2025-06-09005634.610000000;k19:32768;k20:128;k21:2025-06-09005629.120000000;k22:2024-01-15185447.459986909;k23:12800;k24:3;k25:2025-06-09005612.710000000;k26:2025-06-09005612.720000000;k27:8192;k28:12;k29:2025-06-09005612.720000000;k30:2025-06-09005612.720000000;k31:8320;k32:1;k33:2025-06-09005709.000000000;k34:2025-06-09005716.370000000;k35:4;k36:64;k37:2025-06-09002617.121000000;k38:2025-06-09000200.276000000;k39:3;k40:4;k10:1",
    "brand": "Xiaomi",
    "channelId": "2017",
    "cid": "",
    "imei": "",
    "imsi": "",
    "mac": "",
    "model": "24071PN0DG",
    "networkType": "wifi",
    "oaid": "",
    "osVersion": "Android 14,level 34",
    "qimei": "",
    "qimei36": "",
    "sdkVersion": "1.3.8.2",
    "targetSdkVersion": "34",
    "audit": "",
    "userId": "{}",
    "packageId": "com.tencent.mobileqq",
    "deviceType": "Phone",
    "sdkName": "",
    "reserved": "{\"harmony\":\"0\",\"clone\":\"0\",\"containe\":\"\",\"oz\":\"VjZnfmxwpuB+W3oPcPwMUhO3\\/n9kxHC+zVC6w0uzsSh=\",\"oo\":\"YfdkuF0T2/g9Qz3WMTyhrx==\",\"kelong\":\"0\",\"uptimes\":\"2025-06-09 10:30:15\",\"multiUser\":\"0\",\"bod\":\"SM8650-AC-\",\"brd\":\"Xiaomi\",\"dv\":\"24071PN0DG\",\"firstLevel\":\"\",\"manufact\":\"Xiaomi\",\"name\":\"24071PN0DG\",\"host\":\"xiaomi.eu\",\"kernel\":\"Linux localhost 5.15.137-android14-11-g92b6e8ab96e7-ab #1 SMP PREEMPT Mon May 13 10:30:42 UTC 2024 aarch64\"}"
};

// POST /generate - 生成qimei
router.post('/generate', async (req, res) => {
    try {
        const { params: customParams } = req.body;
        const maxRetries = 2; // 最多重试次数
        
        // 使用自定义参数或默认参数
        let paramsToUse = defaultParams;
        if (customParams) {
            // 如果提供了自定义参数，合并到默认参数中
            paramsToUse = { ...defaultParams, ...customParams };
        }
        
        let success = false;
        let retryCount = 0;
        let qimeiData = null;
        let lastError = null;
          // 尝试生成QIMEI，失败后重试
        while (!success && retryCount <= maxRetries) {
            try {
                // 生成所需参数
                const k = getRandomKey();
                const key = rsaEncrypt(k);
                const timeVal = getTime();
                const nonce = getRandomKey();                const params = getParams(JSON.stringify(paramsToUse), k, false);
                const sign = crypto.createHash('md5').update(key + params + timeVal + nonce + 'ZdJqM15EeO2zWc08').digest('hex');
                
                // 获取qimei结果
                const result = await getImei(k, key, params, timeVal, nonce, sign);
                qimeiData = JSON.parse(result);
                success = true;
            } catch (error) {
                lastError = error;
                retryCount++;
                
                // 如果不是最后一次重试，等待一段时间后再次尝试
                if (retryCount <= maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, 1500));
                }
            }
        }
          if (success && qimeiData) {
            res.json({
                success: true,
                data: {
                    qimei: qimeiData.q16 || '',
                    qimei36: qimeiData.q36 || '',
                    raw: qimeiData
                },
                timestamp: new Date().toISOString()
            });
        } else {
            throw new Error(lastError ? lastError.message : '生成QIMEI失败，请稍后重试');
        }
          } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

// GET /template - 获取参数模板
router.get('/template', (req, res) => {
    res.json({
        success: true,
        data: defaultParams,
        description: "qimei生成参数模板，可以修改这些参数来生成不同的qimei",
        timestamp: new Date().toISOString()
    });
});

// POST /batch - 批量生成qimei
router.post('/batch', async (req, res) => {
    try {
        const { count = 1, params: customParams } = req.body;
        
        if (count > 10) {
            return res.status(400).json({
                success: false,
                error: '批量生成数量不能超过10个',
                timestamp: new Date().toISOString()
            });
        }
        
        const results = [];
        const maxRetries = 2; // 每个QIMEI最多重试次数
        
        for (let i = 0; i < count; i++) {
            let success = false;
            let retryCount = 0;
            let lastError = null;
            
            // 尝试生成QIMEI，失败后重试
            while (!success && retryCount <= maxRetries) {
                try {
                    // 使用自定义参数或默认参数
                    let paramsToUse = defaultParams;
                    if (customParams) {
                        paramsToUse = { ...defaultParams, ...customParams };
                    }
                    
                    const k = getRandomKey();
                    const key = rsaEncrypt(k);
                    const timeVal = getTime();
                    const nonce = getRandomKey();
                    const params = getParams(JSON.stringify(paramsToUse), k, false);
                    const sign = crypto.createHash('md5').update(key + params + timeVal + nonce + 'ZdJqM15EeO2zWc08').digest('hex');
                    
                    const result = await getImei(k, key, params, timeVal, nonce, sign);
                    const qimeiData = JSON.parse(result);
                      results.push({
                        index: i + 1,
                        qimei: qimeiData.q16 || '',
                        qimei36: qimeiData.q36 || '',
                        raw: qimeiData
                    });
                    
                    success = true;
                } catch (error) {
                    lastError = error;                    retryCount++;
                    // 如果不是最后一次重试，等待一段时间后再次尝试
                    if (retryCount <= maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, 1500));
                    }
                }
            }
            
            // 如果所有重试都失败了，记录错误
            if (!success) {
                results.push({
                    index: i + 1,
                    error: lastError ? lastError.message : '未知错误'
                });
            }
            
            // 添加延迟避免请求过快
            if (i < count - 1) {
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        res.json({
            success: true,
            data: results,
            total: count,
            timestamp: new Date().toISOString()
        });        
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
});

module.exports = router;