const express = require('express');
const mysqlModel = require('../models/mysql-real-model');
const { formatTimestamp } = require('../utils/helpers');

const router = express.Router();

// 获取签名日志
router.get('/logs/sign', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;        // 过滤条件
        const filters = {};
        const { ip, uin, type, success, startDate, endDate } = req.query;
        
        if (ip && ip.trim()) filters.ip = ip.trim();
        if (uin && uin.trim()) filters.uin = uin.trim();
        if (type && type.trim()) filters.type = type.trim();
        if (success !== undefined && success !== '' && success !== null) {
            filters.success = success === 'true' || success === true;
        }
        if (startDate && startDate.trim()) filters.startDate = startDate.trim();
        if (endDate && endDate.trim()) filters.endDate = endDate.trim();

        const result = await mysqlModel.getSignLogs(filters, { page, limit });

        // 格式化时间
        const formattedLogs = result.logs.map(log => ({
            ...log,
            timestamp: formatTimestamp(log.request_time),
            success: Boolean(log.success)
        }));

        res.json({
            code: 0,
            msg: '查询成功',
            data: {
                logs: formattedLogs,
                pagination: result.pagination
            }
        });
    } catch (error) {
        console.error('获取签名日志失败:', error);
        return res.status(500).json({ 
            code: 500, 
            msg: '查询失败', 
            error: error.message 
        });
    }
});

// 获取日志详情
router.get('/logs/sign/:id', async (req, res) => {
    try {
        const id = parseInt(req.params.id);
        const log = await mysqlModel.getSignLogById(id);
        
        if (!log) {
            return res.status(404).json({ code: 404, msg: '日志不存在' });
        }
        
        const formattedLog = {
            ...log,
            timestamp: formatTimestamp(log.request_time),
            success: Boolean(log.success)
        };
        
        res.json({
            code: 0,
            msg: '查询成功',
            data: formattedLog
        });
    } catch (error) {
        console.error('获取日志详情失败:', error);
        return res.status(500).json({ 
            code: 500, 
            msg: '查询失败', 
            error: error.message 
        });
    }
});

// 获取未授权访问日志
router.get('/logs/unauthorized', async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        
        const filters = {};
        const { ip, startDate, endDate } = req.query;
        
        if (ip) filters.ip = ip;
        if (startDate) filters.startDate = startDate;
        if (endDate) filters.endDate = endDate;

        const result = await mysqlModel.getUnauthorizedLogs(filters, { page, limit });

        // 格式化时间
        const formattedLogs = result.logs.map(log => ({
            ...log,
            timestamp: formatTimestamp(log.attempt_time)
        }));

        res.json({
            code: 0,
            msg: '查询成功',
            data: {
                logs: formattedLogs,
                pagination: result.pagination
            }
        });
    } catch (error) {
        console.error('获取未授权访问日志失败:', error);
        return res.status(500).json({ 
            code: 500, 
            msg: '查询失败', 
            error: error.message 
        });
    }
});

// 清理旧日志
router.delete('/logs/clean', async (req, res) => {
    try {
        const days = parseInt(req.query.days) || 30;
        
        const result = await mysqlModel.cleanOldLogs(days);
        
        res.json({
            code: 0,
            msg: '清理成功',
            data: {
                signLogsDeleted: result.signLogsDeleted,
                unauthorizedLogsDeleted: result.unauthorizedLogsDeleted,
                days: days
            }
        });
    } catch (error) {
        console.error('清理日志失败:', error);
        return res.status(500).json({ 
            code: 500, 
            msg: '清理失败', 
            error: error.message 
        });
    }
});

module.exports = router;
