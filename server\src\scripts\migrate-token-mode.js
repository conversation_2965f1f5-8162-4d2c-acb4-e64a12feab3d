const fs = require('fs');
const path = require('path');
const colors = require('colors');
const mysqlModel = require('../models/mysql-real-model');

/**
 * 数据库迁移脚本：添加token_mode字段支持
 */
class TokenModeMigration {
    constructor() {
        this.mysqlModel = mysqlModel;
    }

    async migrate() {
        try {
            console.log('🔄 开始执行token模式迁移...'.cyan);

            // 检查字段是否已存在
            const columnExists = await this.checkColumnExists();

            if (columnExists) {
                console.log('⚠️  token_mode字段已存在，跳过迁移'.yellow);
                await this.verifyMigration();
                return;
            }

            // 手动执行迁移步骤
            await this.addTokenModeColumn();
            await this.addTokenModeIndex();
            await this.updateExistingRecords();

            // 验证迁移结果
            await this.verifyMigration();

            console.log('✅ token模式迁移完成！'.green.bold);

        } catch (error) {
            console.error('❌ 迁移失败:'.red.bold, error.message);
            throw error;
        }
    }

    async checkColumnExists() {
        try {
            const checkColumnSql = `
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE()
                  AND TABLE_NAME = 'api_configs'
                  AND COLUMN_NAME = 'token_mode'
            `;

            const result = await this.mysqlModel.db.query(checkColumnSql);
            return result.length > 0;
        } catch (error) {
            console.error('检查字段存在性失败:', error.message);
            return false;
        }
    }

    async addTokenModeColumn() {
        try {
            console.log('📝 添加token_mode字段...'.gray);
            const sql = `
                ALTER TABLE api_configs
                ADD COLUMN token_mode ENUM('exclusive', 'shared') DEFAULT 'exclusive'
                COMMENT 'Token模式：exclusive=独享，shared=共享'
                AFTER auth_key
            `;
            await this.mysqlModel.db.run(sql);
            console.log('✅ token_mode字段添加成功'.green);
        } catch (error) {
            if (error.message.includes('Duplicate column name')) {
                console.log('⚠️  token_mode字段已存在'.yellow);
            } else {
                throw error;
            }
        }
    }

    async addTokenModeIndex() {
        try {
            console.log('📝 添加token_mode索引...'.gray);
            const sql = `ALTER TABLE api_configs ADD INDEX idx_token_mode (token_mode)`;
            await this.mysqlModel.db.run(sql);
            console.log('✅ token_mode索引添加成功'.green);
        } catch (error) {
            if (error.message.includes('Duplicate key name')) {
                console.log('⚠️  token_mode索引已存在'.yellow);
            } else {
                throw error;
            }
        }
    }

    async updateExistingRecords() {
        try {
            console.log('📝 更新现有记录的token模式...'.gray);

            // 设置共享模式（多个节点使用相同token）
            const sharedSql = `
                UPDATE api_configs a1
                SET token_mode = 'shared'
                WHERE a1.auth_key IS NOT NULL
                  AND a1.auth_key != ''
                  AND LENGTH(a1.auth_key) = 128
                  AND EXISTS (
                    SELECT 1 FROM api_configs a2
                    WHERE a2.auth_key = a1.auth_key
                      AND a2.id != a1.id
                  )
            `;
            const sharedResult = await this.mysqlModel.db.run(sharedSql);
            console.log(`✅ 设置共享模式: ${sharedResult.affectedRows || 0}条记录`.green);

            // 确保其他记录为独享模式
            const exclusiveSql = `
                UPDATE api_configs
                SET token_mode = 'exclusive'
                WHERE token_mode IS NULL
                  OR (auth_key IS NULL OR auth_key = '' OR LENGTH(auth_key) != 128)
            `;
            const exclusiveResult = await this.mysqlModel.db.run(exclusiveSql);
            console.log(`✅ 设置独享模式: ${exclusiveResult.affectedRows || 0}条记录`.green);

        } catch (error) {
            console.error('更新记录失败:', error.message);
            throw error;
        }
    }

    async verifyMigration() {
        try {
            console.log('🔍 验证迁移结果...'.cyan);

            // 检查token_mode字段是否存在
            const checkColumnSql = `
                SELECT COLUMN_NAME 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                  AND TABLE_NAME = 'api_configs' 
                  AND COLUMN_NAME = 'token_mode'
            `;
            
            const columnResult = await this.mysqlModel.db.query(checkColumnSql);
            
            if (columnResult.length === 0) {
                throw new Error('token_mode字段未成功添加');
            }

            console.log('✅ token_mode字段验证成功'.green);

            // 检查数据统计
            const statsSql = `
                SELECT 
                    token_mode,
                    COUNT(*) as count,
                    COUNT(DISTINCT auth_key) as unique_tokens
                FROM api_configs 
                WHERE auth_key IS NOT NULL AND auth_key != ''
                GROUP BY token_mode
            `;

            const stats = await this.mysqlModel.db.query(statsSql);
            
            console.log('📊 迁移统计结果:'.cyan);
            stats.forEach(stat => {
                console.log(`   ${stat.token_mode}: ${stat.count}个节点, ${stat.unique_tokens}个唯一token`.gray);
            });

        } catch (error) {
            console.error('❌ 验证失败:'.red, error.message);
            throw error;
        }
    }

    async rollback() {
        try {
            console.log('🔄 开始回滚token模式迁移...'.yellow);

            // 删除token_mode字段
            const rollbackSql = `
                ALTER TABLE api_configs 
                DROP COLUMN IF EXISTS token_mode
            `;

            await this.mysqlModel.db.run(rollbackSql);
            console.log('✅ 回滚完成'.green);

        } catch (error) {
            console.error('❌ 回滚失败:'.red, error.message);
            throw error;
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const migration = new TokenModeMigration();
    
    const command = process.argv[2];
    
    if (command === 'rollback') {
        migration.rollback()
            .then(() => {
                console.log('回滚完成'.green);
                process.exit(0);
            })
            .catch(error => {
                console.error('回滚失败:'.red, error.message);
                process.exit(1);
            });
    } else {
        migration.migrate()
            .then(() => {
                console.log('迁移完成'.green);
                process.exit(0);
            })
            .catch(error => {
                console.error('迁移失败:'.red, error.message);
                process.exit(1);
            });
    }
}

module.exports = TokenModeMigration;
