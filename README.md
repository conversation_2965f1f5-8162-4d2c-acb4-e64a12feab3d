# 🔐 QsignHook 签名算法服务端

[![Node.js](https://img.shields.io/badge/Node.js-16%2B-green.svg)](https://nodejs.org/)
[![React](https://img.shields.io/badge/React-19.1.0-blue.svg)](https://reactjs.org/)
[![Express](https://img.shields.io/badge/Express-4.21.2-lightgrey.svg)](https://expressjs.com/)
[![MySQL](https://img.shields.io/badge/MySQL-8.0%2B-blue.svg)](https://mysql.com/)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![PNPM Workspace](https://img.shields.io/badge/PNPM-Workspace-orange.svg)](https://pnpm.io/)

一个功能完整的QQ签名算法服务端解决方案，包含高性能后端API服务器和现代化前端管理控制台。支持MySQL数据库、优化签名服务、风控系统、Token管理等企业级功能。

## ✨ 核心特性

### 🚀 **高性能签名服务**
- **优化队列管理** - 支持高并发请求处理，智能队列调度
- **负载均衡** - 多种负载均衡策略（轮询、最少连接、响应时间）
- **健康检查** - 自动节点健康监控和故障转移
- **连接池** - HTTP连接复用，提升性能

### 🎯 **多客户端支持**
- 支持QQ、TIM、企点、QQ轻聊版等多种客户端
- 智能节点选择和路由
- 完整的API接口：`/api/sign`、`/api/Get0553`、`/api/energy`

### 🛡️ **企业级安全**
- **风控系统** - 关键词过滤、黑白名单管理
- **Token管理** - 支持独立Token和共享Token
- **用户认证** - JWT认证和权限管理
- **访问控制** - IP限流和请求监控

### 📊 **实时监控**
- **签名服务监控** - 实时性能统计和节点状态
- **数据可视化** - 基于Recharts的图表展示
- **日志管理** - 详细的请求日志和错误追踪
- **系统统计** - 成功率、响应时间等关键指标

### 🗄️ **数据库支持**
- **MySQL数据库** - 企业级数据存储
- **自动初始化** - 一键创建数据库表结构
- **数据迁移** - 平滑的数据库升级

## 🏗️ 项目结构

```
QsignHookServer/
├── package.json              # 根目录配置 - 统一管理所有依赖
├── pnpm-workspace.yaml       # PNPM Workspace 配置
├── pnpm-lock.yaml           # 统一的锁定文件
├── client/                   # React 前端项目
│   ├── package.json         # 客户端配置（仅脚本）
│   ├── src/
│   │   ├── pages/           # 页面组件
│   │   │   ├── Dashboard.js          # 仪表板
│   │   │   ├── SignNodeManager.js    # 签名节点管理
│   │   │   ├── TokenManager.js       # Token管理
│   │   │   ├── SignatureMonitor.js   # 签名服务监控
│   │   │   ├── RiskControl.js        # 风控管理
│   │   │   └── UserManagement.js     # 用户管理
│   │   ├── components/      # 通用组件
│   │   └── services/        # API服务
├── server/                   # Express 后端项目
│   ├── package.json         # 服务端配置（仅脚本）
│   ├── src/
│   │   ├── routes/          # API路由
│   │   ├── services/        # 业务服务
│   │   │   ├── OptimizedSignatureService.js  # 优化签名服务
│   │   │   ├── SignatureQueue.js             # 签名队列管理
│   │   │   └── SignatureServiceManager.js    # 服务管理器
│   │   ├── middleware/      # 中间件
│   │   ├── models/          # 数据模型
│   │   └── config/          # 配置文件
│   ├── database/            # 数据库相关
│   │   ├── mysql-init.sql   # MySQL初始化脚本
│   │   └── config.json      # 数据库配置
│   └── logs/                # 日志文件
└── README.md
```

## 🚀 快速开始

### 📋 系统要求

- **Node.js** 16.0.0 或更高版本
- **MySQL** 8.0 或更高版本（推荐）或 5.7+
- **pnpm** 包管理器（推荐）或 npm

### ⚡ 安装和启动

#### 1. 一键安装所有依赖
```bash
# 克隆项目
git clone <repository-url>
cd QsignHookserver-master

# 一次性安装所有依赖（包括前端和后端）
pnpm install
```

#### 2. 配置数据库

**MySQL数据库配置（推荐）**
```bash
# 1. 创建数据库
mysql -u root -p
CREATE DATABASE qsign_hook CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 2. 配置数据库连接
# 编辑 server/database/config.json
{
  "mysql": {
    "host": "localhost",
    "port": 3306,
    "user": "root",
    "password": "your_password",
    "database": "qsign_hook"
  }
}
```

**自动初始化数据库**
```bash
# 首次启动时会自动创建表结构
pnpm run dev
```

#### 3. 启动项目

**开发模式（推荐）:**
```bash
# 同时启动前端和后端开发服务器
pnpm run dev
```

**单独启动:**
```bash
# 仅启动后端服务器
pnpm run server:dev

# 仅启动前端（需要后端运行）
pnpm run client:dev
```

**生产模式:**
```bash
# 构建前端
pnpm run client:build

# 启动后端生产服务器
pnpm run server:start
```

### 📱 访问地址

- **前端管理控制台**: http://localhost:3000
- **后端API服务**: http://localhost:12041
- **签名服务监控**: http://localhost:3000 (登录后访问)

## 🛠️ 可用脚本

| 命令 | 描述 |
|------|------|
| `pnpm install` | 一键安装所有依赖 |
| `pnpm run dev` | 开发模式启动前后端 |
| `pnpm run start:config` | 配置向导启动（推荐首次使用） |
| `pnpm run config:init` | 运行配置向导 |
| `pnpm run config:reset` | 重置配置为默认值 |
| `pnpm run server:dev` | 启动后端开发服务器 |
| `pnpm run client:dev` | 启动前端开发服务器 |
| `pnpm run server:start` | 启动后端生产服务器 |
| `pnpm run client:build` | 构建前端生产版本 |
| `pnpm run client:test` | 运行前端测试 |
| `pnpm run clean` | 清理所有 node_modules |
| `pnpm run fresh-install` | 清理并重新安装 |

## 📦 依赖管理

本项目使用 **PNPM Workspace** 进行依赖管理，所有依赖都在根目录的 `package.json` 中统一管理：

### 前端依赖
- React 19.1.0
- Ant Design 5.26.0
- Axios、Moment.js、Recharts 等

### 后端依赖
- Express 4.21.2
- JWT、BCrypt、CORS 等
- Got、Node-RSA 等

### 开发依赖
- Nodemon、Concurrently、Rimraf

## 🔧 配置说明

### 配置文件系统

项目使用基于文件的配置系统，配置文件位于 `server/database/config.json`。

### 配置结构

```json
{
  "server": {
    "port": 12041,
    "host": "0.0.0.0",
    "logLevel": "info"
  },
  "client": {
    "port": 3000,
    "apiUrl": "http://localhost:12041/api"
  },
  "api": {
    "defaultAuthKey": "1145141919810",
    "timeout": 10000,
    "baseUrls": {
      "QQ": "http://127.0.0.1:9511",
      "qidian": "http://127.0.0.1:9513",
      "qqlite": "http://127.0.0.1:9512",
      "tim": "http://127.0.0.1:9514"
    }
  },
  "database": {
    "path": "./database/signature.json",
    "backupInterval": 24
  }
}
```

### 配置管理

- **Web界面管理**: 通过前端管理控制台的"系统配置"页面
- **命令行管理**: 使用 `pnpm run config:init` 运行配置向导
- **手动编辑**: 直接编辑 `server/database/config.json` 文件

### 环境变量

在根目录创建 `.env` 文件：

```env
# 后端配置
PORT=3001
JWT_SECRET=your-jwt-secret-key
DB_PATH=./database/signature.json

# 前端配置
REACT_APP_API_URL=http://localhost:3001
```
```bash
# 双击运行或在命令行执行
start.bat
```

#### Linux/macOS 用户
```bash
# 添加执行权限并运行
chmod +x start.sh
./start.sh
```

### 📝 启动说明

启动脚本会自动完成以下操作：
1. ✅ 检查 Node.js 环境
2. 📦 安装 pnpm 包管理器（如未安装）
3. 🔧 安装项目依赖
4. 🚀 启动前后端服务

启动成功后，您可以访问：
- **前端管理界面**: http://localhost:3000
- **后端API服务**: http://localhost:3001

### 🔧 手动安装（可选）

**方法一：双击运行（推荐）**
```bash
# 双击 start.bat 文件
start.bat
```

**方法二：PowerShell（美观界面）**
```powershell
# 右键选择"用PowerShell运行"或在PowerShell中执行
.\start.ps1
```

#### Linux/macOS 用户

```bash
# 设置执行权限
chmod +x start.sh

# 运行启动脚本
./start.sh
```

### 🔧 手动安装

如果一键启动失败，可以手动安装：

```bash
# 1. 克隆或下载项目
git clone <repository-url>
cd QsignHookServer

# 2. 安装 pnpm（如果未安装）
npm install -g pnpm

# 3. 安装依赖
pnpm install
cd server && pnpm install && cd ..
cd client && pnpm install && cd ..

# 4. 启动服务
pnpm run dev
```

## 🌐 访问地址

启动成功后，通过以下地址访问：

| 服务 | 地址 | 说明 |
|------|------|------|
| 🖥️ **管理控制台** | http://localhost:3000 | Web管理界面 |
| 🔌 **API服务器** | http://localhost:12041 | 签名服务API |
| 🏥 **健康检查** | http://localhost:12041/health | 服务状态检查 |

## 👤 默认账号

| 项目 | 值 |
|------|-----|
| **用户名** | `admin` |
| **密码** | `admin123` |
| **API密钥** | `1145141919810` |

> ⚠️ **安全提示**: 生产环境请务必修改默认密码和API密钥！

## 📖 核心功能

### 🔐 签名服务 API

#### 支持的客户端类型

| 客户端 | Type参数 | 端口 | 说明 |
|--------|----------|------|------|
| QQ | `QQ` | 9511 | 标准QQ客户端 |
| TIM | `tim` | 9514 | TIM轻量客户端 |
| 企点 | `qidian` | 9513 | 企业QQ |
| QQ轻聊版 | `qqlite` | 9512 | QQ精简版 |

#### 主要接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/sign` | POST | 获取协议签名 |
| `/energy` | POST | 查询用户能量 |
| `/Get0553` | POST | 获取调试ID |

### 📊 管理控制台

- **📈 数据统计** - 实时请求统计、成功率分析
- **📋 日志管理** - 详细的请求日志和错误日志
- **⚙️ 系统设置** - API密钥、端口等配置管理
- **👥 用户管理** - 用户认证和权限控制
- **📊 可视化图表** - 基于Recharts的数据可视化

### 🔒 安全特性

- JWT用户认证
- API密钥验证
- 请求日志记录
- 错误监控和提醒

## 📁 项目结构

```
QsignHookServer/
├── 📂 client/                  # React 前端应用
│   ├── 📂 src/
│   │   ├── 📂 components/      # React 组件
│   │   ├── 📂 pages/          # 页面组件
│   │   ├── 📂 services/       # API 服务
│   │   └── 📂 styles/         # 样式文件
│   ├── 📂 public/             # 静态资源
│   └── 📄 package.json        # 前端依赖配置
├── 📂 server/                 # Express 后端服务
│   ├── 📂 src/
│   │   ├── 📂 routes/         # API 路由
│   │   ├── 📂 models/         # 数据模型
│   │   ├── 📂 middleware/     # 中间件
│   │   └── 📂 utils/          # 工具函数
│   ├── 📂 database/           # 数据库文件
│   └── 📄 package.json        # 后端依赖配置
├── 🚀 start.bat              # Windows 启动脚本
├── 🚀 start.ps1              # PowerShell 启动脚本
├── 🚀 start.sh               # Linux/macOS 启动脚本
├── 🔧 quick-start.bat         # 快速启动脚本
├── 📋 diagnose.ps1            # 诊断脚本
├── 📖 API_Documentation.md    # API 文档
├── 📖 START_GUIDE.md          # 启动指南
└── 📄 package.json            # 根项目配置
```

## 🛠️ 开发指南

### 🔧 可用脚本

```bash
# 开发模式 - 同时启动前后端
pnpm run dev

# 仅启动后端服务
pnpm run server:dev

# 仅启动前端服务
pnpm run client:dev

# 生产模式启动
pnpm run start

# 构建前端项目
pnpm run client:build

# 安装所有依赖
pnpm run install:all

# 清理所有依赖
pnpm run clean

# 清理并重新安装
pnpm run fresh-install
```

### 🔌 环境变量

#### 后端环境变量
```bash
PORT=12041                    # 服务端口
NODE_ENV=development          # 运行环境
```

#### 前端环境变量
```bash
REACT_APP_API_URL=http://localhost:12041/api  # API基础URL
DISABLE_ESLINT_PLUGIN=true    # 禁用ESLint插件
GENERATE_SOURCEMAP=false      # 禁用SourceMap生成
```

### 🔧 技术栈

#### 后端技术
- **Node.js** - JavaScript运行时
- **Express.js** - Web应用框架
- **node-json-db** - 轻量级JSON数据库
- **jsonwebtoken** - JWT认证
- **bcryptjs** - 密码加密
- **axios** - HTTP客户端
- **cors** - 跨域资源共享
- **colors** - 终端颜色输出

#### 前端技术
- **React 19.1.0** - 用户界面库
- **Ant Design 5.25.4** - UI组件库
- **Recharts** - 数据可视化
- **Axios** - HTTP客户端
- **Moment.js** - 日期处理

## 🚨 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# Windows 检查端口
netstat -ano | findstr :12041
netstat -ano | findstr :3000

# 结束进程
taskkill /PID <进程ID> /F
```

#### 2. Node.js 版本过低
```bash
# 检查版本
node --version

# 更新到最新版本
# 访问 https://nodejs.org/ 下载最新版本
```

#### 3. 依赖安装失败
```bash
# 清理缓存
pnpm store prune
npm cache clean --force

# 重新安装
pnpm run fresh-install
```

#### 4. 权限问题（Linux/macOS）
```bash
# 给脚本执行权限
chmod +x start.sh

# 如果需要全局安装权限
sudo npm install -g pnpm
```

### 🔍 诊断工具

运行诊断脚本检查环境：
```bash
# Windows PowerShell
.\diagnose.ps1

# 详细模式
.\diagnose.ps1 -Verbose
```

## 📋 API 文档

详细的API文档请参考：[API_Documentation.md](API_Documentation.md)

### 快速示例

```bash
# 获取签名
curl -X POST "http://localhost:12041/sign?Type=QQ" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "x-xiya-authkey: 1145141919810" \
  -d "cmd=810&uin=123456789&data=example"

# 查询能量
curl -X POST "http://localhost:12041/energy?Type=QQ" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "x-xiya-authkey: 1145141919810" \
  -d "uin=123456789&data=example"
```

## 🔐 安全配置

### 生产环境安全建议

1. **修改默认密码**
   - 登录管理控制台修改默认账号密码
   - 定期更换密码

2. **更换API密钥**
   - 在设置页面生成新的API密钥
   - 确保密钥足够复杂

3. **网络安全**
   - 配置防火墙规则
   - 使用HTTPS（需要反向代理）
   - 限制访问IP范围

4. **监控日志**
   - 定期检查访问日志
   - 监控异常请求
   - 设置告警机制

## 🤝 贡献指南

欢迎提交问题和功能请求！

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目基于 MIT 许可证开源 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Express.js](https://expressjs.com/) - 后端框架
- [React](https://reactjs.org/) - 前端框架
- [Ant Design](https://ant.design/) - UI组件库
- [Recharts](https://recharts.org/) - 图表库

## 📞 技术支持

如果你在使用过程中遇到问题：

1. 📖 查看 [启动指南](START_GUIDE.md)
2. 🔍 运行诊断脚本检查环境
3. 📋 查看 [API文档](API_Documentation.md)
4. 🐛 提交 Issue 描述问题

---

<div align="center">

**⭐ 如果这个项目对你有帮助，请给它一个星标！**

*最后更新: 2025年6月8日*

</div>
