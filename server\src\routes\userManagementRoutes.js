/**
 * 🔧 用户管理路由 - 重构版本
 * 提供添加和删除管理员用户的功能，仅限 xiya 用户访问
 */
const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const dataModelInstance = require('../models/mysql-real-model');

// JWT 密钥
const JWT_SECRET = 'xiya_signature_server_secret_key_2024';

module.exports = (dataModel) => {
  // 使用传入的数据模型实例或默认实例
  const dbModel = dataModel || dataModelInstance;

  // 简化的认证中间件
  const authenticateUser = async (req, res, next) => {
    try {
      const authHeader = req.headers['authorization'];
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({
          code: 401,
          msg: '需要登录'
        });
      }

      const token = authHeader.split(' ')[1];
      const decoded = jwt.verify(token, JWT_SECRET);
      req.user = decoded;

      console.log(`✅ 用户认证成功: ${decoded.username}`);
      next();
    } catch (error) {
      console.error('用户认证失败:', error);
      return res.status(401).json({
        code: 401,
        msg: '登录已过期，请重新登录'
      });
    }
  };

  // 检查是否为 xiya 用户
  const checkXiyaUser = (req, res, next) => {
    if (req.user && req.user.username === 'xiya') {
      next();
    } else {
      return res.status(403).json({
        code: 403,
        msg: '权限不足：此功能仅限 xiya 用户使用'
      });
    }
  };
  /**
   * 🏠 获取所有用户列表
   */
  router.get('/users', authenticateUser, checkXiyaUser, async (req, res) => {
    try {
      console.log(`[${new Date().toISOString()}] GET /api/user-management/users - 用户: ${req.user.username}`);
      
      const users = await dbModel.getAllUsers();
      console.log(`📊 获取到 ${users.length} 个用户:`, users.map(u => u.username));
      
      // 处理用户数据
      const safeUsers = users.map(user => ({
        id: user.id,
        username: user.username,
        role: user.role || 'admin',
        created_at: user.created_at,
        last_login: user.last_login,
        is_2fa_enabled: user.totp_secret ? true : false
      }));

      console.log(`✅ 返回用户数据:`, safeUsers);

      res.json({
        code: 0,
        msg: '获取成功',
        data: safeUsers
      });
    } catch (error) {
      console.error('获取用户列表失败:', error);
      res.status(500).json({
        code: 500,
        msg: '获取失败',
        error: error.message
      });
    }
  });
  /**
   * ➕ 添加新的管理员用户
   */
  router.post('/users', authenticateUser, checkXiyaUser, async (req, res) => {
    try {
      const { username, password } = req.body;

      // 验证输入
      if (!username || !password) {
        return res.status(400).json({
          code: 400,
          msg: '用户名和密码不能为空'
        });
      }

      if (username.length < 3 || password.length < 6) {
        return res.status(400).json({
          code: 400,
          msg: '用户名至少3位，密码至少6位'
        });
      }

      console.log(`[${new Date().toISOString()}] POST /api/user-management/users - 添加用户: ${username}`);

      // 检查用户名是否已存在
      const existingUser = await dbModel.getUserByUsername(username);
      if (existingUser) {
        return res.status(409).json({
          code: 409,
          msg: '用户名已存在'
        });
      }

      // 创建用户
      const userId = await dbModel.createUser(username, password);

      res.json({
        code: 0,
        msg: '管理员用户创建成功',
        data: {
          id: userId,
          username: username,
          role: 'admin'
        }
      });
    } catch (error) {
      console.error('创建用户失败:', error);
      res.status(500).json({
        code: 500,
        msg: '创建失败',
        error: error.message
      });
    }
  });
  /**
   * 🗑️ 删除管理员用户
   */
  router.delete('/users/:userId', authenticateUser, checkXiyaUser, async (req, res) => {
    try {
      const { userId } = req.params;

      console.log(`[${new Date().toISOString()}] DELETE /api/user-management/users/${userId}`);

      // 获取要删除的用户信息
      const userToDelete = await dbModel.getUserById(userId);
      if (!userToDelete) {
        return res.status(404).json({
          code: 404,
          msg: '用户不存在'
        });
      }

      // 不允许删除 xiya 用户
      if (userToDelete.username === 'xiya') {
        return res.status(403).json({
          code: 403,
          msg: '不能删除 xiya 用户'
        });
      }

      // 不允许删除自己
      if (userToDelete.id === req.user.id) {
        return res.status(403).json({
          code: 403,
          msg: '不能删除自己的账户'
        });
      }

      // 删除用户
      await dbModel.deleteUser(userId);

      res.json({
        code: 0,
        msg: `用户 ${userToDelete.username} 已删除`
      });
    } catch (error) {
      console.error('删除用户失败:', error);
      res.status(500).json({
        code: 500,
        msg: '删除失败',
        error: error.message
      });
    }
  });
  /**
   * 🔄 重置用户密码
   */
  router.put('/users/:userId/password', authenticateUser, checkXiyaUser, async (req, res) => {
    try {
      const { userId } = req.params;
      const { newPassword } = req.body;

      if (!newPassword || newPassword.length < 6) {
        return res.status(400).json({
          code: 400,
          msg: '新密码至少6位'
        });
      }

      console.log(`[${new Date().toISOString()}] PUT /api/user-management/users/${userId}/password`);

      // 获取用户信息
      const user = await dbModel.getUserById(userId);
      if (!user) {
        return res.status(404).json({
          code: 404,
          msg: '用户不存在'
        });
      }

      // 更新密码
      await dbModel.updateUserPassword(userId, newPassword);

      res.json({
        code: 0,
        msg: `用户 ${user.username} 的密码已重置`
      });
    } catch (error) {
      console.error('重置密码失败:', error);
      res.status(500).json({
        code: 500,
        msg: '重置失败',
        error: error.message
      });
    }
  });
  /**
   * 🔐 重置用户2FA
   */
  router.delete('/users/:userId/2fa', authenticateUser, checkXiyaUser, async (req, res) => {
    try {
      const { userId } = req.params;

      console.log(`[${new Date().toISOString()}] DELETE /api/user-management/users/${userId}/2fa`);

      // 获取用户信息
      const user = await dbModel.getUserById(userId);
      if (!user) {
        return res.status(404).json({
          code: 404,
          msg: '用户不存在'
        });
      }

      // 清除2FA设置
      await dbModel.clearUserTotpSecret(userId);

      res.json({
        code: 0,
        msg: `用户 ${user.username} 的二步验证已重置`
      });
    } catch (error) {
      console.error('重置2FA失败:', error);
      res.status(500).json({
        code: 500,
        msg: '重置失败',
        error: error.message
      });
    }
  });

  return router;
};
