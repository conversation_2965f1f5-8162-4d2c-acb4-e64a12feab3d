const colors = require('colors');
const dataModel = require('../models/mysql-real-model');

/**
 * 节点选择中间件
 * 根据不同的128位token选择对应的签名节点（支持一对多映射）
 */
class NodeSelector {
    constructor() {
        this.tokenNodeMap = new Map(); // token -> [nodes] mapping cache (一对多)
        this.nodeConfigs = new Map();  // node configs cache
        this.lastCacheUpdate = 0;
        this.cacheTimeout = 60000; // 1分钟缓存
    }

    /**
     * 更新节点配置缓存
     */
    async updateCache() {
        try {
            const now = Date.now();
            if (now - this.lastCacheUpdate < this.cacheTimeout) {
                return; // 缓存未过期
            }

            console.log('🔄 更新节点配置缓存...'.blue);
            
            // 获取所有API配置
            const configs = await dataModel.getAllApiConfigs();
            
            // 清空缓存
            this.tokenNodeMap.clear();
            this.nodeConfigs.clear();
            
            // 重建缓存 - 支持一个token对应多个节点
            for (const config of configs) {
                if (config.auth_key && config.auth_key.length === 128) {
                    // 获取或创建token对应的节点数组
                    if (!this.tokenNodeMap.has(config.auth_key)) {
                        this.tokenNodeMap.set(config.auth_key, []);
                    }

                    // 添加节点到token的授权列表
                    const authorizedNodes = this.tokenNodeMap.get(config.auth_key);
                    if (!authorizedNodes.includes(config.client_type)) {
                        authorizedNodes.push(config.client_type);
                    }

                    // 缓存节点配置
                    this.nodeConfigs.set(config.client_type, config);
                    console.log(`📝 映射token到节点: ${config.auth_key.substring(0, 8)}... -> ${config.client_type}`.gray);
                }
            }
            
            this.lastCacheUpdate = now;
            console.log(`✅ 节点配置缓存更新完成，共${this.tokenNodeMap.size}个token映射`.green);
            
        } catch (error) {
            console.error('更新节点配置缓存失败:'.red, error.message);
        }
    }

    /**
     * 根据token获取授权的节点列表
     */
    async getAuthorizedNodesByToken(token) {
        if (!token || token.length !== 128) {
            return [];
        }

        // 直接从数据库获取最新的token信息，不依赖缓存
        const tokenInfo = await dataModel.getTokenModeAndNodes(token);
        return tokenInfo.nodes || [];
    }

    /**
     * 根据token和请求类型选择最佳节点
     */
    async selectBestNode(token, requestType = 'sign') {
        // 获取token模式和授权节点
        const tokenInfo = await dataModel.getTokenModeAndNodes(token);

        if (!tokenInfo.mode || tokenInfo.nodes.length === 0) {
            return null;
        }

        const { mode, nodes } = tokenInfo;

        // 如果只有一个节点，直接返回
        if (nodes.length === 1) {
            console.log(`✅ ${mode === 'exclusive' ? '独享' : '共享'}模式，使用唯一节点: ${nodes[0].client_type}`.green);
            return nodes[0];
        }

        // 独享模式：应该只有一个节点，如果有多个则是配置错误
        if (mode === 'exclusive') {
            console.warn(`⚠️  独享模式token发现多个节点，这可能是配置错误！使用第一个节点: ${nodes[0].client_type}`.yellow);
            return nodes[0];
        }

        // 共享模式：使用负载均衡选择最佳节点
        if (mode === 'shared') {
            const selectedNode = this.selectNodeByStrategy(nodes, requestType);
            console.log(`🔄 共享模式负载均衡选择节点: ${selectedNode.client_type} (${nodes.length}个可用节点)`.blue);
            return selectedNode;
        }

        return nodes[0];
    }

    /**
     * 节点选择策略（用于共享模式的负载均衡）
     */
    selectNodeByStrategy(nodes, requestType) {
        // 过滤出活跃的节点
        const activeNodes = nodes.filter(node => node.status === 'active');

        if (activeNodes.length === 0) {
            console.warn('⚠️  没有活跃的节点可用'.yellow);
            return nodes[0]; // 返回第一个节点作为备选
        }

        if (activeNodes.length === 1) {
            return activeNodes[0];
        }

        // 策略1: 基于时间的轮询选择（简单负载均衡）
        // 每5秒切换一次，确保相对均匀的分布
        const now = Date.now();
        const index = Math.floor(now / 5000) % activeNodes.length;

        const selectedNode = activeNodes[index];
        console.log(`🔄 共享模式负载均衡: ${selectedNode.client_type} (${index + 1}/${activeNodes.length}个活跃节点)`.blue);

        return selectedNode;
    }

    /**
     * 根据token获取节点配置（兼容旧接口）
     */
    async getNodeByToken(token) {
        return await this.selectBestNode(token);
    }

    /**
     * 获取所有token-节点映射
     */
    async getAllTokenMappings() {
        await this.updateCache();

        const mappings = [];
        for (const [token, authorizedNodeTypes] of this.tokenNodeMap.entries()) {
            const authorizedNodes = authorizedNodeTypes
                .map(nodeType => this.nodeConfigs.get(nodeType))
                .filter(config => config);

            if (authorizedNodes.length > 0) {
                mappings.push({
                    token: token.substring(0, 8) + '...' + token.substring(120), // 显示部分token
                    fullToken: token,
                    authorizedNodes: authorizedNodes.map(config => ({
                        nodeType: config.client_type,
                        baseUrl: config.base_url,
                        description: config.description || ''
                    })),
                    nodeCount: authorizedNodes.length,
                    status: 'active'
                });
            }
        }

        return mappings;
    }

    /**
     * 验证token并选择节点的中间件
     */
    async middleware(req, res, next) {
        try {
            // 获取token（支持多种方式）
            const token = req.headers['x-node-token'] ||
                         req.headers['x-xiya-authkey'] ||
                         (req.headers.authorization && !req.headers.authorization.startsWith('Bearer ') ? req.headers.authorization : null) ||
                         req.query.token ||
                         req.body.token;

            if (!token) {
                return res.status(400).json({
                    code: 400,
                    msg: '缺少节点token，请在请求头中添加 x-node-token 或使用其他认证方式'
                });
            }

            // 验证token格式
            if (token.length !== 128) {
                return res.status(400).json({
                    code: 400,
                    msg: 'token格式错误，必须是128位字符串'
                });
            }

            // 根据token获取授权的节点列表
            const authorizedNodes = await this.getAuthorizedNodesByToken(token);

            if (authorizedNodes.length === 0) {
                console.log(`❌ 未找到token对应的授权节点: ${token.substring(0, 8)}...`.red);
                return res.status(403).json({
                    code: 403,
                    msg: '无效的节点token，未找到对应的签名节点'
                });
            }

            // 选择最佳节点
            const selectedNode = await this.selectBestNode(token, req.path);

            if (!selectedNode) {
                console.log(`❌ 无法选择可用节点: ${token.substring(0, 8)}...`.red);
                return res.status(503).json({
                    code: 503,
                    msg: '当前没有可用的签名节点'
                });
            }

            // 检查选中节点状态
            if (selectedNode.status !== 'active') {
                console.log(`⚠️  选中节点已禁用: ${selectedNode.client_type}`.yellow);
                return res.status(503).json({
                    code: 503,
                    msg: `签名节点 ${selectedNode.client_type} 当前不可用`
                });
            }

            // 将节点配置添加到请求对象
            req.selectedNode = selectedNode;
            req.authorizedNodes = authorizedNodes;
            req.nodeToken = token;

            console.log(`✅ 节点选择成功: ${token.substring(0, 8)}... -> ${selectedNode.client_type} (${selectedNode.base_url}) [${authorizedNodes.length}个授权节点]`.green);
            
            next();
            
        } catch (error) {
            console.error('节点选择中间件错误:'.red, error.message);
            return res.status(500).json({
                code: 500,
                msg: '节点选择失败',
                error: error.message
            });
        }
    }

    /**
     * 强制刷新缓存
     */
    async refreshCache() {
        this.lastCacheUpdate = 0;
        await this.updateCache();
    }

    /**
     * 获取缓存统计信息
     */
    getCacheStats() {
        return {
            tokenMappings: this.tokenNodeMap.size,
            nodeConfigs: this.nodeConfigs.size,
            lastUpdate: new Date(this.lastCacheUpdate).toISOString(),
            cacheAge: Date.now() - this.lastCacheUpdate
        };
    }
}

// 创建单例实例
const nodeSelector = new NodeSelector();

// 导出中间件函数
const nodeSelectorMiddleware = async (req, res, next) => {
    await nodeSelector.middleware(req, res, next);
};

// 导出实例和中间件
module.exports = {
    nodeSelector,
    nodeSelectorMiddleware
};
