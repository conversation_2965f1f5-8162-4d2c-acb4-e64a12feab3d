#!/usr/bin/env node
const fs = require('fs');
const path = require('path');
const readline = require('readline');
const colors = require('colors');

// 创建readline接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 默认配置
const DEFAULT_CONFIG = {
  server: {
    port: 12041,
    host: '0.0.0.0',
    logLevel: 'info'
  },  client: {
    port: 3000,
    apiUrl: 'http://0.0.0.0:12041/api'
  },  api: {
    defaultAuthKey: '1145141919810',
    timeout: 10000,
    baseUrls: {
      'QQ': 'http://127.0.0.1:9511',
      'qidian': 'http://127.0.0.1:9513',
      'qqlite': 'http://127.0.0.1:9512',
      'tim': 'http://127.0.0.1:9514'
    },
    energyBaseUrl: 'http://127.0.0.1',
    get0553BaseUrl: 'http://127.0.0.1'
  },
  database: {
    path: './database/signature.json',
    backupInterval: 24 // 小时
  }
};

// 配置文件路径
const configDir = path.join(__dirname, '../database');
const configPath = path.join(configDir, 'config.json');

// 确保配置目录存在
if (!fs.existsSync(configDir)) {
  fs.mkdirSync(configDir, { recursive: true });
  console.log(`✅ 创建配置目录: ${configDir}`.green);
}

// 询问函数 - 返回一个Promise
function ask(question, defaultValue) {
  return new Promise((resolve) => {
    rl.question(`${question} ${defaultValue ? `(默认: ${defaultValue})` : ''}: `, (answer) => {
      resolve(answer || defaultValue);
    });
  });
}

// 主函数
async function main() {
  console.log('\n');
  console.log('╔══════════════════════════════════════════════════════════╗'.rainbow.bold);
  console.log('║                                                          ║'.rainbow.bold);
  console.log('║            🔧 签名服务端配置向导 🔧                        ║'.cyan.bold);
  console.log('║                                                          ║'.rainbow.bold);
  console.log('║          欢迎使用签名算法服务端配置初始化工具              ║'.white);
  console.log('║                                                          ║'.rainbow.bold);
  console.log('╚══════════════════════════════════════════════════════════╝'.rainbow.bold);
  
  // 检查配置文件是否存在
  if (fs.existsSync(configPath)) {
    console.log('\n📁 检测到已有配置文件:'.yellow.bold);
    console.log(`   位置: ${configPath}`.gray);
    
    const useExisting = await ask('🤔 是否使用现有配置? (yes/no)', 'yes');
    if (useExisting.toLowerCase() === 'yes') {
      console.log('\n✅ 使用现有配置文件'.green.bold);
      console.log('🚀 可以直接启动服务器了!'.green);
      rl.close();
      return;
    }
    
    console.log('\n🔄 将创建新的配置文件...'.yellow);
  }
  
  // 创建新的配置
  console.log('\n📋 开始配置向导，请按提示输入配置信息'.cyan.bold);
  console.log('💡 提示: 直接按回车使用默认值'.gray);
  
  // 配置对象
  const config = { ...DEFAULT_CONFIG };
  
  // 服务器配置
  console.log('\n' + '─'.repeat(50).yellow);
  console.log('🖥️  服务器配置'.yellow.bold);
  console.log('─'.repeat(50).yellow);
  
  config.server.port = parseInt(await ask('📡 后端服务器端口', config.server.port));
  config.server.host = await ask('🌐 后端服务器主机地址', config.server.host);
  
  // 客户端配置
  console.log('\n' + '─'.repeat(50).blue);
  console.log('🖥️  前端配置'.blue.bold);
  console.log('─'.repeat(50).blue);
  
  config.client.port = parseInt(await ask('🌐 前端服务器端口', config.client.port));
  config.client.apiUrl = await ask('🔗 API URL (前端调用的后端地址)', 
    `http://localhost:${config.server.port}/api`);
  
  // API配置
  console.log('\n' + '─'.repeat(50).green);
  console.log('🔑 API配置'.green.bold);
  console.log('─'.repeat(50).green);
  
  config.api.defaultAuthKey = await ask('🔐 默认API认证密钥', config.api.defaultAuthKey);
  
  // 签名服务基础URL
  console.log('\n' + '─'.repeat(50).magenta);
  console.log('🌐 签名服务基础URL配置'.magenta.bold);
  console.log('─'.repeat(50).magenta);
  console.log('💡 这些是各个客户端类型的签名服务地址'.gray);
  
  for (const [type, url] of Object.entries(config.api.baseUrls)) {
    const emoji = type === 'QQ' ? '🐧' : type === 'tim' ? '📱' : type === 'qidian' ? '🏢' : '💬';
    config.api.baseUrls[type] = await ask(`${emoji} ${type}签名服务地址`, url);
  }
  
  // 保存配置文件
  console.log('\n' + '─'.repeat(50).cyan);
  console.log('💾 保存配置'.cyan.bold);
  console.log('─'.repeat(50).cyan);
  
  try {
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    
    console.log('\n✅ 配置文件保存成功!'.green.bold);
    console.log(`📁 文件位置: ${configPath}`.gray);
    
    console.log('\n📊 配置摘要:'.cyan.bold);
    console.log(`   🖥️  后端地址: http://${config.server.host}:${config.server.port}`.cyan);
    console.log(`   🌐 前端地址: http://localhost:${config.client.port}`.cyan);
    console.log(`   🔗 API地址: ${config.client.apiUrl}`.cyan);
    console.log(`   🔑 认证密钥: ${config.api.defaultAuthKey}`.cyan);
    
    console.log('\n🚀 下一步操作:'.green.bold);
    console.log('   1. 启动服务器: pnpm run dev'.green);
    console.log('   2. 或者使用: pnpm run start'.green);
    console.log('   3. 访问前端: http://localhost:' + config.client.port.toString().green);
    
  } catch (error) {
    console.log('\n❌ 保存配置文件失败!'.red.bold);
    console.error(`   错误: ${error.message}`.red);
    console.log('\n💡 解决建议:'.yellow.bold);
    console.log('   1. 检查文件写入权限'.yellow);
    console.log('   2. 检查磁盘空间'.yellow);
    console.log('   3. 手动创建目录'.yellow);
  }
  
  rl.close();
}

// 启动配置向导
main().catch((error) => {
  console.error(`\n❌ 配置向导出错: ${error.message}`.red.bold);
  rl.close();
});
