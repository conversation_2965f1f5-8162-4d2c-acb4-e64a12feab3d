#!/usr/bin/env node

const colors = require('colors');
const axios = require('axios');

/**
 * 测试监控API接口
 */
async function testMonitorAPIs() {
    console.log('📊 测试监控API接口...'.cyan);
    
    const baseUrl = 'http://localhost:12041';
    
    // 测试的监控接口
    const monitoringEndpoints = [
        {
            name: '签名服务状态',
            method: 'GET',
            url: '/api/sign/status',
            description: '获取签名服务的实时状态'
        },
        {
            name: '签名服务性能统计',
            method: 'GET', 
            url: '/api/sign/performance',
            description: '获取性能统计数据'
        },
        {
            name: '签名服务健康检查',
            method: 'GET',
            url: '/api/sign/health', 
            description: '检查服务健康状态'
        },
        {
            name: '重新加载节点配置',
            method: 'POST',
            url: '/api/sign/reload-nodes',
            description: '重新加载签名节点配置'
        }
    ];
    
    console.log(`📋 准备测试 ${monitoringEndpoints.length} 个监控接口...`.gray);
    
    for (const endpoint of monitoringEndpoints) {
        console.log(`\n🔍 测试: ${endpoint.name}`.yellow);
        console.log(`   方法: ${endpoint.method}`.gray);
        console.log(`   路径: ${endpoint.url}`.gray);
        console.log(`   功能: ${endpoint.description}`.gray);
        
        try {
            let response;
            
            if (endpoint.method === 'GET') {
                response = await axios.get(`${baseUrl}${endpoint.url}`, {
                    timeout: 10000
                });
            } else if (endpoint.method === 'POST') {
                response = await axios.post(`${baseUrl}${endpoint.url}`, {}, {
                    timeout: 10000
                });
            }
            
            if (response.data.code === 0) {
                console.log(`   ✅ 成功: ${response.data.msg}`.green);
                
                // 显示关键数据
                if (endpoint.url.includes('status') && response.data.data) {
                    const data = response.data.data;
                    console.log(`      初始化: ${data.initialized ? '✅' : '❌'}`.gray);
                    if (data.initialized) {
                        console.log(`      队列长度: ${data.queueLength}`.gray);
                        console.log(`      当前并发: ${data.currentConcurrency}`.gray);
                        console.log(`      总请求: ${data.totalRequests}`.gray);
                        console.log(`      成功请求: ${data.completedRequests}`.gray);
                    }
                }
                
                if (endpoint.url.includes('performance') && response.data.data) {
                    const data = response.data.data;
                    if (data.queue) {
                        console.log(`      成功率: ${data.queue.successRate}%`.gray);
                        console.log(`      平均响应时间: ${data.queue.averageResponseTime}ms`.gray);
                    }
                    if (data.nodes) {
                        console.log(`      节点数量: ${data.nodes.length}`.gray);
                    }
                }
                
                if (endpoint.url.includes('health') && response.data.data) {
                    const data = response.data.data;
                    console.log(`      健康状态: ${data.healthy ? '✅' : '❌'}`.gray);
                    console.log(`      健康节点: ${data.healthyNodes}/${data.totalNodes}`.gray);
                    console.log(`      健康率: ${data.healthRatio}%`.gray);
                }
                
                if (endpoint.url.includes('reload-nodes') && response.data.data) {
                    const data = response.data.data;
                    console.log(`      重新加载节点数: ${data.nodeCount}`.gray);
                }
                
            } else {
                console.log(`   ❌ 失败: ${response.data.msg}`.red);
            }
            
        } catch (error) {
            if (error.response) {
                console.log(`   ❌ HTTP错误: ${error.response.status}`.red);
                console.log(`      响应: ${JSON.stringify(error.response.data)}`.gray);
                
                if (error.response.status === 404) {
                    console.log(`      💡 建议: 检查路由路径是否正确`.yellow);
                } else if (error.response.status === 500) {
                    console.log(`      💡 建议: 检查服务是否正常运行`.yellow);
                }
            } else if (error.code === 'ECONNREFUSED') {
                console.log(`   ❌ 连接失败: 服务器未启动`.red);
                console.log(`      💡 建议: 确保服务器在 ${baseUrl} 运行`.yellow);
            } else {
                console.log(`   ❌ 请求失败: ${error.message}`.red);
            }
        }
    }
    
    // 测试路由路径验证
    console.log('\n🔗 验证路由路径...'.cyan);
    
    const pathTests = [
        '/api/sign/status',
        '/api/sign/performance', 
        '/api/sign/health'
    ];
    
    for (const path of pathTests) {
        try {
            const response = await axios.get(`${baseUrl}${path}`, {
                timeout: 5000,
                validateStatus: () => true // 接受所有状态码
            });
            
            if (response.status === 404) {
                console.log(`   ❌ ${path}: 路由不存在 (404)`.red);
            } else if (response.status >= 200 && response.status < 300) {
                console.log(`   ✅ ${path}: 路由正常 (${response.status})`.green);
            } else if (response.status >= 500) {
                console.log(`   ⚠️  ${path}: 服务器错误 (${response.status})`.yellow);
            } else {
                console.log(`   ✅ ${path}: 路由存在 (${response.status})`.green);
            }
            
        } catch (error) {
            if (error.code === 'ECONNREFUSED') {
                console.log(`   ❌ ${path}: 服务器连接失败`.red);
                break; // 如果连接失败，跳出循环
            } else {
                console.log(`   ⚠️  ${path}: ${error.message}`.yellow);
            }
        }
    }
    
    console.log('\n🎉 监控API测试完成!'.green.bold);
    
    // 显示修复总结
    console.log('\n🔧 修复内容:'.rainbow.bold);
    console.log('✅ 修正了监控接口的路由路径'.green);
    console.log('✅ /api/sign/status - 签名服务状态'.green);
    console.log('✅ /api/sign/performance - 性能统计'.green);
    console.log('✅ /api/sign/health - 健康检查'.green);
    console.log('✅ /api/sign/reload-nodes - 重新加载节点'.green);
    
    console.log('\n📋 使用说明:'.cyan.bold);
    console.log('• 前端监控面板现在可以正常获取数据'.gray);
    console.log('• 所有监控接口都在 /api/sign/ 路径下'.gray);
    console.log('• 支持实时状态监控和性能统计'.gray);
    console.log('• 可以通过接口重新加载节点配置'.gray);
}

// 如果直接运行此脚本
if (require.main === module) {
    testMonitorAPIs()
        .then(() => {
            console.log('\n✅ 测试脚本执行完成!'.green.bold);
            process.exit(0);
        })
        .catch((error) => {
            console.error('\n💥 测试脚本执行失败:'.red.bold, error);
            process.exit(1);
        });
}

module.exports = testMonitorAPIs;
