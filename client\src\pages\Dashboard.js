import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Alert, Spin, Progress, Typography, Space, Button, Tooltip, Badge } from 'antd';
import {
  UserOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  GlobalOutlined,
  SecurityScanOutlined,
  ClockCircleOutlined,
  <PERSON>boltOutlined,
  TrophyOutlined,
  RocketOutlined,
  HeartOutlined,
  StarOutlined,
  FireOutlined,
  ReloadOutlined,
  DashboardOutlined
} from '@ant-design/icons';
import { statsAPI, healthAPI } from '../services/api';
import NotificationHelper from '../components/NotificationHelper';
import { normalizeResponse, isResponseSuccess, getResponseData } from '../utils/responseHelper';

const { Title, Text } = Typography;

const Dashboard = () => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [serverStatus, setServerStatus] = useState('unknown');
  const [overview, setOverview] = useState({});
  const [error, setError] = useState(null);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  useEffect(() => {
    fetchData();
    // 每30秒刷新一次数据
    const interval = setInterval(fetchData, 30000);
    return () => clearInterval(interval);
  }, []);

  const fetchData = async (isManualRefresh = false) => {
    try {
      if (isManualRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      // 检查服务器状态
      try {
        await healthAPI.check();
        setServerStatus('online');
      } catch {
        setServerStatus('offline');
      }      // 获取统计数据
      const response = await statsAPI.getOverview();
      console.log('📊 Dashboard - 完整API响应:', response);
      
      // 使用统一的响应格式处理
      const normalizedResponse = normalizeResponse(response);
      
      if (isResponseSuccess(normalizedResponse)) {
        const responseData = getResponseData(normalizedResponse);
        const { today, total } = responseData;
        
        console.log('📊 Dashboard - 今日原始数据:', today);
        console.log('📊 Dashboard - 总体原始数据:', total);
        
        // 确保所有数值都转换为数字类型，并修正字段名
        const parsedData = {
          // 今日数据
          todayRequests: parseInt(today.total_requests) || 0,
          todaySuccess: parseInt(today.success_requests) || 0,
          todayFailed: parseInt(today.failed_requests) || 0,
          
          // 总体数据  
          totalRequests: parseInt(total.total_requests) || 0,
          successfulRequests: parseInt(total.success_requests) || 0,
          failedRequests: parseInt(total.failed_requests) || 0,
          
          // 其他统计
          uniqueIPs: parseInt(total.unique_ips) || 0,
          uniqueUINs: parseInt(total.unique_uins) || 0,
          unauthorizedAttempts: 0 // 这个需要从其他 API 获取
        };
        
        console.log('📊 Dashboard - 解析后数据:', parsedData);
        setOverview(parsedData);
        setLastUpdate(new Date());
        
        console.log('📊 Dashboard数据已更新:', {
          todayRequests: parsedData.todayRequests,
          totalRequests: parsedData.totalRequests,
          successfulRequests: parsedData.successfulRequests
        });
      } else {
        console.error('❌ Dashboard - API返回失败:', normalizedResponse.message);
        NotificationHelper.error('数据获取失败', normalizedResponse.message);
      }
    } catch (err) {
      setError('获取数据失败: ' + err.message);
      setServerStatus('offline');
      NotificationHelper.networkError(err.response?.status || 500, '获取Dashboard数据失败');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    fetchData(true);
  };

  const getStatusColor = () => {
    switch (serverStatus) {
      case 'online': return '#52c41a';
      case 'offline': return '#ff4d4f';
      default: return '#faad14';
    }
  };

  const getStatusText = () => {
    switch (serverStatus) {
      case 'online': return '在线';
      case 'offline': return '离线';
      default: return '未知';
    }
  };

  const calculateSuccessRate = () => {
    const total = overview.totalRequests || 0;
    const successful = overview.successfulRequests || 0;
    return total > 0 ? Math.round((successful / total) * 100) : 0;
  };

  const calculateTodaySuccessRate = () => {
    const total = overview.todayRequests || 0;
    const successful = overview.todaySuccess || 0;
    return total > 0 ? Math.round((successful / total) * 100) : 0;
  };

  if (loading && !overview.totalRequests) {
    return (
      <div style={{ 
        textAlign: 'center', 
        padding: '50px',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          background: 'rgba(255, 255, 255, 0.9)',
          padding: '40px',
          borderRadius: '20px',
          backdropFilter: 'blur(10px)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
        }}>
          <Spin size="large" />
          <p style={{ marginTop: 16, fontSize: '16px', color: '#333' }}>正在加载仪表盘数据...</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh',
      padding: '24px'
    }}>
      {/* 页面标题 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <div style={{
            background: 'rgba(255, 255, 255, 0.95)',
            padding: '20px 24px',
            borderRadius: '16px',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <div>
                <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
                  <DashboardOutlined style={{ marginRight: '12px' }} />
                  签名服务监控面板
                </Title>
                <Text type="secondary" style={{ fontSize: '14px' }}>
                  实时监控签名服务运行状态和性能指标
                </Text>
              </div>
              <Button
                type="primary"
                icon={<ReloadOutlined spin={refreshing} />}
                onClick={handleRefresh}
                loading={refreshing}
                size="large"
                style={{
                  borderRadius: '8px',
                  background: 'linear-gradient(135deg, #1890ff 0%, #096dd9 100%)',
                  border: 'none',
                  boxShadow: '0 4px 12px rgba(24, 144, 255, 0.3)'
                }}
              >
                刷新数据
              </Button>
            </div>
          </div>
        </Col>
      </Row>

      {/* 服务器状态 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col span={24}>
          <Alert
            message={
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <span style={{ fontSize: '16px' }}>
                  <Badge 
                    status={serverStatus === 'online' ? 'processing' : 'error'} 
                    text={
                      <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                        服务器状态: <span style={{ color: getStatusColor() }}>{getStatusText()}</span>
                        {serverStatus === 'online' && ' - 签名服务运行正常 🎉'}
                        {serverStatus === 'offline' && ' - 签名服务不可用 ⚠️'}
                      </span>
                    }
                  />
                </span>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  最后更新: {lastUpdate.toLocaleTimeString()} ✨
                </Text>
              </div>
            }
            type={serverStatus === 'online' ? 'success' : 'error'}
            showIcon
            style={{
              borderRadius: '12px',
              border: `2px solid ${serverStatus === 'online' ? '#52c41a' : '#ff4d4f'}`,
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)'
            }}
          />
        </Col>
      </Row>

      {error && (
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col span={24}>
            <Alert message={error} type="error" showIcon style={{ borderRadius: '12px' }} />
          </Col>
        </Row>
      )}

      {/* 核心指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card 
            style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              borderRadius: '16px',
              color: 'white',
              boxShadow: '0 8px 32px rgba(102, 126, 234, 0.3)',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            }}
            bodyStyle={{ padding: '24px' }}
            hoverable
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)';
              e.currentTarget.style.boxShadow = '0 12px 40px rgba(102, 126, 234, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 8px 32px rgba(102, 126, 234, 0.3)';
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px' }}>总请求数</Text>
                <div style={{ fontSize: '32px', fontWeight: 'bold', color: 'white', marginTop: '8px' }}>
                  {overview.totalRequests || 0}
                </div>
              </div>
              <ThunderboltOutlined style={{ fontSize: '48px', color: 'rgba(255,255,255,0.6)' }} />
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card 
            style={{
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              border: 'none',
              borderRadius: '16px',
              color: 'white',
              boxShadow: '0 8px 32px rgba(240, 147, 251, 0.3)',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            }}
            bodyStyle={{ padding: '24px' }}
            hoverable
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)';
              e.currentTarget.style.boxShadow = '0 12px 40px rgba(240, 147, 251, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 8px 32px rgba(240, 147, 251, 0.3)';
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px' }}>成功请求</Text>
                <div style={{ fontSize: '32px', fontWeight: 'bold', color: 'white', marginTop: '8px' }}>
                  {overview.successfulRequests || 0}
                </div>
              </div>
              <CheckCircleOutlined style={{ fontSize: '48px', color: 'rgba(255,255,255,0.6)' }} />
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card 
            style={{
              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
              border: 'none',
              borderRadius: '16px',
              color: 'white',
              boxShadow: '0 8px 32px rgba(79, 172, 254, 0.3)',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            }}
            bodyStyle={{ padding: '24px' }}
            hoverable
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)';
              e.currentTarget.style.boxShadow = '0 12px 40px rgba(79, 172, 254, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 8px 32px rgba(79, 172, 254, 0.3)';
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px' }}>失败请求</Text>
                <div style={{ fontSize: '32px', fontWeight: 'bold', color: 'white', marginTop: '8px' }}>
                  {overview.failedRequests || 0}
                </div>
              </div>
              <CloseCircleOutlined style={{ fontSize: '48px', color: 'rgba(255,255,255,0.6)' }} />
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card 
            style={{
              background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
              border: 'none',
              borderRadius: '16px',
              color: 'white',
              boxShadow: '0 8px 32px rgba(67, 233, 123, 0.3)',
              transition: 'all 0.3s ease',
              cursor: 'pointer'
            }}
            bodyStyle={{ padding: '24px' }}
            hoverable
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-4px)';
              e.currentTarget.style.boxShadow = '0 12px 40px rgba(67, 233, 123, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 8px 32px rgba(67, 233, 123, 0.3)';
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px' }}>成功率</Text>
                <div style={{ fontSize: '32px', fontWeight: 'bold', color: 'white', marginTop: '8px' }}>
                  {calculateSuccessRate()}%
                </div>
              </div>
              <TrophyOutlined style={{ fontSize: '48px', color: 'rgba(255,255,255,0.6)' }} />
            </div>
            <Progress 
              percent={calculateSuccessRate()} 
              showInfo={false} 
              strokeColor="rgba(255,255,255,0.6)"
              trailColor="rgba(255,255,255,0.2)"
              style={{ marginTop: '12px' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} md={8}>
          <Card
            title={
              <div style={{ color: '#1890ff' }}>
                <GlobalOutlined style={{ marginRight: '8px' }} />
                用户统计
              </div>
            }
            style={{
              borderRadius: '16px',
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Statistic
                title="独立IP数"
                value={overview.uniqueIPs || 0}
                prefix={<GlobalOutlined />}
                valueStyle={{ color: '#722ed1', fontSize: '24px' }}
              />
              <Statistic
                title="独立UIN数"
                value={overview.uniqueUINs || 0}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#13c2c2', fontSize: '24px' }}
              />
            </Space>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card
            title={
              <div style={{ color: '#1890ff' }}>
                <ClockCircleOutlined style={{ marginRight: '8px' }} />
                今日统计
              </div>
            }
            style={{
              borderRadius: '16px',
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Statistic
                title="今日请求"
                value={overview.todayRequests || 0}
                prefix={<FireOutlined />}
                valueStyle={{ color: '#fa8c16', fontSize: '24px' }}
              />
              <div style={{ marginTop: 16 }}>
                <Text style={{ fontSize: '12px', color: '#666' }}>今日成功率</Text>
                <Progress
                  percent={calculateTodaySuccessRate()}
                  strokeColor="#52c41a"
                  style={{ marginTop: '4px' }}
                />
              </div>
            </Space>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card
            title={
              <div style={{ color: '#1890ff' }}>
                <SecurityScanOutlined style={{ marginRight: '8px' }} />
                安全统计
              </div>
            }
            style={{
              borderRadius: '16px',
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Statistic
                title="未授权尝试"
                value={overview.unauthorizedAttempts || 0}
                prefix={<SecurityScanOutlined />}
                valueStyle={{ color: '#ff4d4f', fontSize: '24px' }}
              />
              <div style={{ marginTop: 16 }}>
                <Badge 
                  status={overview.unauthorizedAttempts > 0 ? 'error' : 'success'} 
                  text={overview.unauthorizedAttempts > 0 ? '检测到威胁' : '系统安全'}
                />
              </div>
            </Space>
          </Card>
        </Col>
      </Row>

      {/* 性能指标 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card
            title={
              <div style={{ color: '#1890ff' }}>
                <RocketOutlined style={{ marginRight: '8px' }} />
                今日请求进度
              </div>
            }
            style={{
              borderRadius: '16px',
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <Statistic
              value={overview.todayRequests || 0}
              valueStyle={{ color: '#1890ff', fontSize: 32 }}
              suffix="次"
            />
            <div style={{ marginTop: 16 }}>
              <Progress
                percent={Math.min(100, ((overview.todayRequests || 0) / 1000) * 100)}
                strokeColor={{
                  '0%': '#108ee9',
                  '100%': '#87d068',
                }}
                showInfo={false}
              />
              <p style={{ margin: 0, fontSize: 12, color: '#666', marginTop: '8px' }}>
                相对于1000次请求的进度 (目标达成率: {Math.min(100, ((overview.todayRequests || 0) / 1000) * 100).toFixed(1)}%)
              </p>
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card
            title={
              <div style={{ color: '#1890ff' }}>
                <HeartOutlined style={{ marginRight: '8px' }} />
                服务健康度
              </div>
            }
            style={{
              borderRadius: '16px',
              background: 'rgba(255, 255, 255, 0.95)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
            }}
            bodyStyle={{ padding: '24px' }}
          >
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '48px', marginBottom: '16px' }}>
                {serverStatus === 'online' ? '💚' : '❤️‍🩹'}
              </div>
              <Title level={3} style={{ margin: 0, color: getStatusColor() }}>
                {serverStatus === 'online' ? '健康' : '异常'}
              </Title>
              <Text type="secondary">
                {serverStatus === 'online' ? '所有系统正常运行' : '请检查服务状态'}
              </Text>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
