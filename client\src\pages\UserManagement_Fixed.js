import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Popconfirm, 
  Space, 
  Tag, 
  Typography,
  Alert,
  Divider
} from 'antd';
import { 
  UserAddOutlined, 
  DeleteOutlined, 
  KeyOutlined, 
  SecurityScanOutlined,
  ExclamationCircleOutlined,
  CrownOutlined
} from '@ant-design/icons';
import api from '../services/api';
import NotificationHelper from '../components/NotificationHelper';

const { Title, Text } = Typography;

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [form] = Form.useForm();
  const [passwordForm] = Form.useForm();

  // 检查当前用户是否为 xiya
  const currentUser = JSON.parse(localStorage.getItem('user_info') || '{}');
  const isXiya = currentUser.username === 'xiya';

  useEffect(() => {
    if (isXiya) {
      loadUsers();
    }
  }, [isXiya]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const response = await api.get('/user-management/users');
      
      if (response.data && response.data.success) {
        setUsers(response.data.data || []);
        NotificationHelper.success('加载成功', '用户列表加载完成');
      } else {
        NotificationHelper.error('加载失败', response.data?.message || '未知错误');
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      NotificationHelper.networkError(error.response?.status || 500, '获取用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAddUser = async (values) => {
    try {
      const response = await api.post('/user-management/users', values);
      if (response.data.success) {
        NotificationHelper.operationSuccess('create', '管理员用户创建成功');
        setModalVisible(false);
        form.resetFields();
        loadUsers();
      } else {
        NotificationHelper.error('创建失败', response.data.message);
      }
    } catch (error) {
      NotificationHelper.networkError(error.response?.status || 500, '创建用户失败');
    }
  };

  const handleDeleteUser = async (userId) => {
    try {
      const response = await api.delete(`/user-management/users/${userId}`);
      if (response.data.success) {
        NotificationHelper.operationSuccess('delete', '用户删除成功');
        loadUsers();
      } else {
        NotificationHelper.error('删除失败', response.data.message);
      }
    } catch (error) {
      NotificationHelper.networkError(error.response?.status || 500, '删除用户失败');
    }
  };

  const handleResetPassword = async (values) => {
    try {
      const response = await api.put(`/user-management/users/${selectedUser.id}/password`, values);
      if (response.data.success) {
        NotificationHelper.operationSuccess('update', '密码重置成功');
        setPasswordModalVisible(false);
        passwordForm.resetFields();
        setSelectedUser(null);
      } else {
        NotificationHelper.error('重置失败', response.data.message);
      }
    } catch (error) {
      NotificationHelper.networkError(error.response?.status || 500, '重置密码失败');
    }
  };

  const handleReset2FA = async (userId, username) => {
    try {
      const response = await api.delete(`/user-management/users/${userId}/2fa`);
      if (response.data.success) {
        NotificationHelper.operationSuccess('unbind', `${username} 的二步验证已重置`);
        loadUsers();
      } else {
        NotificationHelper.error('重置失败', response.data.message);
      }
    } catch (error) {
      NotificationHelper.networkError(error.response?.status || 500, '重置2FA失败');
    }
  };

  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text, record) => (
        <Space>
          {text === 'xiya' && <CrownOutlined style={{ color: '#faad14' }} />}
          <Text strong={text === 'xiya'}>{text}</Text>
          {text === 'xiya' && <Tag color="gold">超级管理员</Tag>}
        </Space>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => (
        <Tag color="blue" icon={<SecurityScanOutlined />}>
          {role === 'admin' ? '管理员' : role}
        </Tag>
      ),
    },
    {
      title: '二步验证',
      dataIndex: 'is_2fa_enabled',
      key: 'is_2fa_enabled',
      render: (enabled) => (
        <Tag color={enabled ? 'green' : 'default'}>
          {enabled ? '已启用' : '未启用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '最后登录',
      dataIndex: 'last_login',
      key: 'last_login',
      render: (date) => date ? new Date(date).toLocaleString('zh-CN') : '从未登录',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="primary"
            size="small"
            icon={<KeyOutlined />}
            onClick={() => {
              setSelectedUser(record);
              setPasswordModalVisible(true);
            }}
          >
            重置密码
          </Button>
          
          {record.is_2fa_enabled && (
            <Popconfirm
              title="重置二步验证"
              description={`确定要重置 ${record.username} 的二步验证吗？`}
              onConfirm={() => handleReset2FA(record.id, record.username)}
              okText="确定"
              cancelText="取消"
            >
              <Button size="small" icon={<SecurityScanOutlined />}>
                重置2FA
              </Button>
            </Popconfirm>
          )}
          
          {record.username !== 'xiya' && (
            <Popconfirm
              title="删除用户"
              description={`确定要删除用户 ${record.username} 吗？此操作不可恢复！`}
              onConfirm={() => handleDeleteUser(record.id)}
              okText="删除"
              cancelText="取消"
              okButtonProps={{ danger: true }}
            >
              <Button 
                danger 
                size="small" 
                icon={<DeleteOutlined />}
              >
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  // 如果不是 xiya 用户，显示权限不足
  if (!isXiya) {
    return (
      <div style={{ padding: '24px' }}>
        <Card>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <ExclamationCircleOutlined style={{ fontSize: '64px', color: '#faad14', marginBottom: '16px' }} />
            <Title level={3}>权限不足</Title>
            <Text type="secondary">此功能仅限 xiya 用户使用</Text>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '8px' }}>
            <CrownOutlined style={{ color: '#faad14' }} />
            用户管理
          </Title>
          <Text type="secondary">管理系统管理员用户，添加和删除管理员账户</Text>
        </div>

        <Alert
          message="安全提示"
          description="此功能具有高级权限，请谨慎操作。删除用户操作不可恢复，请确认后再进行操作。"
          type="warning"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <div style={{ marginBottom: '16px' }}>
          <Button
            type="primary"
            icon={<UserAddOutlined />}
            onClick={() => setModalVisible(true)}
          >
            添加管理员
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: (total) => `共 ${total} 个用户`,
          }}
        />

        {/* 添加用户模态框 */}
        <Modal
          title="添加管理员用户"
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            form.resetFields();
          }}
          footer={null}
          width={500}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleAddUser}
          >
            <Form.Item
              label="用户名"
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3位' },
                { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
              ]}
            >
              <Input placeholder="请输入用户名" />
            </Form.Item>

            <Form.Item
              label="密码"
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位' }
              ]}
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>

            <Form.Item
              label="确认密码"
              name="confirmPassword"
              dependencies={['password']}
              rules={[
                { required: true, message: '请确认密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password placeholder="请再次输入密码" />
            </Form.Item>

            <Divider />

            <div style={{ textAlign: 'right' }}>
              <Space>
                <Button onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                }}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit">
                  创建用户
                </Button>
              </Space>
            </div>
          </Form>
        </Modal>

        {/* 重置密码模态框 */}
        <Modal
          title={`重置密码 - ${selectedUser?.username}`}
          open={passwordModalVisible}
          onCancel={() => {
            setPasswordModalVisible(false);
            passwordForm.resetFields();
            setSelectedUser(null);
          }}
          footer={null}
          width={400}
        >
          <Form
            form={passwordForm}
            layout="vertical"
            onFinish={handleResetPassword}
          >
            <Form.Item
              label="新密码"
              name="newPassword"
              rules={[
                { required: true, message: '请输入新密码' },
                { min: 6, message: '密码至少6位' }
              ]}
            >
              <Input.Password placeholder="请输入新密码" />
            </Form.Item>

            <Form.Item
              label="确认新密码"
              name="confirmNewPassword"
              dependencies={['newPassword']}
              rules={[
                { required: true, message: '请确认新密码' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (!value || getFieldValue('newPassword') === value) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('两次输入的密码不一致'));
                  },
                }),
              ]}
            >
              <Input.Password placeholder="请再次输入新密码" />
            </Form.Item>

            <Divider />

            <div style={{ textAlign: 'right' }}>
              <Space>
                <Button onClick={() => {
                  setPasswordModalVisible(false);
                  passwordForm.resetFields();
                  setSelectedUser(null);
                }}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit">
                  重置密码
                </Button>
              </Space>
            </div>
          </Form>
        </Modal>
      </Card>
    </div>
  );
};

export default UserManagement;
