#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const colors = require('colors');
const mysqlConnection = require('../config/mysql');

/**
 * MySQL数据库初始化脚本
 */
class MySQLDatabaseInit {
    constructor() {
        this.sqlFile = path.join(__dirname, 'mysql-init.sql');
    }

    /**
     * 初始化数据库
     */
    async init() {
        try {
            console.log('🚀 开始初始化MySQL数据库...'.cyan);
            
            // 检查SQL文件是否存在
            if (!fs.existsSync(this.sqlFile)) {
                throw new Error(`SQL初始化文件不存在: ${this.sqlFile}`);
            }

            // 读取SQL文件
            const sqlContent = fs.readFileSync(this.sqlFile, 'utf8');
            
            // 连接数据库
            await mysqlConnection.init();
            
            // 分割SQL语句（按分号分割，忽略注释）
            const sqlStatements = this.parseSQLStatements(sqlContent);
            
            console.log(`📝 准备执行 ${sqlStatements.length} 条SQL语句...`.yellow);
            
            // 执行SQL语句
            for (let i = 0; i < sqlStatements.length; i++) {
                const sql = sqlStatements[i].trim();
                if (sql) {
                    try {
                        await mysqlConnection.query(sql);
                        console.log(`✅ [${i + 1}/${sqlStatements.length}] 执行成功`.green);
                    } catch (error) {
                        console.error(`❌ [${i + 1}/${sqlStatements.length}] 执行失败:`.red, error.message);
                        console.error(`SQL: ${sql.substring(0, 100)}...`.gray);
                        // 继续执行其他语句，不中断
                    }
                }
            }
            
            // 验证表是否创建成功
            await this.verifyTables();
            
            console.log('🎉 MySQL数据库初始化完成！'.green);
            
        } catch (error) {
            console.error('❌ MySQL数据库初始化失败:'.red, error.message);
            throw error;
        }
    }

    /**
     * 解析SQL语句
     */
    parseSQLStatements(sqlContent) {
        // 移除注释
        const cleanSQL = sqlContent
            .replace(/--.*$/gm, '') // 移除单行注释
            .replace(/\/\*[\s\S]*?\*\//g, ''); // 移除多行注释
        
        // 按分号分割，但要考虑字符串中的分号
        const statements = [];
        let currentStatement = '';
        let inString = false;
        let stringChar = '';
        
        for (let i = 0; i < cleanSQL.length; i++) {
            const char = cleanSQL[i];
            const prevChar = i > 0 ? cleanSQL[i - 1] : '';
            
            if (!inString && (char === '"' || char === "'")) {
                inString = true;
                stringChar = char;
            } else if (inString && char === stringChar && prevChar !== '\\') {
                inString = false;
                stringChar = '';
            } else if (!inString && char === ';') {
                if (currentStatement.trim()) {
                    statements.push(currentStatement.trim());
                }
                currentStatement = '';
                continue;
            }
            
            currentStatement += char;
        }
        
        // 添加最后一个语句（如果有）
        if (currentStatement.trim()) {
            statements.push(currentStatement.trim());
        }
        
        return statements.filter(stmt => stmt.length > 0);
    }

    /**
     * 验证表是否创建成功
     */
    async verifyTables() {
        const expectedTables = [
            'sign_logs', 'users', 'settings', 'api_configs',
            'blacklist', 'whitelist', 'risk_keywords', 'risk_records',
            'unauthorized_logs', 'daily_stats', 'message_logs'
        ];
        
        console.log('🔍 验证表结构...'.yellow);
        
        for (const tableName of expectedTables) {
            try {
                const result = await mysqlConnection.query(
                    'SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?',
                    [tableName]
                );
                
                if (result[0].count > 0) {
                    console.log(`✅ 表 ${tableName} 创建成功`.green);
                } else {
                    console.log(`❌ 表 ${tableName} 创建失败`.red);
                }
            } catch (error) {
                console.log(`❌ 验证表 ${tableName} 时出错:`.red, error.message);
            }
        }
    }

    /**
     * 检查数据库是否已初始化
     */
    async isInitialized() {
        try {
            await mysqlConnection.init();
            
            // 检查关键表是否存在
            const result = await mysqlConnection.query(
                'SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?',
                ['sign_logs']
            );
            
            return result[0].count > 0;
        } catch (error) {
            return false;
        }
    }

    /**
     * 重置数据库（危险操作）
     */
    async reset() {
        try {
            console.log('⚠️  警告：即将重置数据库，所有数据将被删除！'.red);
            
            await mysqlConnection.init();
            
            // 获取所有表
            const tables = await mysqlConnection.query(
                'SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE()'
            );
            
            // 禁用外键检查
            await mysqlConnection.query('SET FOREIGN_KEY_CHECKS = 0');
            
            // 删除所有表
            for (const table of tables) {
                await mysqlConnection.query(`DROP TABLE IF EXISTS ${table.table_name}`);
                console.log(`🗑️  删除表: ${table.table_name}`.gray);
            }
            
            // 启用外键检查
            await mysqlConnection.query('SET FOREIGN_KEY_CHECKS = 1');
            
            console.log('🧹 数据库重置完成'.yellow);
            
        } catch (error) {
            console.error('❌ 数据库重置失败:'.red, error.message);
            throw error;
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const init = new MySQLDatabaseInit();
    
    (async () => {
        try {
            const args = process.argv.slice(2);
            
            if (args.includes('--reset')) {
                await init.reset();
                await init.init();
            } else if (args.includes('--check')) {
                const isInit = await init.isInitialized();
                console.log(`数据库初始化状态: ${isInit ? '已初始化' : '未初始化'}`);
            } else {
                const isInit = await init.isInitialized();
                if (!isInit) {
                    await init.init();
                } else {
                    console.log('✅ 数据库已经初始化，跳过初始化步骤'.green);
                }
            }
            
            process.exit(0);
        } catch (error) {
            console.error('脚本执行失败:'.red, error.message);
            process.exit(1);
        }
    })();
}

module.exports = MySQLDatabaseInit;
