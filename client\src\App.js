import React, { useState, useEffect } from 'react';
import { Layout, Menu, theme, Button, Dropdown, Avatar, notification, App as AntdApp } from 'antd';
import {
  DashboardOutlined,
  FileTextOutlined,
  BarChartOutlined,
  SecurityScanOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  ApiOutlined,
  ExperimentOutlined,
  SafetyOutlined,
  GlobalOutlined,
  CrownOutlined
} from '@ant-design/icons';
import Dashboard from './pages/Dashboard';
import SignLogs from './pages/SignLogs';
import UnauthorizedLogs from './pages/UnauthorizedLogs';
import Statistics from './pages/Statistics';
import Settings from './pages/Settings';
import ApiDocs from './pages/ApiDocs';
import QimeiTest from './pages/QimeiTest';
import RiskControl from './pages/RiskControl';
import WebSocketManagement from './pages/WebSocketManagement';
import UserManagement from './pages/UserManagement';
import Login from './components/Login';
import SignNodeManager from './pages/SignNodeManager';
import TokenManager from './pages/TokenManager';
import SignatureMonitor from './pages/SignatureMonitor';
import PublicNodeStatus from './pages/PublicNodeStatus';
import { authAPI } from './services/api';
import NotificationHelper from './components/NotificationHelper';
import './App.css';

const { Header, Content, Sider } = Layout;

const menuItems = [
  {
    key: 'dashboard',
    icon: <DashboardOutlined />,
    label: '仪表板',
  },
  {
    key: 'signLogs',
    icon: <FileTextOutlined />,
    label: '签名日志',
  },
  {
    key: 'unauthorizedLogs',
    icon: <SecurityScanOutlined />,
    label: '未授权访问',
  },
  {
    key: 'statistics',
    icon: <BarChartOutlined />,
    label: '数据统计',
  },
  {
    key: 'riskControl',
    icon: <SafetyOutlined />,
    label: '风控管理',
  },
  {
    key: 'websocketManagement',
    icon: <GlobalOutlined />,
    label: '连接管理',
  },
  {
    key: 'qimeiGenerator',
    icon: <ExperimentOutlined />,
    label: 'QIMEI生成器',
  },
  {
    key: 'apiDocs',
    icon: <ApiOutlined />,
    label: 'API文档',
  },
  {
    key: 'settings',
    icon: <SettingOutlined />,
    label: '系统设置',
  },
  {
    key: 'signNodeManager',
    icon: <ApiOutlined />,
    label: '签名服务节点',
  },
  {
    key: 'signatureMonitor',
    icon: <DashboardOutlined />,
    label: '签名服务监控',
  },
  {
    key: 'tokenManager',
    icon: <CrownOutlined />,
    label: 'Token管理',
  },
];

function App() {
  const [selectedKey, setSelectedKey] = useState('dashboard');
  const [collapsed, setCollapsed] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const {
    token: { colorBgContainer },
  } = theme.useToken();

  // 配置notification全局设置
  useEffect(() => {
    notification.config({
      placement: 'topRight',
      top: 50,
      duration: 4.5,
      rtl: false,
      maxCount: 3,
      getContainer: () => document.body,
    });
  }, []);

  // 检查登录状态
  useEffect(() => {
    checkAuthStatus();
  }, []);
  
  // 监听URL hash变化以处理路由
  useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.replace('#', '');

      // 检查是否是公开页面
      if (hash === 'public-status') {
        setSelectedKey(hash);
        console.log(`🔀 访问公开页面: ${hash}`);
        return;
      }

      const currentMenuItems = getMenuItems();
      if (hash && currentMenuItems.some(item => item.key === hash)) {
        setSelectedKey(hash);
        console.log(`🔀 从URL hash设置活动菜单: ${hash}`);
      }
    };

    // 初始检查
    handleHashChange();

    // 监听hash变化
    window.addEventListener('hashchange', handleHashChange);

    return () => {
      window.removeEventListener('hashchange', handleHashChange);
    };
  }, []);

  const checkAuthStatus = async () => {
    const token = localStorage.getItem('auth_token');
    if (!token) {
      setLoading(false);
      return;
    }

    try {
      const response = await authAPI.verify();
      const responseData = response.data || response;
      
      if (responseData && responseData.code === 0) {
        setIsAuthenticated(true);
        setUser(responseData.data.user);
        localStorage.setItem('user_info', JSON.stringify(responseData.data.user));
      } else {
        // 只有在明确认证失败时才清除token
        if (responseData && responseData.code === 401) {
          console.log('Token已过期，清除本地认证信息');
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user_info');
          setIsAuthenticated(false);
          setUser(null);
        } else {
          // 其他情况下保持认证状态，可能是网络问题
          const userInfo = localStorage.getItem('user_info');
          if (userInfo) {
            console.log('网络异常，使用本地用户信息维持登录状态');
            setIsAuthenticated(true);
            setUser(JSON.parse(userInfo));
          }
        }
      }
    } catch (error) {
      console.error('验证失败:', error);
      
      // 根据错误类型决定处理方式
      if (error.response) {
        const status = error.response.status;
        if (status === 401 || status === 403) {
          // 认证失败，清除本地状态
          console.log('认证失败，清除本地状态');
          localStorage.removeItem('auth_token');
          localStorage.removeItem('user_info');
          setIsAuthenticated(false);
          setUser(null);
        } else {
          // 服务器错误，保持本地状态
          const userInfo = localStorage.getItem('user_info');
          if (userInfo) {
            console.log('服务器错误，使用本地用户信息维持登录状态');
            setIsAuthenticated(true);
            setUser(JSON.parse(userInfo));
          }
        }
      } else {
        // 网络错误时使用本地存储的用户信息
        const userInfo = localStorage.getItem('user_info');
        if (userInfo) {
          console.log('网络错误，使用本地用户信息维持登录状态');
          setIsAuthenticated(true);
          setUser(JSON.parse(userInfo));
        }
      }
    } finally {
      setLoading(false);
    }
  };

  const handleLogin = (success) => {
    if (success) {
      console.log('登录成功回调触发');
      
      // 立即设置认证状态
      setIsAuthenticated(true);
      
      // 立即设置用户信息
      const userInfo = localStorage.getItem('user_info');
      if (userInfo) {
        try {
          setUser(JSON.parse(userInfo));
        } catch (err) {
          console.error('解析用户信息失败:', err);
        }
      }
    }
  };

  const handleLogout = async () => {
    try {
      await authAPI.logout();
      NotificationHelper.loginNotification('success', '登出成功~ 再见主人! (｡♥‿♥｡)');
    } catch (error) {
      console.error('登出失败:', error);
    } finally {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user_info');
      setIsAuthenticated(false);
      setUser(null);
    }
  };

  // 动态生成菜单项，根据用户权限显示不同菜单
  const getMenuItems = () => {
    const baseItems = [...menuItems];
    
    // 如果是 xiya 用户，添加用户管理菜单
    if (user && user.username === 'xiya') {
      baseItems.splice(baseItems.length - 1, 0, {
        key: 'userManagement',
        icon: <CrownOutlined />,
        label: '用户管理',
      });
    }
    
    return baseItems;
  };

  const renderContent = () => {
    switch (selectedKey) {
      case 'dashboard':
        return <Dashboard />;
      case 'signLogs':
        return <SignLogs />;
      case 'unauthorizedLogs':
        return <UnauthorizedLogs />;
      case 'statistics':
        return <Statistics />;
      case 'riskControl':
        return <RiskControl />;
      case 'websocketManagement':
        return <WebSocketManagement />;
      case 'qimeiGenerator':
        return <QimeiTest />;
      case 'apiDocs':
        return <ApiDocs />;
      case 'userManagement':
        return <UserManagement />;
      case 'settings':
        return <Settings />;
      case 'signNodeManager':
        return <SignNodeManager />;
      case 'signatureMonitor':
        return <SignatureMonitor />;
      case 'tokenManager':
        return <TokenManager />;
      case 'public-status':
        return <PublicNodeStatus />;
      default:
        return <Dashboard />;
    }
  };

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-emoji">🌸</div>
        <div>加载中，请稍候~ (｡♥‿♥｡)</div>
      </div>
    );
  }

  // 如果未登录，检查是否访问公开页面
  if (!isAuthenticated) {
    // 如果访问的是公开状态页面，直接显示
    if (selectedKey === 'public-status') {
      return (
        <AntdApp>
          <Layout style={{ minHeight: '100vh' }}>
            <Header style={{
              padding: '0 24px',
              background: colorBgContainer,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}>
              <div className="header-title">
                🌸 签名服务节点状态 - 公开页面
              </div>
              <Button type="primary" onClick={() => window.location.hash = ''}>
                返回登录
              </Button>
            </Header>
            <Content style={{ margin: '16px' }}>
              <div className="main-content" style={{
                padding: 24,
                minHeight: 360,
                background: colorBgContainer
              }}>
                <PublicNodeStatus />
              </div>
            </Content>
          </Layout>
        </AntdApp>
      );
    }

    // 其他情况显示登录页面
    return <Login onLogin={handleLogin} />;
  }

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: `${user?.username || '用户'}`,
      disabled: true,
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '登出',
      onClick: handleLogout,
    },
  ];

  return (
    <AntdApp>
      <Layout style={{ minHeight: '100vh' }}>
        <Sider
          collapsible
          collapsed={collapsed}
          onCollapse={setCollapsed}
          theme="dark"
        >
          <div className="sidebar-logo">
            {collapsed ? '🌸' : '🌸 签名服务控制台'}
          </div>
          <Menu
            theme="dark"
            selectedKeys={[selectedKey]}
            mode="inline"
            items={getMenuItems()}
            onClick={({ key }) => setSelectedKey(key)}
            className="anime-menu"
          />
        </Sider>
        <Layout>
          <Header style={{
            padding: '0 24px',
            background: colorBgContainer,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
          }}>
            <div className="header-title">
              签名算法服务端控制台 ✨
            </div>
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              trigger={['click']}
            >
              <div className="header-user-info">
                <Avatar
                  size="small"
                  className="anime-avatar"
                >
                  🌸
                </Avatar>
                <span style={{ color: '#6b46c1', fontWeight: 500 }}>
                  {user?.username || '用户'} ♪(´▽｀)
                </span>
              </div>
            </Dropdown>
          </Header>
          <Content style={{ margin: '16px' }}>
            <div className="main-content" style={{
              padding: 24,
              minHeight: 360,
              background: colorBgContainer
            }}>
              {renderContent()}
            </div>
          </Content>
        </Layout>
      </Layout>
    </AntdApp>
  );
}

export default App;
