const express = require('express');
const axios = require('axios');
const colors = require('colors');
const { riskControlMiddleware, ipRateLimitMiddleware } = require('../middleware/risk-control');
const { nodeSelectorMiddleware } = require('../middleware/nodeSelector');
const { requestValidationMiddleware } = require('../middleware/request-validator');

const { extractUin, extractCmd, getCurrentDate } = require('../utils/helpers');
const configManager = require('../utils/config');
const fetch = require('node-fetch'); // 确保已安装node-fetch
const mysqlDataModel = require('../models/mysql-real-model');
const signatureServiceManager = require('../services/SignatureServiceManager');

const router = express.Router();

// 优化的签名接口 - 使用队列和负载均衡
router.post('/optimized', requestValidationMiddleware, ipRateLimitMiddleware, riskControlMiddleware, async (req, res) => {
    const ip = req.ip || req.socket.remoteAddress;
    const user_agent = req.get('User-Agent') || 'unknown';
    let uin = null;
    let cmd = null;

    try {
        const startTime = Date.now();

        // 解析请求数据
        let requestData = '';
        let parsedParams = {};

        // 处理不同格式的请求体
        if (req.body && typeof req.body === 'object') {
            parsedParams = { ...req.body };
            const params = new URLSearchParams();
            for (const [key, value] of Object.entries(req.body)) {
                params.append(key, value);
            }
            requestData = params.toString();
        } else if (typeof req.body === 'string') {
            requestData = req.body;
            // 解析URL编码参数
            try {
                const urlParams = new URLSearchParams(requestData);
                for (const [key, value] of urlParams) {
                    parsedParams[key] = value;
                }
            } catch (e) {
                console.warn('解析URL参数失败:', e.message);
            }
        }

        // 处理multer解析的multipart/form-data参数
        if (req.files && Array.isArray(req.files)) {
            req.files.forEach(file => {
                if (file.fieldname && file.buffer) {
                    const value = file.buffer.toString('utf8');
                    parsedParams[file.fieldname] = value;

                    if (requestData) {
                        requestData += `&${file.fieldname}=${encodeURIComponent(value)}`;
                    } else {
                        requestData = `${file.fieldname}=${encodeURIComponent(value)}`;
                    }
                }
            });
        }

        // 合并查询参数
        if (req.query) {
            parsedParams = { ...parsedParams, ...req.query };
        }

        // 提取关键信息 - 优先从解析的参数中获取
        uin = parsedParams.uin || extractUin(requestData);
        cmd = parsedParams.cmd || extractCmd(requestData);

        // 验证必要参数
        if (!uin || !cmd) {
            return res.status(400).json({
                code: 400,
                msg: '缺少必要参数 uin 或 cmd'
            });
        }

        // 解析其他参数
        const qua = req.body.qua || req.query.qua || '';
        const buffer = req.body.buffer || req.query.buffer || '';

        // 确定请求优先级
        let priority = 'normal';
        if (cmd === 'trpc.login.ecdh.EcdhService.SsoNTLoginPasswordLoginUnusualDevice' ||
            cmd === 'trpc.login.ecdh.EcdhService.SsoNTLoginPasswordLogin') {
            priority = 'high'; // 登录请求高优先级
        }

        console.log(`📝 优化签名请求: UIN=${uin}, CMD=${cmd}, Priority=${priority}`.cyan);

        // 使用优化的签名服务
        const result = await signatureServiceManager.requestSignature(cmd, uin, qua, buffer, priority);

        const responseTime = Date.now() - startTime;

        // 记录日志
        await mysqlDataModel.logSignRequest({
            ip,
            user_agent,
            uin,
            cmd,
            success: true,
            response_time_ms: responseTime,
            node_id: result.nodeId,
            request_data: requestData.substring(0, 500), // 限制长度
            response_data: JSON.stringify(result.data).substring(0, 500)
        });

        console.log(`✅ 优化签名成功: UIN=${uin}, 响应时间=${responseTime}ms, 节点=${result.nodeId}`.green);

        // 返回结果
        res.json({
            code: 0,
            msg: 'success',
            data: result.data,
            meta: {
                nodeId: result.nodeId,
                responseTime: responseTime,
                queueTime: result.queueTime || 0
            }
        });

    } catch (error) {
        const responseTime = Date.now() - startTime;

        console.error(`❌ 优化签名失败: UIN=${uin}, 错误=${error.message}`.red);

        // 记录错误日志
        try {
            await mysqlDataModel.logSignRequest({
                ip,
                user_agent,
                uin,
                cmd,
                success: false,
                response_time_ms: responseTime,
                error_message: error.message,
                request_data: requestData.substring(0, 500)
            });
        } catch (logError) {
            console.error('记录错误日志失败:', logError);
        }

        res.status(500).json({
            code: 500,
            msg: error.message || '签名失败',
            error: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

// 签名接口 - /api/sign - 按照接口文档规范
router.post('/sign', requestValidationMiddleware, ipRateLimitMiddleware, riskControlMiddleware, async (req, res) => {
    const ip = req.ip || req.socket.remoteAddress;
    const user_agent = req.get('User-Agent') || 'unknown';
    let uin = null;
    let cmd = null;
    const startTime = Date.now(); // 移到外层作用域

    try {

        // 解析请求数据
        let requestData = '';
        if (req.body && typeof req.body === 'object') {
            const params = new URLSearchParams();
            for (const [key, value] of Object.entries(req.body)) {
                params.append(key, value);
            }
            requestData = params.toString();
        } else if (typeof req.body === 'string') {
            requestData = req.body;
        }

        // 数据处理 - 保持向后兼容
        let processedData = String(requestData);
        processedData = processedData.replace(/buffer/g, "data");
        processedData = processedData.replace(/(\&|^)seq=/g, "$1ssoseq=");
        processedData = processedData.replace(/&android_id=/g, "&type=getSign&android_id=");
        processedData = processedData.replace(/b'/g, '');
        processedData = processedData.replace(/'/g, "");

        // 提取关键信息
        uin = extractUin(processedData, '/sign');
        cmd = extractCmd(processedData, '/sign');

        // 验证必要参数
        if (!uin || !cmd) {
            return res.status(400).json({
                code: 400,
                msg: '缺少必要参数 uin 或 cmd'
            });
        }

        // 解析其他参数
        const urlParams = new URLSearchParams(processedData);
        const qua = urlParams.get('qua') || req.body.qua || req.query.qua || '';
        const buffer = urlParams.get('data') || urlParams.get('buffer') || req.body.buffer || req.query.buffer || '';

        // 确定请求优先级
        let priority = 'normal';
        if (cmd && (cmd.includes('Login') || cmd.includes('login'))) {
            priority = 'high'; // 登录请求高优先级
        }

        console.log(`📝 签名请求: UIN=${uin}, CMD=${cmd}, Priority=${priority}`.cyan);

        // 使用优化的签名服务（保持原有转发逻辑）
        const originalFormat = requestData.startsWith('{') ? 'json' : 'form';
        const signResult = await signatureServiceManager.requestSignature(processedData, originalFormat, priority);

        const responseTime = Date.now() - startTime;

        // 记录日志
        await mysqlDataModel.logSignRequest({
            ip,
            user_agent,
            uin,
            cmd,
            type: signResult.nodeId, // 使用节点ID作为type
            endpoint: '/sign',
            success: true,
            error_msg: null,
            response_time_ms: responseTime,
            request_data: requestData.substring(0, 500),
            response_data: JSON.stringify(signResult).substring(0, 500)
        });

        console.log(`✅ 签名成功: UIN=${uin}, 响应时间=${responseTime}ms, 节点=${signResult.nodeId}`.green);

        // 返回结果（保持向后兼容的格式）
        const responseData = {
            code: signResult.data.code || 0,
            msg: signResult.nodeMessage || 'success',
            data: signResult.data.data || signResult.data,
            // 兼容旧格式 - 直接展开签名数据到根级别
            ...(signResult.data.data || signResult.data)
        };

        console.log(`📤 返回响应: ${JSON.stringify(responseData).substring(0, 200)}...`.gray);
        res.json(responseData);

    } catch (error) {
        const responseTime = Date.now() - startTime;

        console.error(`❌ 签名失败: UIN=${uin}, 错误=${error.message}`.red);

        // 记录错误日志
        try {
            await mysqlDataModel.logSignRequest({
                ip,
                user_agent,
                uin,
                cmd,
                type: 'optimized_service', // 标识为优化服务
                endpoint: '/sign',
                success: false,
                error_msg: error.message,
                response_time_ms: responseTime,
                request_data: requestData.substring(0, 500),
                response_data: null
            });
        } catch (logError) {
            console.error('记录错误日志失败:', logError);
        }

        res.status(500).json({
            code: 500,
            msg: error.message || '签名失败',
            error: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

// Energy接口 - /api/energy - 按照接口文档规范
router.all('/energy', requestValidationMiddleware, nodeSelectorMiddleware, async (req, res) => {
    const ip = req.ip || req.connection.remoteAddress;
    const user_agent = req.get('User-Agent') || 'unknown';
    const typeValue = req.query.Type;
    const startTime = Date.now(); // 记录开始时间 - 移到外层作用域
    let uin = null;
    let cmd = null;

    // 从请求体或查询参数中获取数据
    const requestData = req.method === 'POST' ? req.body : req.query;

    try {

        const cmdData = requestData.data.split("_");
        const data = `cmd=${requestData.data}&data=000000000010${requestData.guid.toUpperCase()}000A${Buffer.from(requestData.version).toString('hex').toUpperCase()}0000000${cmdData[1]}00000000&type=energy`;
          // 提取信息
        uin = requestData.uin;
        cmd = requestData.cmd; // 使用原始的data作为cmd

        // 从选中的节点获取baseURL
        const selectedNode = req.selectedNode;
        const baseUrl = selectedNode.base_url;
        const nodeType = selectedNode.client_type;

        if (!baseUrl) {
            const errorMsg = "选中的节点配置无效";
            await mysqlDataModel.logSignRequest({
                ip, uin, cmd, type: nodeType, endpoint: '/energy',
                success: false, error_msg: errorMsg,
                request_data: JSON.stringify(requestData), response_data: null, user_agent,
                response_time_ms: Date.now() - startTime
            });
            return res.json({ code: 1, msg: errorMsg });
        }

        const headers = { "Content-Type": "application/x-www-form-urlencoded" };

        // 确保请求发送到正确的energy端点
        let targetUrl = baseUrl;
        if (!targetUrl.includes('/energy') && !targetUrl.includes('/api')) {
            // 如果baseUrl是根URL，添加energy端点
            targetUrl = targetUrl.endsWith('/') ? targetUrl + 'energy' : targetUrl + '/energy';
        }

        // 发送请求到签名服务器
        const response = await axios.post(targetUrl, data, {
            headers,
            timeout: 30000, // 30秒超时
            transformRequest: [(data) => {
                // 不进行任何转换，直接发送字符串
                return data;
            }]
        });

        const result = response.data;

        // 处理不同的响应格式 - 优先使用result字段
        let resultData;
        if (result.result !== undefined) {
            // 优先使用 {result: "..."}
            resultData = result.result;
        } else if (result.data !== undefined) {
            // 如果没有result，使用 {data: "..."}
            resultData = result.data;
        } else {
            // 如果都没有，直接返回整个响应
            resultData = result;
        }

        const responseData = {
            code: 0,
            msg: `当前签名服务端: ${nodeType}`,
            data: resultData
        };        // 记录成功请求到文件日志
        await mysqlDataModel.logSignRequest({
            ip, uin, cmd, type: nodeType, endpoint: '/energy',
            success: true, error_msg: null,
            request_data: JSON.stringify(requestData),
            response_data: JSON.stringify({
                ...responseData,
                node: nodeType,
                token: req.nodeToken.substring(0, 8) + '...'
            }),
            user_agent,
            response_time_ms: Date.now() - startTime
        });

        // MySQL会自动维护统计信息，无需手动更新
        res.json(responseData);

    } catch (error) {
        console.error(`❌ /energy 接口出错:`.red, error.message);

        const errorResponse = { code: 3, msg: "服务器内部错误" };

        await mysqlDataModel.logSignRequest({
            ip, uin, cmd, type: req.selectedNode?.client_type || 'unknown', endpoint: '/energy',
            success: false, error_msg: error.message,
            request_data: JSON.stringify(requestData),
            response_data: JSON.stringify({
                ...errorResponse,
                node: req.selectedNode?.client_type || 'unknown',
                token: req.nodeToken ? req.nodeToken.substring(0, 8) + '...' : 'unknown'
            }),
            user_agent,
            response_time_ms: Date.now() - startTime
        });

        // MySQL会自动维护统计信息，无需手动更新
        res.json(errorResponse);
    }
});

// Get0553接口 - /api/Get0553 - 按照接口文档规范
router.all('/Get0553', requestValidationMiddleware, nodeSelectorMiddleware, async (req, res) => {
    const ip = req.ip || req.connection.remoteAddress;
    const user_agent = req.get('User-Agent') || 'unknown';
    const typeValue = req.query.Type;
    const startTime = Date.now(); // 记录开始时间 - 移到外层作用域
      // 从请求体或查询参数中获取数据
    const requestData = req.method === 'POST' ? req.body : req.query;
    let uin = requestData.uin;
    let cmd = requestData.cmd;

    try {
        const data = `cmd=${requestData.cmd}&uin=${requestData.uin}&data=${requestData.data}&ssoseq=${requestData.ssoseq}&type=getXwDebugID`;

        // 从选中的节点获取baseURL
        const selectedNode = req.selectedNode;
        const baseUrl = selectedNode.base_url;
        const nodeType = selectedNode.client_type;

        console.log(`使用节点: ${nodeType} - ${baseUrl}`.cyan);
        if (!baseUrl) {
            const errorMsg = "选中的节点配置无效";
            await mysqlDataModel.logSignRequest({
                ip, uin, cmd, type: nodeType, endpoint: '/Get0553',
                success: false, error_msg: errorMsg,
                request_data: JSON.stringify(requestData), response_data: null, user_agent,
                response_time_ms: Date.now() - startTime
            });            return res.json({ code: 1, msg: errorMsg });
        }

        const headers = { "Content-Type": "application/x-www-form-urlencoded" };

        // 确保请求发送到正确的Get0553端点
        let targetUrl = baseUrl;
        if (!targetUrl.includes('/Get0553') && !targetUrl.includes('/api')) {
            // 如果baseUrl是根URL，添加Get0553端点
            targetUrl = targetUrl.endsWith('/') ? targetUrl + 'Get0553' : targetUrl + '/Get0553';
        }

        // 发送请求到签名服务器
        const response = await axios.post(targetUrl, data, {
            headers,
            timeout: 30000, // 30秒超时
            transformRequest: [(data) => {
                // 不进行任何转换，直接发送字符串
                return data;
            }]
        });

        const result = response.data;

        // 处理不同的响应格式 - 优先使用result字段
        let resultData;
        if (result.result !== undefined) {
            // 优先使用 {result: "..."}
            resultData = result.result;
        } else if (result.data !== undefined) {
            // 如果没有result，使用 {data: "..."}
            resultData = result.data;
        } else {
            // 如果都没有，直接返回整个响应
            resultData = result;
        }

        const responseData = {
            code: 0,
            msg: `当前签名服务端: ${nodeType}`,
            data: resultData
        };// 记录成功请求到文件日志
        await mysqlDataModel.logSignRequest({
            ip, uin, cmd, type: nodeType, endpoint: '/Get0553',
            success: true, error_msg: null,
            request_data: JSON.stringify(requestData),
            response_data: JSON.stringify({
                ...responseData,
                node: nodeType,
                token: req.nodeToken.substring(0, 8) + '...'
            }),
            user_agent,
            response_time_ms: Date.now() - startTime
        });

        // MySQL会自动维护统计信息，无需手动更新
        res.json(responseData);

    } catch (error) {
        console.error(`❌ /Get0553 接口出错:`.red, error.message);

        const errorResponse = { code: 3, msg: "服务器内部错误" };

        await mysqlDataModel.logSignRequest({
            ip, uin, cmd, type: req.selectedNode?.client_type || 'unknown', endpoint: '/Get0553',
            success: false, error_msg: error.message,
            request_data: JSON.stringify(requestData),
            response_data: JSON.stringify({
                ...errorResponse,
                node: req.selectedNode?.client_type || 'unknown',
                token: req.nodeToken ? req.nodeToken.substring(0, 8) + '...' : 'unknown'
            }),
            user_agent,
            response_time_ms: Date.now() - startTime
        });

        // MySQL会自动维护统计信息，无需手动更新
        res.json(errorResponse);
    }
});

// 节点检测接口
router.post('/nodes/check', async (req, res) => {
  const { base_url } = req.body;
  if (!base_url) return res.status(400).json({ code: 400, msg: '缺少base_url参数' });

  // 直接访问节点根路由进行健康检查
  const url = base_url.replace(/\/$/, '');
  const start = Date.now();

  try {
    const resp = await fetch(url, {
      method: 'GET',
      timeout: 5000,
      headers: {
        'User-Agent': 'QsignHook-HealthCheck/1.0'
      }
    });

    const latency = Date.now() - start;
    let responseData = null;

    try {
      const text = await resp.text();
      // 尝试解析为JSON，如果失败就保留原文本
      try {
        responseData = JSON.parse(text);
      } catch {
        responseData = text.substring(0, 200); // 限制长度
      }
    } catch (textError) {
      responseData = 'Unable to read response';
    }

    // 只要有响应就认为节点健康
    res.json({
      code: 0,
      msg: '节点健康检查成功',
      data: {
        latency,
        status: resp.status,
        healthy: true,
        response: responseData,
        url: url
      }
    });

  } catch (err) {
    const latency = Date.now() - start;
    res.json({
      code: -1,
      msg: '节点检测失败',
      data: {
        error: err.message,
        latency,
        healthy: false,
        url: url
      }
    });
  }
});

// 签名服务状态监控接口
router.get('/sign/status', async (req, res) => {
    try {
        const status = signatureServiceManager.getStatus();
        res.json({
            code: 0,
            msg: 'success',
            data: status
        });
    } catch (error) {
        res.status(500).json({
            code: 500,
            msg: error.message
        });
    }
});

// 签名服务性能统计接口
router.get('/sign/performance', async (req, res) => {
    try {
        const stats = signatureServiceManager.getPerformanceStats();
        res.json({
            code: 0,
            msg: 'success',
            data: stats
        });
    } catch (error) {
        res.status(500).json({
            code: 500,
            msg: error.message
        });
    }
});

// 签名服务健康检查接口
router.get('/sign/health', async (req, res) => {
    try {
        const health = await signatureServiceManager.healthCheck();
        res.json({
            code: 0,
            msg: 'success',
            data: health
        });
    } catch (error) {
        res.status(500).json({
            code: 500,
            msg: error.message
        });
    }
});

// 重新加载节点配置接口
router.post('/sign/reload-nodes', async (req, res) => {
    try {
        const nodeCount = await signatureServiceManager.reloadNodes();
        res.json({
            code: 0,
            msg: `成功重新加载 ${nodeCount} 个节点`,
            data: { nodeCount }
        });
    } catch (error) {
        res.status(500).json({
            code: 500,
            msg: error.message
        });
    }
});

module.exports = router;
