// 测试NotificationHelper的工具
import NotificationHelper from '../components/NotificationHelper';

export const testNotifications = () => {
  console.log('🧪 开始测试通知系统...');
  
  // 使用新的testAll方法
  NotificationHelper.testAll();
  
  // 测试强制显示
  setTimeout(() => {
    console.log('🔧 测试强制显示通知');
    NotificationHelper.forceShow('success', '强制成功通知', '这是一个强制显示的成功通知，带有红色边框用于调试');
  }, 4000);
    // 测试info通知
  setTimeout(() => {
    console.log('🛠️ 测试info通知');
    NotificationHelper.info('Info通知测试', '这是一个萌系信息通知测试');
  }, 5000);
  
  console.log('🧪 所有测试通知已排队');
};
