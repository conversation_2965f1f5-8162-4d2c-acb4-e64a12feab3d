const WebSocket = require('ws');
const colors = require('colors');
const dataModel = require('../models/mysql-real-model');

/**
 * WebSocket风控服务
 * 处理客户端与服务端的实时通信
 */
class WebSocketRiskService {
  constructor() {
    this.wss = null;
    this.clients = new Map();
    this.port = 12042;
    this.isRunning = false;
  }

  /**
   * 启动WebSocket服务
   */
  async start() {
    try {
      // 从配置表获取端口
      const configPort = await dataModel.getSetting('ws_port', this.port);
      this.port = parseInt(configPort);

      this.wss = new WebSocket.Server({ 
        port: this.port,
        perMessageDeflate: false
      });

      this.wss.on('connection', (ws, req) => {
        this.handleConnection(ws, req);
      });

      this.wss.on('error', (error) => {
        console.error('WebSocket服务器错误:'.red, error);
      });

      this.isRunning = true;
      console.log(`🔗 WebSocket风控服务已启动，监听端口: ${this.port}`.green);
      
      return true;
    } catch (error) {
      console.error('启动WebSocket服务失败:'.red, error);
      return false;
    }
  }

  /**
   * 停止WebSocket服务
   */
  async stop() {
    if (this.wss) {
      this.wss.close();
      this.clients.clear();
      this.isRunning = false;
      console.log('WebSocket风控服务已停止'.yellow);
    }
  }

  /**
   * 处理客户端连接
   */
  handleConnection(ws, req) {
    const clientId = this.generateClientId();
    const clientInfo = {
      id: clientId,
      ip: req.socket.remoteAddress,
      userAgent: req.headers['user-agent'],
      connectedAt: new Date(),
      isAuthenticated: false,
      lastHeartbeat: Date.now()
    };

    this.clients.set(clientId, { ws, info: clientInfo });
    
    console.log(`🔗 客户端连接: ${clientId} (${clientInfo.ip})`.cyan);

    // 发送欢迎消息
    this.sendMessage(ws, {
      type: 'connected',
      clientId: clientId,
      timestamp: new Date().toISOString()
    });

    // 监听消息
    ws.on('message', async (data) => {
      await this.handleMessage(clientId, data);
    });

    // 监听关闭
    ws.on('close', () => {
      this.handleDisconnection(clientId);
    });

    // 监听错误
    ws.on('error', (error) => {
      console.error(`客户端错误 ${clientId}:`.red, error);
      this.handleDisconnection(clientId);
    });

    // 启动心跳检测
    this.startHeartbeat(clientId);
  }

  /**
   * 处理客户端消息
   */
  async handleMessage(clientId, data) {
    try {
      const message = JSON.parse(data.toString());
      const client = this.clients.get(clientId);
      
      if (!client) return;

      // 更新心跳时间
      client.info.lastHeartbeat = Date.now();

      switch (message.type) {
        case 'auth':
          await this.handleAuth(clientId, message);
          break;
        
        case 'risk_check':
          await this.handleRiskCheck(clientId, message);
          break;
        
        case 'heartbeat':
          this.sendMessage(client.ws, { type: 'heartbeat_ack', timestamp: new Date().toISOString() });
          break;
        
        case 'get_risk_stats':
          await this.handleGetRiskStats(clientId);
          break;
        
        default:
          console.warn(`未知消息类型: ${message.type}`.yellow);
      }
    } catch (error) {
      console.error(`处理消息失败 ${clientId}:`.red, error);
      this.sendError(clientId, 'INVALID_MESSAGE', '消息格式错误');
    }
  }

  /**
   * 处理客户端认证
   */
  async handleAuth(clientId, message) {
    try {
      const { authKey } = message.data || {};
      const client = this.clients.get(clientId);
      
      if (!client) return;

      // 验证认证密钥
      const storedAuthKey = await dataModel.getAuthKey();
      const isAuthEnabled = await dataModel.isAuthEnabled();
        if (isAuthEnabled && authKey !== storedAuthKey) {
        this.sendError(clientId, 'AUTH_FAILED', '认证失败');
        return;
      }      // 先标记为已认证
      client.info.isAuthenticated = true;
      
      this.sendMessage(client.ws, {
        type: 'auth_success',
        data: {
          clientId: clientId,
          serverTime: new Date().toISOString()
        }
      });

      // 使用 setTimeout 确保认证成功消息先发送
      setTimeout(async () => {
        try {
          await this.sendSensitiveWords(clientId);
        } catch (error) {
          console.error(`延迟发送敏感词库失败 ${clientId}:`.red, error);
        }
      }, 100);

      console.log(`✅ 客户端认证成功: ${clientId}`.green);
    } catch (error) {
      console.error(`认证处理失败 ${clientId}:`.red, error);
      this.sendError(clientId, 'AUTH_ERROR', '认证处理异常');
    }
  }

  /**
   * 处理风控检测请求
   */
  async handleRiskCheck(clientId, message) {
    try {
      const client = this.clients.get(clientId);
      
      if (!client || !client.info.isAuthenticated) {
        this.sendError(clientId, 'UNAUTHORIZED', '未认证');
        return;
      }

      const { content, clientInfo: requestClientInfo = {} } = message.data || {};
      
      if (!content) {
        this.sendError(clientId, 'INVALID_CONTENT', '检测内容不能为空');
        return;
      }

      // 合并客户端信息
      const fullClientInfo = {
        ...requestClientInfo,
        websocket_client_id: clientId,
        websocket_ip: client.info.ip,
        websocket_user_agent: client.info.userAgent
      };

      // 检查白名单
      const isWhitelisted = await dataModel.isWhitelisted(fullClientInfo);
      if (isWhitelisted) {
        this.sendMessage(client.ws, {
          type: 'risk_result',
          data: {
            requestId: message.requestId,
            isRisk: false,
            isWhitelisted: true,
            action: 'allow',
            message: '白名单用户，允许通过'
          }
        });
        return;
      }

      // 检查黑名单
      const blacklistResult = await dataModel.isBlacklisted(fullClientInfo);
      if (blacklistResult.isBlacklisted) {
        this.sendMessage(client.ws, {
          type: 'risk_result',
          data: {
            requestId: message.requestId,
            isRisk: true,
            isBlacklisted: true,
            action: 'block',
            message: '黑名单用户，禁止访问',
            banInfo: blacklistResult.record
          }
        });
        return;
      }

      // 风控内容检测
      const riskResult = await dataModel.checkRiskContent(content, fullClientInfo);
      
      // 记录风控数据
      if (riskResult.isRisk) {
        await dataModel.recordRiskData({
          username: requestClientInfo.username,
          device_id: requestClientInfo.device_id,
          ip_address: client.info.ip,
          uin: requestClientInfo.uin,
          risk_content: content,
          matched_keywords: riskResult.matchedKeywords,
          risk_score: riskResult.riskScore,
          risk_level: riskResult.riskLevel,
          auto_blocked: riskResult.autoBlock,
          client_info: fullClientInfo,
          request_data: JSON.stringify(message.data)
        });
      }

      // 发送检测结果
      this.sendMessage(client.ws, {
        type: 'risk_result',
        data: {
          requestId: message.requestId,
          isRisk: riskResult.isRisk,
          riskScore: riskResult.riskScore,
          riskLevel: riskResult.riskLevel,
          matchedKeywords: riskResult.matchedKeywords,
          action: riskResult.autoBlock ? 'block' : 'allow',
          message: riskResult.autoBlock ? '检测到高风险内容，已自动拦截' : 
                   riskResult.isRisk ? '检测到风险内容，请人工审核' : '内容安全'
        }
      });

      // 如果是高风险，通知管理员
      if (riskResult.autoBlock) {
        this.notifyAdmins('high_risk_detected', {
          clientId: clientId,
          ip: client.info.ip,
          riskScore: riskResult.riskScore,
          riskLevel: riskResult.riskLevel,
          content: content.substring(0, 100) + (content.length > 100 ? '...' : '')
        });
      }

    } catch (error) {
      console.error(`风控检测失败 ${clientId}:`.red, error);
      this.sendError(clientId, 'RISK_CHECK_ERROR', '风控检测异常');
    }
  }
  /**
   * 获取风控统计
   */
  async handleGetRiskStats(clientId) {
    try {
      const client = this.clients.get(clientId);
      
      if (!client || !client.info.isAuthenticated) {
        this.sendError(clientId, 'UNAUTHORIZED', '未认证');
        return;
      }

      const stats = await dataModel.getRiskControlStats();
      
      this.sendMessage(client.ws, {
        type: 'risk_stats',
        data: stats
      });
    } catch (error) {
      console.error(`获取风控统计失败 ${clientId}:`.red, error);
      this.sendError(clientId, 'STATS_ERROR', '获取统计信息失败');
    }
  }

  /**
   * 发送敏感词库给客户端
   */
  async sendSensitiveWords(clientId) {
    try {
      const client = this.clients.get(clientId);
      
      console.log(`🔍 检查客户端状态: ${clientId}`.gray);
      console.log(`   - 客户端存在: ${!!client}`.gray);
      console.log(`   - 已认证: ${client?.info?.isAuthenticated}`.gray);
      console.log(`   - WebSocket状态: ${client?.ws?.readyState}`.gray);
      
      if (!client) {
        console.warn(`客户端不存在: ${clientId}`.yellow);
        return;
      }
      
      if (!client.info.isAuthenticated) {
        console.warn(`客户端未认证: ${clientId}`.yellow);
        return;
      }
      
      if (client.ws.readyState !== WebSocket.OPEN) {
        console.warn(`WebSocket连接未打开: ${clientId}`.yellow);
        return;
      }

      console.log(`📡 开始获取敏感词库...`.gray);
      
      // 获取活跃的敏感词库
      const keywords = await dataModel.getRiskKeywords(
        { is_active: true }, 
        { page: 1, limit: 10000 }
      );

      console.log(`📊 获取到敏感词数量: ${keywords.keywords.length}`.gray);

      // 构建敏感词数据
      const sensitiveWordsData = {
        total: keywords.keywords.length,
        updateTime: new Date().toISOString(),
        keywords: keywords.keywords.map(keyword => ({
          id: keyword.id,
          keyword: keyword.keyword,
          type: keyword.keyword_type,
          level: keyword.risk_level,
          description: keyword.description
        }))
      };

      // 发送敏感词库
      this.sendMessage(client.ws, {
        type: 'sensitive_words_update',
        data: sensitiveWordsData
      });

      console.log(`📋 敏感词库已发送给客户端: ${clientId} (${sensitiveWordsData.total}个词)`.cyan);
      
    } catch (error) {
      console.error(`发送敏感词库失败 ${clientId}:`.red, error);
    }
  }

  /**
   * 广播敏感词库更新给所有认证客户端
   */
  async broadcastSensitiveWordsUpdate() {
    try {
      console.log('📢 广播敏感词库更新...'.yellow);
      
      for (const [clientId, client] of this.clients.entries()) {
        if (client.info.isAuthenticated) {
          await this.sendSensitiveWords(clientId);
        }
      }
      
    } catch (error) {
      console.error('广播敏感词库更新失败:'.red, error);
    }
  }

  /**
   * 处理客户端断开连接
   */
  handleDisconnection(clientId) {
    const client = this.clients.get(clientId);
    if (client) {
      console.log(`❌ 客户端断开: ${clientId} (${client.info.ip})`.gray);
      this.clients.delete(clientId);
    }
  }

  /**
   * 发送消息给客户端
   */
  sendMessage(ws, message) {
    try {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify(message));
      }
    } catch (error) {
      console.error('发送消息失败:'.red, error);
    }
  }

  /**
   * 发送错误消息
   */
  sendError(clientId, errorCode, errorMessage) {
    const client = this.clients.get(clientId);
    if (client) {
      this.sendMessage(client.ws, {
        type: 'error',
        data: {
          code: errorCode,
          message: errorMessage,
          timestamp: new Date().toISOString()
        }
      });
    }
  }

  /**
   * 广播消息给所有认证客户端
   */
  broadcastToAuthenticated(message) {
    this.clients.forEach((client, clientId) => {
      if (client.info.isAuthenticated) {
        this.sendMessage(client.ws, message);
      }
    });
  }

  /**
   * 通知管理员
   */
  notifyAdmins(eventType, data) {
    const notification = {
      type: 'admin_notification',
      data: {
        eventType: eventType,
        data: data,
        timestamp: new Date().toISOString()
      }
    };

    // 这里可以扩展为发送给特定的管理员客户端
    this.broadcastToAuthenticated(notification);
    
    console.log(`📢 管理员通知: ${eventType}`.yellow, data);
  }

  /**
   * 启动心跳检测
   */
  startHeartbeat(clientId) {
    const heartbeatInterval = setInterval(() => {
      const client = this.clients.get(clientId);
      
      if (!client) {
        clearInterval(heartbeatInterval);
        return;
      }

      const now = Date.now();
      const timeSinceLastHeartbeat = now - client.info.lastHeartbeat;
      
      // 超过60秒没有心跳，断开连接
      if (timeSinceLastHeartbeat > 60000) {
        console.log(`💓 客户端心跳超时，断开连接: ${clientId}`.yellow);
        client.ws.close();
        clearInterval(heartbeatInterval);
        return;
      }

      // 发送ping
      this.sendMessage(client.ws, { type: 'ping' });
    }, 30000); // 每30秒检查一次
  }

  /**
   * 生成客户端ID
   */
  generateClientId() {
    return 'client_' + Date.now() + '_' + Math.random().toString(36).substring(2);
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      port: this.port,
      clientCount: this.clients.size,
      authenticatedCount: Array.from(this.clients.values()).filter(c => c.info.isAuthenticated).length
    };
  }

  /**
   * 获取客户端列表
   */
  getClients() {
    const clients = [];
    this.clients.forEach((client, clientId) => {
      clients.push({
        id: clientId,
        ip: client.info.ip,
        userAgent: client.info.userAgent,
        connectedAt: client.info.connectedAt,
        isAuthenticated: client.info.isAuthenticated,
        lastHeartbeat: new Date(client.info.lastHeartbeat)
      });
    });
    return clients;
  }
  /**
   * 踢出客户端
   */
  kickClient(clientId, reason = '管理员操作') {
    console.log(`🔍 尝试踢出客户端: ${clientId}`.yellow);
    
    const client = this.clients.get(clientId);
    if (client) {
      console.log(`📤 发送踢出消息给客户端: ${clientId}`.yellow);
      
      // 发送踢出通知
      this.sendMessage(client.ws, {
        type: 'kicked',
        data: { 
          reason: reason,
          timestamp: new Date().toISOString()
        }
      });
      
      // 延迟关闭连接，让客户端有时间接收消息
      setTimeout(() => {
        try {
          if (client.ws && client.ws.readyState === WebSocket.OPEN) {
            console.log(`🔌 强制关闭客户端连接: ${clientId}`.yellow);
            client.ws.close(1000, reason);
          }
          // 从客户端列表中移除
          this.clients.delete(clientId);
          console.log(`✅ 客户端 ${clientId} 已被踢出`.green);
        } catch (error) {
          console.error(`强制关闭客户端连接失败 ${clientId}:`.red, error);
        }
      }, 1000);
      
      return true;
    } else {
      console.warn(`❌ 客户端不存在: ${clientId}`.yellow);
      return false;
    }
  }
}

// 创建单例实例
const webSocketService = new WebSocketRiskService();

module.exports = webSocketService;
