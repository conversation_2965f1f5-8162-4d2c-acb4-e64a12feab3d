import React, { useState, useEffect, useRef } from 'react';
import { Button, Input, Space, Tooltip } from 'antd';
import { ReloadOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';
import './TextCaptcha.css';

const TextCaptcha = ({ onSuccess, onFail }) => {
  const [captchaText, setCaptchaText] = useState('');
  const [userInput, setUserInput] = useState('');
  const [isVerifying, setIsVerifying] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [attempts, setAttempts] = useState(0);
  const [difficulty, setDifficulty] = useState('easy');
  const canvasRef = useRef(null);
  const inputRef = useRef(null);

  // 生成随机验证码文本
  const generateCaptchaText = () => {
    // 根据难度级别生成不同复杂度的验证码
    let chars, length;
    
    switch (difficulty) {
      case 'easy':
        chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789'; // 去除易混淆字符
        length = 4;
        break;
      case 'medium':
        chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
        length = 5;
        break;
      case 'hard':
        chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz0123456789';
        length = 6;
        break;
      default:
        chars = 'ABCDEFGHJKLMNPQRSTUVWXYZ23456789';
        length = 4;
    }
    
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  };

  // 绘制验证码到canvas
  const drawCaptcha = (text) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 设置背景渐变
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#f0f8ff');
    gradient.addColorStop(0.5, '#e6f3ff');
    gradient.addColorStop(1, '#ddeeff');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);

    // 添加背景噪点（降低密度）
    const noiseCount = difficulty === 'easy' ? 20 : difficulty === 'medium' ? 35 : 50;
    for (let i = 0; i < noiseCount; i++) {
      ctx.fillStyle = `rgba(${Math.random() * 100 + 150}, ${Math.random() * 100 + 150}, ${Math.random() * 100 + 150}, 0.3)`;
      ctx.fillRect(Math.random() * width, Math.random() * height, 1, 1);
    }

    // 绘制干扰线（减少数量）
    const lineCount = difficulty === 'easy' ? 1 : difficulty === 'medium' ? 2 : 3;
    for (let i = 0; i < lineCount; i++) {
      ctx.strokeStyle = `rgba(${Math.random() * 100 + 100}, ${Math.random() * 100 + 100}, ${Math.random() * 100 + 100}, 0.4)`;
      ctx.lineWidth = Math.random() * 2 + 1;
      ctx.beginPath();
      ctx.moveTo(Math.random() * width, Math.random() * height);
      ctx.lineTo(Math.random() * width, Math.random() * height);
      ctx.stroke();
    }

    // 绘制文字
    const fontSize = Math.floor(height * 0.6);
    ctx.font = `bold ${fontSize}px 'Arial', sans-serif`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // 为每个字符设置不同的样式
    const charWidth = width / text.length;
    for (let i = 0; i < text.length; i++) {
      const char = text[i];
      const x = charWidth * (i + 0.5);
      const y = height / 2;
      
      // 随机颜色（但保持可读性）
      const hue = Math.random() * 60 + 200; // 蓝紫色系
      ctx.fillStyle = `hsl(${hue}, 70%, 35%)`;
      
      // 随机旋转（轻微）
      const rotation = (Math.random() - 0.5) * 0.3;
      
      ctx.save();
      ctx.translate(x, y);
      ctx.rotate(rotation);
      
      // 添加文字阴影效果
      ctx.shadowColor = 'rgba(0,0,0,0.3)';
      ctx.shadowOffsetX = 1;
      ctx.shadowOffsetY = 1;
      ctx.shadowBlur = 2;
      
      ctx.fillText(char, 0, 0);
      ctx.restore();
    }

    // 添加轻微的整体扭曲效果（仅困难模式）
    if (difficulty === 'hard') {
      const imageData = ctx.getImageData(0, 0, width, height);
      const data = imageData.data;
      
      for (let y = 0; y < height; y++) {
        for (let x = 0; x < width; x++) {
          const offset = Math.sin(x * 0.02) * 2 + Math.sin(y * 0.02) * 1;
          const sourceX = Math.round(x + offset);
          const sourceY = y;
          
          if (sourceX >= 0 && sourceX < width && sourceY >= 0 && sourceY < height) {
            const sourceIndex = (sourceY * width + sourceX) * 4;
            const targetIndex = (y * width + x) * 4;
            
            data[targetIndex] = data[sourceIndex];
            data[targetIndex + 1] = data[sourceIndex + 1];
            data[targetIndex + 2] = data[sourceIndex + 2];
            data[targetIndex + 3] = data[sourceIndex + 3];
          }
        }
      }
      
      ctx.putImageData(imageData, 0, 0);
    }
  };

  // 生成新验证码
  const generateNewCaptcha = () => {
    const newText = generateCaptchaText();
    setCaptchaText(newText);
    drawCaptcha(newText);
    setUserInput('');
    setShowHint(false);
  };

  // 验证输入
  const verifyCaptcha = () => {
    if (!userInput.trim()) {
      onFail && onFail('请输入验证码');
      return;
    }

    setIsVerifying(true);

    // 模拟验证延迟
    setTimeout(() => {
      const isCorrect = userInput.toLowerCase() === captchaText.toLowerCase();
      
      if (isCorrect) {
        onSuccess && onSuccess();
      } else {
        const newAttempts = attempts + 1;
        setAttempts(newAttempts);
        
        // 根据错误次数调整难度
        if (newAttempts >= 3 && difficulty === 'hard') {
          setDifficulty('medium');
        } else if (newAttempts >= 2 && difficulty === 'medium') {
          setDifficulty('easy');
        } else if (newAttempts >= 1 && difficulty === 'easy') {
          // 保持简单模式，但提供提示
          setShowHint(true);
        }
        
        onFail && onFail(`验证码错误，请重试 (${newAttempts}/5)`);
        
        // 自动刷新验证码
        setTimeout(() => {
          generateNewCaptcha();
        }, 1000);
      }
      
      setIsVerifying(false);
    }, 300);
  };

  // 处理键盘事件
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      verifyCaptcha();
    }
  };

  // 获取难度提示文本
  const getDifficultyHint = () => {
    switch (difficulty) {
      case 'easy': return '简单模式：大小写不敏感';
      case 'medium': return '中等模式：包含大小写字母';
      case 'hard': return '困难模式：包含数字和特殊效果';
      default: return '';
    }
  };

  // 初始化
  useEffect(() => {
    generateNewCaptcha();
    // 自动聚焦输入框
    setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
  }, [difficulty]);

  return (
    <div className="text-captcha-container">
      <div className="captcha-header">
        <div className="difficulty-indicator">
          <span className="difficulty-text">{getDifficultyHint()}</span>
        </div>
      </div>
      
      <div className="captcha-display">
        <canvas
          ref={canvasRef}
          width="200"
          height="80"
          className="captcha-canvas"
          onClick={generateNewCaptcha}
        />
        <Tooltip title="点击图片或按钮刷新验证码">
          <Button
            type="text"
            icon={<ReloadOutlined />}
            onClick={generateNewCaptcha}
            className="refresh-button"
            size="small"
          />
        </Tooltip>
      </div>

      <div className="captcha-input-section">
        <Space direction="vertical" style={{ width: '100%' }}>
          <Input
            ref={inputRef}
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="请输入验证码（不区分大小写）"
            maxLength={6}
            className="captcha-input"
            disabled={isVerifying}
            autoComplete="off"
          />
          
          {showHint && (
            <div className="captcha-hint">
              <Tooltip title="显示/隐藏提示">
                <Button
                  type="text"
                  size="small"
                  icon={showHint ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  onClick={() => setShowHint(!showHint)}
                >
                  {showHint ? '隐藏提示' : '显示提示'}
                </Button>
              </Tooltip>
              {showHint && (
                <div className="hint-text">
                  提示：验证码是 <code>{captchaText}</code>
                </div>
              )}
            </div>
          )}
          
          <Button
            type="primary"
            onClick={verifyCaptcha}
            loading={isVerifying}
            disabled={!userInput.trim()}
            className="verify-button"
            block
          >
            {isVerifying ? '验证中...' : '验证'}
          </Button>
        </Space>
      </div>

      <div className="captcha-tips">
        <div className="tip-item">💡 看不清？点击图片刷新</div>
        <div className="tip-item">🔤 输入不区分大小写</div>
        <div className="tip-item">⌨️ 按回车键快速验证</div>
        {attempts > 0 && (
          <div className="tip-item error">
            ❌ 已尝试 {attempts} 次，{5 - attempts} 次后自动降低难度
          </div>
        )}
      </div>
    </div>
  );
};

export default TextCaptcha;
