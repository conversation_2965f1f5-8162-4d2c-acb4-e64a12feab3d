import { message, notification } from 'antd';
import { authAPI } from '../services/api';

/**
 * 增强的登录状态管理器
 * 解决特殊情况下可能不会跳转的问题
 */
class LoginStateManager {
  constructor() {
    this.isLoginInProgress = false;
    this.loginRetryCount = 0;
    this.maxRetries = 3;
    this.authCheckInterval = null;
    this.forceRedirectTimer = null;
      // 绑定方法
    this.handleLogin = this.handleLogin.bind(this);
    this.checkCurrentAuthState = this.checkCurrentAuthState.bind(this);
    this.forceRedirect = this.forceRedirect.bind(this);
    
    console.log('🔧 LoginStateManager 已初始化');
  }

  /**
   * 处理登录流程
   * @param {Object} credentials - 登录凭据
   * @param {Function} onSuccess - 成功回调
   * @param {Function} onFailure - 失败回调
   */
  async handleLogin(credentials, onSuccess, onFailure) {
    if (this.isLoginInProgress) {
      console.log('⚠️ 登录正在进行中，忽略重复请求');
      return;
    }

    this.isLoginInProgress = true;
    this.loginRetryCount = 0;

    try {
      console.log('🚀 开始登录流程...');
      const result = await this.performLogin(credentials);
      
      if (result.success) {
        console.log('✅ 登录成功');
        this.handleLoginSuccess(result.data, onSuccess);
      } else {
        console.log('❌ 登录失败:', result.error);
        this.handleLoginFailure(result.error, onFailure);
      }
    } catch (error) {
      console.error('🔥 登录过程发生异常:', error);
      this.handleLoginFailure(error, onFailure);
    } finally {
      this.isLoginInProgress = false;
    }
  }

  /**
   * 执行登录请求
   * @param {Object} credentials - 登录凭据
   */
  async performLogin(credentials) {
    try {
      const response = await authAPI.login(credentials);
      
      // 统一处理响应格式
      const responseData = response.data || response;
      
      if (responseData.code === 0) {
        return {
          success: true,
          data: responseData.data
        };
      } else {
        return {
          success: false,
          error: responseData.msg || '登录失败'
        };
      }
    } catch (error) {
      return {
        success: false,
        error: this.parseLoginError(error)
      };
    }
  }

  /**
   * 解析登录错误
   * @param {Error} error - 错误对象
   */
  parseLoginError(error) {
    if (error.response) {
      const status = error.response.status;
      const data = error.response.data;
      
      switch (status) {
        case 401:
          return data?.msg || '用户名或密码错误';
        case 403:
          return data?.msg || '账户被禁用';
        case 429:
          return '请求过于频繁，请稍后再试';
        case 500:
          return '服务器内部错误，请联系管理员';
        default:
          return data?.msg || `网络错误 (${status})`;
      }
    } else if (error.request) {
      return '网络连接失败，请检查服务器是否启动';
    } else {
      return error.message || '未知错误';
    }
  }

  /**
   * 处理登录成功
   * @param {Object} data - 登录返回的数据
   * @param {Function} onSuccess - 成功回调
   */
  handleLoginSuccess(data, onSuccess) {
    try {
      // 保存认证信息
      localStorage.setItem('auth_token', data.token);
      localStorage.setItem('user_info', JSON.stringify(data.user));
      localStorage.setItem('login_time', new Date().toISOString());
      
      // 显示成功消息
      message.success('欢迎回来，主人~ (｡♥‿♥｡)');
      
      // 立即执行成功回调
      if (typeof onSuccess === 'function') {
        onSuccess(true);
      }
      
      // 启动认证状态检查
      this.startAuthStateCheck();
      
      // 设置强制跳转定时器（备用方案）
      this.setForceRedirectTimer();
      
    } catch (error) {
      console.error('🔥 处理登录成功时发生错误:', error);
      this.handleLoginFailure('保存登录状态失败', () => {});
    }
  }

  /**
   * 处理登录失败
   * @param {String} error - 错误信息
   * @param {Function} onFailure - 失败回调
   */
  handleLoginFailure(error, onFailure) {
    message.error(error || '登录失败，请重试~ (｡•́︿•̀｡)');
    
    if (typeof onFailure === 'function') {
      onFailure(error);
    }
    
    // 如果是网络错误且重试次数未达上限，尝试重试
    if (error.includes('网络') && this.loginRetryCount < this.maxRetries) {
      this.loginRetryCount++;
      console.log(`🔄 网络错误，准备重试 (${this.loginRetryCount}/${this.maxRetries})`);
      
      setTimeout(() => {
        notification.info({
          message: '自动重试',
          description: `检测到网络错误，正在尝试第 ${this.loginRetryCount} 次重试...`,
          duration: 3
        });
      }, 2000);
    }
  }

  /**
   * 启动认证状态检查
   */
  startAuthStateCheck() {
    this.clearAuthStateCheck();
    
    let checkCount = 0;
    const maxChecks = 10;
    
    this.authCheckInterval = setInterval(() => {
      checkCount++;
      
      const isAuthenticated = this.checkCurrentAuthState();
      
      if (isAuthenticated) {
        console.log('✅ 认证状态检查通过');
        this.clearAuthStateCheck();
        this.clearForceRedirectTimer();
        return;
      }
      
      if (checkCount >= maxChecks) {
        console.warn('⚠️ 认证状态检查超时');
        this.clearAuthStateCheck();
        this.forceRedirect();
        return;
      }
      
      console.log(`🔄 检查认证状态... (${checkCount}/${maxChecks})`);
    }, 500);
  }

  /**
   * 检查当前认证状态
   */
  checkCurrentAuthState() {
    const token = localStorage.getItem('auth_token');
    const userInfo = localStorage.getItem('user_info');
    
    // 检查DOM中是否有已登录的标识
    const isInConsole = document.querySelector('.sidebar-logo') !== null ||
                       document.querySelector('.header-user-info') !== null ||
                       document.querySelector('.main-content') !== null;
    
    return !!(token && userInfo && isInConsole);
  }

  /**
   * 设置强制跳转定时器
   */
  setForceRedirectTimer() {
    this.clearForceRedirectTimer();
    
    // 5秒后强制跳转（兜底方案）
    this.forceRedirectTimer = setTimeout(() => {
      console.log('⏰ 触发强制跳转定时器');
      this.forceRedirect();
    }, 5000);
  }

  /**
   * 强制跳转到控制台
   */
  forceRedirect() {
    const token = localStorage.getItem('auth_token');
    
    if (!token) {
      console.warn('⚠️ 没有认证token，无法强制跳转');
      return;
    }
    
    console.log('🔄 执行强制跳转...');
    
    // 尝试多种跳转方式
    try {
      // 方式1: 刷新页面
      if (window.location.pathname === '/' || window.location.pathname.includes('login')) {
        window.location.reload();
        return;
      }
      
      // 方式2: 使用 React Router (如果存在)
      if (window.history && window.history.pushState) {
        window.history.pushState({}, '', '/dashboard');
        window.location.reload();
        return;
      }
      
      // 方式3: 直接跳转
      window.location.href = '/';
      
    } catch (error) {
      console.error('🔥 强制跳转失败:', error);
      // 最后的备用方案
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  }

  /**
   * 清除认证状态检查
   */
  clearAuthStateCheck() {
    if (this.authCheckInterval) {
      clearInterval(this.authCheckInterval);
      this.authCheckInterval = null;
    }
  }

  /**
   * 清除强制跳转定时器
   */
  clearForceRedirectTimer() {
    if (this.forceRedirectTimer) {
      clearTimeout(this.forceRedirectTimer);
      this.forceRedirectTimer = null;
    }
  }

  /**
   * 获取登录状态信息
   */
  getLoginStatus() {
    const token = localStorage.getItem('auth_token');
    const userInfo = localStorage.getItem('user_info');
    const loginTime = localStorage.getItem('login_time');
    
    return {
      hasToken: !!token,
      hasUserInfo: !!userInfo,
      loginTime: loginTime ? new Date(loginTime) : null,
      isAuthenticated: this.checkCurrentAuthState(),
      isLoginInProgress: this.isLoginInProgress
    };
  }

  /**
   * 重置登录状态
   */
  reset() {
    this.isLoginInProgress = false;
    this.loginRetryCount = 0;
    this.clearAuthStateCheck();
    this.clearForceRedirectTimer();
    
    console.log('🔧 LoginStateManager 已重置');
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.reset();
    console.log('🗑️ LoginStateManager 已销毁');
  }
}

// 创建全局实例
const loginStateManager = new LoginStateManager();

// 将实例暴露到window对象以便调试
if (typeof window !== 'undefined') {
  window.loginStateManager = loginStateManager;
}

export default loginStateManager;
