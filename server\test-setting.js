const MySQLDataModel = require('./src/models/mysql-real-model');

async function testSetting() {
    try {
        const mysqlModel = new MySQLDataModel();

        console.log('测试 setSetting 方法...');

        // 测试数据库清理器的调用方式
        console.log('\n=== 测试数据库清理器调用 ===');
        await mysqlModel.setSetting('last_cleanup_time', new Date().toISOString(), 'string', '最后清理时间');
        console.log('✅ 数据库清理器调用测试通过');

        // 测试正常值
        console.log('\n=== 测试正常值 ===');
        await mysqlModel.setSetting('test_key', 'test_value', 'string', '测试设置');
        console.log('✅ 正常值测试通过');

        console.log('🎉 所有测试通过！');

    } catch (error) {
        console.error('❌ 测试失败:', error);
        console.error('错误详情:', error.message);
        console.error('错误堆栈:', error.stack);
    } finally {
        process.exit(0);
    }
}

testSetting();
