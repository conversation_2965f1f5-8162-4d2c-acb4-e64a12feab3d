const colors = require('colors');
const mysqlModel = require('../models/mysql-real-model');
const { extractUin, extractCmd } = require('../utils/helpers');
const { handleValidationFailure } = require('./risk-control');

/**
 * 请求体验证规则配置
 */
const VALIDATION_RULES = {
    // 通用规则
    common: {
        maxRequestSize: 50 * 1024, // 50KB 最大请求体大小
        maxParamLength: 10000,     // 单个参数最大长度
        maxParamCount: 50,         // 最大参数数量
        allowedCharsRegex: /^[a-zA-Z0-9\-_=&%+\/\.\s\u4e00-\u9fa5]*$/, // 允许的字符
        suspiciousPatterns: [
            /script/i,
            /javascript/i,
            /vbscript/i,
            /onload/i,
            /onerror/i,
            /eval\(/i,
            /exec\(/i,
            /system\(/i,
            /cmd\(/i,
            /shell/i,
            /<[^>]*>/g, // HTML标签
            /\.\.\//g,  // 路径遍历
            /union.*select/i, // SQL注入
            /drop.*table/i,
            /delete.*from/i,
            /insert.*into/i,
            /update.*set/i
        ]
    },
    
    // 签名请求验证规则
    sign: {
        requiredParams: ['uin', 'cmd'],
        paramRules: {
            uin: {
                type: 'string',
                pattern: /^\d{5,12}$/, // QQ号格式：5-12位数字
                required: true
            },
            cmd: {
                type: 'string',
                pattern: /^[a-zA-Z0-9\._-]{1,100}$/, // 命令格式
                required: true,
                allowedValues: [
                    'wtlogin.login', 'wtlogin.exchange_emp', 'wtlogin.trans_emp',
                    'MessageSvc.PbSendMsg', 'MessageSvc.PbGetMsg', 'OidbSvc.0x88d_0',
                    'OidbSvc.0x89a_0', 'OidbSvc.0x8a1_0', 'friendlist.getFriendGroupList',
                    'friendlist.GetTroopListReqV2', 'ProfileService.Pb.ReqSystemMsgNew.Group',
                    'trpc.login.ecdh.EcdhService.SsoKeyExchange', 'trpc.login.ecdh.EcdhService.SsoNTLoginPasswordLogin'
                    // 可以根据实际需要添加更多允许的命令
                ]
            },
            data: {
                type: 'string',
                maxLength: 8192, // 8KB
                required: false // data不是必需的
                // 移除十六进制格式验证，因为实际请求中data=1145141919810不是十六进制
            },
            buffer: {
                type: 'string',
                maxLength: 8192,
                required: false
            },
            seq: {
                type: 'string',
                required: false
                // 移除格式验证，因为实际请求中seq=123
            },
            ssoseq: {
                type: 'string',
                required: false
            },
            qua: {
                type: 'string',
                maxLength: 200,
                required: false
            },
            android_id: {
                type: 'string',
                required: false
            },
            androidId: {
                type: 'string',
                required: false
            },
            Type: {
                type: 'string',
                required: false // 查询参数Type，可选
            }
        }
    },
    
    // Energy请求验证规则（宽松验证，不校验参数格式）
    energy: {
        requiredParams: ['data', 'guid', 'version'], // 移除uin要求
        skipUnknownParamWarnings: true, // 跳过未知参数警告
        paramRules: {
            // 允许所有常见参数，不做严格验证
            uin: { type: 'string', required: false },
            data: { type: 'string', required: true },
            guid: { type: 'string', required: true },
            version: { type: 'string', maxLength: 100, required: true },
            Type: { type: 'string', required: false },
            // 添加其他可能的参数
            cmd: { type: 'string', required: false },
            seq: { type: 'string', required: false },
            androidId: { type: 'string', required: false },
            android_id: { type: 'string', required: false }
        }
    },
    
    // Get0553请求验证规则
    get0553: {
        requiredParams: ['uin', 'cmd', 'data', 'ssoseq'],
        paramRules: {
            uin: {
                type: 'string',
                required: true
                // 移除格式验证，因为实际请求中uin=114514
            },
            cmd: {
                type: 'string',
                required: true
                // 移除格式验证，因为实际请求中cmd=114514
            },
            data: {
                type: 'string',
                maxLength: 8192,
                required: true
                // 移除十六进制格式验证，因为实际请求中data=114514
            },
            ssoseq: {
                type: 'string',
                required: true
                // 移除格式验证，因为实际请求中ssoseq=114514
            },
            Type: {
                type: 'string',
                required: false // 查询参数Type，可选
            }
        }
    }
};

/**
 * 验证请求体大小
 */
function validateRequestSize(req) {
    const contentLength = parseInt(req.get('content-length') || '0');
    if (contentLength > VALIDATION_RULES.common.maxRequestSize) {
        return {
            valid: false,
            error: `请求体过大: ${contentLength} bytes, 最大允许: ${VALIDATION_RULES.common.maxRequestSize} bytes`,
            riskLevel: 'high'
        };
    }
    return { valid: true };
}

/**
 * 检测可疑模式
 */
function detectSuspiciousPatterns(content) {
    const suspiciousMatches = [];
    
    for (const pattern of VALIDATION_RULES.common.suspiciousPatterns) {
        const matches = content.match(pattern);
        if (matches) {
            suspiciousMatches.push({
                pattern: pattern.toString(),
                matches: matches
            });
        }
    }
    
    return suspiciousMatches;
}

/**
 * 验证参数值
 */
function validateParamValue(paramName, value, rule, endpoint = '') {
    const errors = [];

    // 检查必需参数
    if (rule.required && (!value || value.toString().trim() === '')) {
        errors.push(`参数 ${paramName} 是必需的`);
        return { valid: false, errors };
    }

    if (!value) return { valid: true, errors: [] };

    const strValue = value.toString();

    // 对于所有接口，采用宽松验证策略
    if (endpoint.includes('/energy') || endpoint.includes('/sign') || endpoint.includes('/Get0553')) {
        // 只检查长度限制，跳过格式验证
        if (rule.maxLength && strValue.length > rule.maxLength) {
            errors.push(`参数 ${paramName} 长度超限: ${strValue.length} > ${rule.maxLength}`);
        }

        // 跳过格式验证和字符规则检查，只做基本存在性检查
        return {
            valid: errors.length === 0,
            errors
        };
    }

    // 其他接口保持原有验证逻辑
    // 检查长度
    if (rule.maxLength && strValue.length > rule.maxLength) {
        errors.push(`参数 ${paramName} 长度超限: ${strValue.length} > ${rule.maxLength}`);
    }

    // 检查格式
    if (rule.pattern && !rule.pattern.test(strValue)) {
        errors.push(`参数 ${paramName} 格式不正确`);
    }

    // 检查允许的值
    if (rule.allowedValues && !rule.allowedValues.includes(strValue)) {
        errors.push(`参数 ${paramName} 值不在允许范围内`);
    }

    // 检查通用字符规则
    if (!VALIDATION_RULES.common.allowedCharsRegex.test(strValue)) {
        errors.push(`参数 ${paramName} 包含不允许的字符`);
    }

    return {
        valid: errors.length === 0,
        errors
    };
}

/**
 * 解析请求数据
 */
function parseRequestData(req) {
    let requestData = '';
    let parsedParams = {};

    try {
        // 处理不同的请求体格式
        if (req.body && typeof req.body === 'object') {
            // JSON 或表单数据
            parsedParams = { ...req.body };
            const params = new URLSearchParams();
            for (const [key, value] of Object.entries(req.body)) {
                params.append(key, value);
            }
            requestData = params.toString();
        } else if (typeof req.body === 'string') {
            // 字符串格式
            requestData = req.body;
            // 尝试解析URL编码的参数
            try {
                const urlParams = new URLSearchParams(requestData);
                for (const [key, value] of urlParams) {
                    parsedParams[key] = value;
                }
            } catch (e) {
                // 如果解析失败，使用正则提取关键参数
                const uinMatch = requestData.match(/uin=([^&]+)/);
                const cmdMatch = requestData.match(/cmd=([^&]+)/);
                if (uinMatch) parsedParams.uin = uinMatch[1];
                if (cmdMatch) parsedParams.cmd = cmdMatch[1];
            }
        }

        // 处理multer解析的multipart/form-data参数
        if (req.files && Array.isArray(req.files)) {
            // multer将表单字段作为文件处理，我们需要提取这些字段
            req.files.forEach(file => {
                if (file.fieldname && file.buffer) {
                    // 将buffer转换为字符串
                    const value = file.buffer.toString('utf8');
                    parsedParams[file.fieldname] = value;

                    // 同时更新requestData
                    if (requestData) {
                        requestData += `&${file.fieldname}=${encodeURIComponent(value)}`;
                    } else {
                        requestData = `${file.fieldname}=${encodeURIComponent(value)}`;
                    }
                }
            });
        }

        // 合并查询参数
        if (req.query) {
            parsedParams = { ...parsedParams, ...req.query };
        }

        return { requestData, parsedParams };
    } catch (error) {
        throw new Error(`请求数据解析失败: ${error.message}`);
    }
}

/**
 * 验证请求参数
 */
function validateRequestParams(parsedParams, validationRule, endpoint = '') {
    const errors = [];
    const warnings = [];

    // 检查参数数量
    const paramCount = Object.keys(parsedParams).length;
    if (paramCount > VALIDATION_RULES.common.maxParamCount) {
        errors.push(`参数数量过多: ${paramCount} > ${VALIDATION_RULES.common.maxParamCount}`);
    }

    // 检查必需参数
    for (const requiredParam of validationRule.requiredParams || []) {
        if (!parsedParams[requiredParam]) {
            errors.push(`缺少必需参数: ${requiredParam}`);
        }
    }

    // 验证每个参数
    for (const [paramName, paramValue] of Object.entries(parsedParams)) {
        const rule = validationRule.paramRules?.[paramName];

        if (rule) {
            const validation = validateParamValue(paramName, paramValue, rule, endpoint);
            if (!validation.valid) {
                errors.push(...validation.errors);
            }
        } else {
            // 跳过所有接口的未知参数警告，因为实际请求中可能有各种参数
            // 不再产生未知参数警告
        }

        // 检查参数长度
        const strValue = paramValue?.toString() || '';
        if (strValue.length > VALIDATION_RULES.common.maxParamLength) {
            errors.push(`参数 ${paramName} 长度超过限制: ${strValue.length} > ${VALIDATION_RULES.common.maxParamLength}`);
        }
    }

    return { errors, warnings };
}

/**
 * 记录验证失败日志
 */
async function logValidationFailure(req, validationType, errors, riskLevel = 'medium') {
    try {
        const ip = req.ip || req.connection.remoteAddress;
        const userAgent = req.get('User-Agent') || 'unknown';
        const { requestData, parsedParams } = parseRequestData(req);

        await mysqlModel.recordRiskData({
            username: parsedParams.username || null,
            device_id: parsedParams.android_id || parsedParams.androidId || null,
            ip_address: ip,
            uin: parsedParams.uin || null,
            risk_content: `请求验证失败 - ${validationType}`,
            matched_keywords: errors,
            risk_score: riskLevel === 'high' ? 90 : riskLevel === 'medium' ? 60 : 30,
            risk_level: riskLevel,
            auto_blocked: true,
            client_info: {
                ip_address: ip,
                user_agent: userAgent,
                uin: parsedParams.uin || null,
                device_id: parsedParams.android_id || parsedParams.androidId || null
            },
            request_data: JSON.stringify({
                url: req.url,
                method: req.method,
                headers: req.headers,
                body: requestData.substring(0, 1000), // 限制长度
                params: parsedParams,
                errors: errors
            })
        });

        console.log(`🚫 请求验证失败记录: ${ip} - ${validationType} - ${errors.join(', ')}`.red);
    } catch (error) {
        console.error('记录验证失败日志时出错:', error);
    }
}

/**
 * 获取验证规则
 */
function getValidationRule(endpoint) {
    if (endpoint.includes('/sign')) {
        return VALIDATION_RULES.sign;
    } else if (endpoint.includes('/energy')) {
        return VALIDATION_RULES.energy;
    } else if (endpoint.includes('/Get0553')) {
        return VALIDATION_RULES.get0553;
    }
    return null;
}

/**
 * 请求验证中间件
 */
const requestValidationMiddleware = async (req, res, next) => {
    const startTime = Date.now();
    const ip = req.ip || req.connection.remoteAddress;
    const endpoint = req.path;

    try {
        console.log(`🔍 开始请求验证: ${req.method} ${endpoint} - IP: ${ip}`.cyan);

        // 1. 验证请求体大小
        const sizeValidation = validateRequestSize(req);
        if (!sizeValidation.valid) {
            await logValidationFailure(req, '请求体大小验证', [sizeValidation.error], sizeValidation.riskLevel);

            // 调用风控处理
            const riskResult = await handleValidationFailure(req, [sizeValidation.error], sizeValidation.riskLevel);

            return res.status(413).json({
                code: 413,
                msg: '请求体过大',
                error: sizeValidation.error,
                blocked: riskResult.shouldBlock,
                timestamp: new Date().toISOString()
            });
        }

        // 2. 解析请求数据
        let requestData, parsedParams;
        try {
            const parsed = parseRequestData(req);
            requestData = parsed.requestData;
            parsedParams = parsed.parsedParams;
        } catch (error) {
            await logValidationFailure(req, '请求数据解析', [error.message], 'high');
            return res.status(400).json({
                code: 400,
                msg: '请求数据格式错误',
                error: error.message,
                timestamp: new Date().toISOString()
            });
        }

        // 3. 检测可疑模式
        const suspiciousMatches = detectSuspiciousPatterns(requestData);
        if (suspiciousMatches.length > 0) {
            const suspiciousInfo = suspiciousMatches.map(m => `${m.pattern}: ${m.matches.join(', ')}`);
            await logValidationFailure(req, '可疑模式检测', suspiciousInfo, 'high');

            // 调用风控处理
            const riskResult = await handleValidationFailure(req, suspiciousInfo, 'high');

            return res.status(400).json({
                code: 400,
                msg: '请求包含可疑内容',
                error: '检测到潜在的安全威胁',
                blocked: riskResult.shouldBlock,
                timestamp: new Date().toISOString()
            });
        }

        // 4. 获取对应的验证规则
        const validationRule = getValidationRule(endpoint);
        if (!validationRule) {
            console.log(`⚠️  未找到验证规则: ${endpoint}`.yellow);
            return next(); // 没有验证规则的接口直接通过
        }

        // 5. 验证请求参数
        const paramValidation = validateRequestParams(parsedParams, validationRule, endpoint);
        if (paramValidation.errors.length > 0) {
            // 先记录验证失败日志
            await logValidationFailure(req, '参数验证', paramValidation.errors, 'medium');

            // 调用风控处理
            const riskResult = await handleValidationFailure(req, paramValidation.errors, 'medium');

            // 如果风控建议阻止，返回更严格的错误
            if (riskResult.shouldBlock) {
                return res.status(403).json({
                    code: 403,
                    msg: '请求被拒绝',
                    error: '频繁的验证失败请求',
                    details: riskResult.details,
                    timestamp: new Date().toISOString()
                });
            }

            return res.status(400).json({
                code: 400,
                msg: '请求参数验证失败',
                errors: paramValidation.errors,
                warnings: paramValidation.warnings,
                timestamp: new Date().toISOString()
            });
        }

        // 6. 记录警告（但不阻止请求）- 已禁用所有警告输出
        // if (paramValidation.warnings.length > 0) {
        //     console.log(`⚠️  参数验证警告: ${paramValidation.warnings.join(', ')}`.yellow);
        // }

        // 7. 将解析后的参数添加到请求对象中
        req.validatedParams = parsedParams;
        req.validationInfo = {
            endpoint,
            validationTime: Date.now() - startTime,
            warnings: paramValidation.warnings
        };

        console.log(`✅ 请求验证通过: ${endpoint} - 耗时: ${Date.now() - startTime}ms`.green);
        next();

    } catch (error) {
        console.error('请求验证中间件错误:'.red, error);

        // 记录系统错误
        try {
            await logValidationFailure(req, '系统错误', [error.message], 'high');
        } catch (logError) {
            console.error('记录验证错误日志失败:', logError);
        }

        // 根据配置决定是否继续处理请求
        const continueOnError = process.env.VALIDATION_CONTINUE_ON_ERROR === 'true';
        if (continueOnError) {
            console.log('⚠️  验证系统错误，继续处理请求'.yellow);
            next();
        } else {
            res.status(500).json({
                code: 500,
                msg: '请求验证系统错误',
                error: 'Request validation system error',
                timestamp: new Date().toISOString()
            });
        }
    }
};

module.exports = {
    requestValidationMiddleware,
    VALIDATION_RULES,
    validateRequestSize,
    detectSuspiciousPatterns,
    validateParamValue,
    parseRequestData,
    validateRequestParams,
    getValidationRule
};
