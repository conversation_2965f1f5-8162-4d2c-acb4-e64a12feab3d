import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Modal, 
  Form, 
  Input, 
  Popconfirm, 
  Space, 
  Tag, 
  Typography,
  Alert,
  Divider
} from 'antd';
import { 
  UserAddOutlined, 
  DeleteOutlined, 
  KeyOutlined, 
  SecurityScanOutlined,
  ExclamationCircleOutlined,
  CrownOutlined
} from '@ant-design/icons';
import api from '../services/api';
import NotificationHelper from '../components/NotificationHelper';
import { normalizeResponse, isResponseSuccess, getResponseData, getResponseMessage } from '../utils/responseHelper';

const { Title, Text } = Typography;

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [form] = Form.useForm();
  const [passwordForm] = Form.useForm();
  // 检查当前用户是否为 xiya
  const currentUser = JSON.parse(localStorage.getItem('user_info') || '{}');
  const isXiya = currentUser.username === 'xiya';
  
  // 🔍 调试用户信息
  console.log('🔍 localStorage中的用户信息:', {
    user_info: localStorage.getItem('user_info'),
    token: localStorage.getItem('token'),
    parsedUser: currentUser,
    isXiya: isXiya
  });

  useEffect(() => {
    if (isXiya) {
      loadUsers();
    }
  }, [isXiya]);  const loadUsers = async () => {
    try {
      setLoading(true);
      console.log('📡 开始加载用户列表...');
      console.log('📡 当前用户信息:', currentUser);
      console.log('📡 是否为xiya:', isXiya);
        const response = await api.get('/user-management/users');
      
      console.log('📡 完整API响应:', response);
      console.log('📡 响应状态:', response.status);
      console.log('📡 响应头:', response.headers);
      console.log('📡 响应数据:', response.data);
      console.log('📡 响应数据类型:', typeof response.data);
      console.log('📡 响应数据success字段:', response.data?.success);
        // 检查response和response.data
      if (!response) {
        console.error('❌ 响应对象为空');
        NotificationHelper.error('💔 数据格式错误', '服务器响应为空');
        return;
      }
      
      if (!response.data) {
        console.error('❌ 响应数据为空');
        NotificationHelper.error('💔 数据格式错误', '服务器响应数据为空');
        return;
      }
        // 使用统一的响应格式处理
      const normalizedResponse = normalizeResponse(response);
      
      if (isResponseSuccess(normalizedResponse)) {
        const responseData = getResponseData(normalizedResponse);
        console.log('✅ 用户数据加载成功:', responseData);
        console.log('✅ 用户数据数组长度:', responseData?.length || 0);
        setUsers(responseData || []);
        NotificationHelper.success('加载成功', `成功获取 ${responseData?.length || 0} 个用户`);
      } else {
        console.error('❌ API返回失败:', normalizedResponse);
        NotificationHelper.error('💔 获取失败', getResponseMessage(normalizedResponse));
      }
    } catch (error) {
      console.error('❌ 网络请求失败:', error);
      console.error('❌ 错误详情:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
        response: error.response,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        headers: error.response?.headers
      });
      
      // 检查错误类型并显示相应消息
      if (error.response?.data?.message) {
        NotificationHelper.error('🔐 验证失败', error.response.data.message);
      } else if (error.response?.status) {
        NotificationHelper.networkError(error.response.status, `网络请求失败: ${error.message}`);
      } else {
        NotificationHelper.error('💔 网络错误', `请求失败: ${error.message}`);
      }
    } finally {
      setLoading(false);
    }
  };
  const handleAddUser = async (values) => {
    try {
      const response = await api.post('/user-management/users', values);
      if (response.data.success || response.data.code === 0) {
        NotificationHelper.operationSuccess('create', '管理员用户创建成功');
        setModalVisible(false);
        form.resetFields();
        loadUsers();
      } else {
        NotificationHelper.error('创建失败', response.data.message || response.data.msg);
      }
    } catch (error) {
      NotificationHelper.networkError(error.response?.status || 500, '创建用户失败');
    }
  };
  const handleDeleteUser = async (userId) => {
    try {
      const response = await api.delete(`/user-management/users/${userId}`);
      if (response.data.success || response.data.code === 0) {
        NotificationHelper.operationSuccess('delete', '用户删除成功');
        loadUsers();
      } else {
        NotificationHelper.error('删除失败', response.data.message || response.data.msg);
      }
    } catch (error) {
      NotificationHelper.networkError(error.response?.status || 500, '删除用户失败');
    }
  };
  const handleResetPassword = async (values) => {
    try {
      const response = await api.put(`/user-management/users/${selectedUser.id}/password`, values);
      if (response.data.success || response.data.code === 0) {
        NotificationHelper.operationSuccess('update', '密码重置成功');
        setPasswordModalVisible(false);
        passwordForm.resetFields();
        setSelectedUser(null);
      } else {
        NotificationHelper.error('重置失败', response.data.message || response.data.msg);
      }
    } catch (error) {
      NotificationHelper.networkError(error.response?.status || 500, '重置密码失败');
    }
  };
  const handleReset2FA = async (userId, username) => {
    try {
      const response = await api.delete(`/user-management/users/${userId}/2fa`);
      if (response.data.success || response.data.code === 0) {
        NotificationHelper.operationSuccess('unbind', `${username} 的二步验证已重置`);
        loadUsers();
      } else {
        NotificationHelper.error('重置失败', response.data.message || response.data.msg);
      }
    } catch (error) {
      NotificationHelper.networkError(error.response?.status || 500, '重置2FA失败');
    }
  };

  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text, record) => (
        <Space>
          {text === 'xiya' && <CrownOutlined style={{ color: '#faad14' }} />}
          <Text strong={text === 'xiya'}>{text}</Text>
          {text === 'xiya' && <Tag color="gold">超级管理员</Tag>}
        </Space>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => (
        <Tag color="blue" icon={<SecurityScanOutlined />}>
          {role === 'admin' ? '管理员' : role}
        </Tag>
      ),
    },
    {
      title: '二步验证',
      dataIndex: 'is_2fa_enabled',
      key: 'is_2fa_enabled',
      render: (enabled) => (
        <Tag color={enabled ? 'green' : 'default'}>
          {enabled ? '已启用' : '未启用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString('zh-CN'),
    },
    {
      title: '最后登录',
      dataIndex: 'last_login',
      key: 'last_login',
      render: (date) => date ? new Date(date).toLocaleString('zh-CN') : '从未登录',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="primary"
            size="small"
            icon={<KeyOutlined />}
            onClick={() => {
              setSelectedUser(record);
              setPasswordModalVisible(true);
            }}
          >
            重置密码
          </Button>
          
          {record.is_2fa_enabled && (
            <Popconfirm
              title="重置二步验证"
              description={`确定要重置 ${record.username} 的二步验证吗？`}
              onConfirm={() => handleReset2FA(record.id, record.username)}
              okText="确定"
              cancelText="取消"
            >
              <Button size="small" icon={<SecurityScanOutlined />}>
                重置2FA
              </Button>
            </Popconfirm>
          )}
          
          {record.username !== 'xiya' && (
            <Popconfirm
              title="删除用户"
              description={`确定要删除用户 ${record.username} 吗？此操作不可恢复！`}
              onConfirm={() => handleDeleteUser(record.id)}
              okText="删除"
              cancelText="取消"
              icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
            >
              <Button danger size="small" icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  // 如果当前用户不是 xiya，显示权限不足的提示
  if (!isXiya) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="权限不足"
          description="此功能仅限 xiya 用户使用"
          type="warning"
          showIcon
        />
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>
            👥 用户管理
          </Title>
          <Button
            type="primary"
            icon={<UserAddOutlined />}
            onClick={() => setModalVisible(true)}
          >
            添加管理员
          </Button>
        </div>

        <Divider />

        {/* 调试信息 */}
        {process.env.NODE_ENV === 'development' && (
          <Alert
            message={`调试信息: 当前用户数量 ${users.length}, 用户列表: ${JSON.stringify(users.map(u => u.username))}`}
            type="info"
            style={{ marginBottom: '16px' }}
            closable
          />
        )}

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
          scroll={{ x: 800 }}
        />

        {/* 添加用户模态框 */}
        <Modal
          title="添加管理员用户"
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            form.resetFields();
          }}
          footer={null}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleAddUser}
          >
            <Form.Item
              label="用户名"
              name="username"
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名至少3位' },
                { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
              ]}
            >
              <Input placeholder="请输入用户名" />
            </Form.Item>

            <Form.Item
              label="密码"
              name="password"
              rules={[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码至少6位' }
              ]}
            >
              <Input.Password placeholder="请输入密码" />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  创建用户
                </Button>
                <Button onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                }}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        {/* 重置密码模态框 */}
        <Modal
          title={`重置 ${selectedUser?.username} 的密码`}
          open={passwordModalVisible}
          onCancel={() => {
            setPasswordModalVisible(false);
            passwordForm.resetFields();
            setSelectedUser(null);
          }}
          footer={null}
        >
          <Form
            form={passwordForm}
            layout="vertical"
            onFinish={handleResetPassword}
          >
            <Form.Item
              label="新密码"
              name="newPassword"
              rules={[
                { required: true, message: '请输入新密码' },
                { min: 6, message: '密码至少6位' }
              ]}
            >
              <Input.Password placeholder="请输入新密码" />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  重置密码
                </Button>
                <Button onClick={() => {
                  setPasswordModalVisible(false);
                  passwordForm.resetFields();
                  setSelectedUser(null);
                }}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    </div>
  );
};

export default UserManagement;