/* QSignHook 登录页面 - 现代友好风格 */

/* 重置和基础样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', <PERSON>l, sans-serif;
}

/* 主容器 - 全屏居中布局 */
.login-container {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 20px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ff9a9e 100%) !important;
  background-attachment: fixed !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  overflow: auto !important;
  z-index: 1000 !important;
  min-height: 100vh !important;
}

/* 背景装饰容器 */
.background-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

/* 浮动装饰元素 */
.floating-hearts {
  position: absolute;
  width: 100%;
  height: 100%;
}

.heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.1);
  font-size: 18px;
  animation: gentleFloat 12s ease-in-out infinite;
  opacity: 0.6;
}

.heart-0 { left: 10%; animation-delay: 0s; }
.heart-1 { left: 20%; animation-delay: 1s; }
.heart-2 { left: 30%; animation-delay: 2s; }
.heart-3 { left: 40%; animation-delay: 3s; }
.heart-4 { left: 50%; animation-delay: 4s; }
.heart-5 { left: 60%; animation-delay: 5s; }
.heart-6 { left: 70%; animation-delay: 0.5s; }
.heart-7 { left: 80%; animation-delay: 1.5s; }
.heart-8 { left: 90%; animation-delay: 2.5s; }
.heart-9 { left: 15%; animation-delay: 3.5s; }
.heart-10 { left: 25%; animation-delay: 4.5s; }
.heart-11 { left: 35%; animation-delay: 5.5s; }

/* 樱花装饰元素 */
.sakura-petals {
  position: absolute;
  width: 100%;
  height: 100%;
}

.petal {
  position: absolute;
  font-size: 14px;
  animation: gentleFall 15s linear infinite;
  opacity: 0.15;
  color: rgba(255, 255, 255, 0.8);
}

.petal-0 { left: 5%; animation-delay: 0s; }
.petal-1 { left: 15%; animation-delay: 1s; }
.petal-2 { left: 25%; animation-delay: 2s; }
.petal-3 { left: 35%; animation-delay: 3s; }
.petal-4 { left: 45%; animation-delay: 4s; }
.petal-5 { left: 55%; animation-delay: 5s; }
.petal-6 { left: 65%; animation-delay: 6s; }
.petal-7 { left: 75%; animation-delay: 7s; }

/* 登录内容容器 */
.login-content {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 420px;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

/* 登录卡片主体 */
.login-card {
  width: 100% !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 20px !important;
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.1),
    0 1px 8px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.9) !important;
  padding: 40px 35px !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

.login-card:hover {
  transform: translateY(-3px);
  box-shadow: 
    0 15px 40px rgba(0, 0, 0, 0.15),
    0 3px 12px rgba(0, 0, 0, 0.1);
}

/* 卡片顶部装饰 */
.login-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #ff9a9e 100%);
  z-index: 1;
}

/* 登录头部区域 */
.login-header {
  text-align: center;
  padding: 0 0 35px 0;
  position: relative;
  z-index: 2;
}

/* Logo 区域 */
.login-logo {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.login-logo-circle {
  width: 85px;
  height: 85px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ff9a9e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 
    0 8px 20px rgba(102, 126, 234, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  border: 2px solid rgba(255, 255, 255, 0.9);
}

.login-logo-circle:hover {
  transform: translateY(-2px) scale(1.03);
  box-shadow: 
    0 12px 25px rgba(102, 126, 234, 0.4),
    0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 欢迎文字区域 */
.welcome-text {
  margin-top: 20px;
}

.login-title {
  margin: 0 0 10px 0 !important;
  background: linear-gradient(135deg, #2c3e50 0%, #667eea 50%, #764ba2 100%);
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
  background-clip: text !important;
  font-weight: 700 !important;
  letter-spacing: 0.5px !important;
  font-size: 24px !important;
  line-height: 1.3 !important;
}

.login-subtitle {
  margin: 0 !important;
  color: #666 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  opacity: 0.8;
  line-height: 1.4;
}

/* 登录表单 */
.login-form {
  margin-top: 30px;
  position: relative;
  z-index: 2;
  width: 100%;
}

/* 表单项间距 */
.login-form .ant-form-item {
  margin-bottom: 20px;
}

.login-form .ant-form-item:last-child {
  margin-bottom: 0;
}

/* 输入框样式 */
.login-input,
.login-input.ant-input-affix-wrapper {
  height: 48px !important;
  border-radius: 12px !important;
  border: 2px solid rgba(221, 221, 221, 0.8) !important;
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(8px) !important;
  padding: 0 18px !important;
  font-size: 15px !important;
  font-weight: 500 !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05) !important;
}

.login-input:hover,
.login-input.ant-input-affix-wrapper:hover {
  border-color: rgba(102, 126, 234, 0.5) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1) !important;
}

.login-input:focus,
.login-input.ant-input-affix-wrapper:focus,
.login-input.ant-input-affix-wrapper-focused {
  border-color: #667eea !important;
  background: rgba(255, 255, 255, 1) !important;
  transform: translateY(-1px) !important;
  box-shadow:
    0 0 0 3px rgba(102, 126, 234, 0.1),
    0 6px 16px rgba(102, 126, 234, 0.15) !important;
}

/* 输入框图标 */
.input-icon {
  color: #667eea !important;
  font-size: 16px !important;
  margin-right: 6px !important;
}

/* 登录按钮 */
.login-button {
  height: 50px !important;
  border-radius: 12px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  margin-top: 12px !important;
  box-shadow: 0 6px 16px rgba(102, 126, 234, 0.3) !important;
  color: white !important;
}

.login-button:hover {
  transform: translateY(-2px) scale(1.01) !important;
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4) !important;
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
}

.login-button:active {
  transform: translateY(-1px) scale(1.005) !important;
}

.login-button:disabled {
  opacity: 0.6 !important;
  transform: none !important;
  cursor: not-allowed !important;
}

/* 登录页脚 */
.login-footer {
  text-align: center;
  margin-top: 30px;
  position: relative;
  z-index: 2;
}

.login-footer span {
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  padding: 6px 14px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* 2FA 模态框样式 */
.twofa-modal .ant-modal-content {
  border-radius: 24px !important;
  overflow: hidden !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3) !important;
}

.twofa-modal .ant-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-bottom: none !important;
  border-radius: 24px 24px 0 0 !important;
  padding: 24px !important;
}

.modal-title {
  color: white !important;
  font-weight: 700 !important;
  font-size: 18px !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.twofa-content {
  text-align: center;
  padding: 30px 20px;
}

.twofa-description {
  color: #666 !important;
  font-weight: 600 !important;
  margin-bottom: 25px !important;
  font-size: 16px !important;
  line-height: 1.5;
}

.twofa-input {
  width: 220px !important;
  height: 60px !important;
  margin: 0 auto 25px auto !important;
  border-radius: 16px !important;
  border: 2px solid rgba(102, 126, 234, 0.3) !important;
  text-align: center !important;
  font-size: 24px !important;
  font-weight: 700 !important;
  letter-spacing: 8px !important;
  background: rgba(255, 255, 255, 0.9) !important;
  transition: all 0.3s ease !important;
}

.twofa-input:focus {
  border-color: #667eea !important;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15) !important;
  transform: scale(1.02);
}

.twofa-button {
  height: 50px !important;
  border-radius: 16px !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  letter-spacing: 1px !important;
  transition: all 0.3s ease !important;
  color: white !important;
}

.twofa-button:hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4) !important;
}

.twofa-button:disabled {
  opacity: 0.5 !important;
  transform: none !important;
}

/* 自定义通知模态框样式 */
.custom-notification-modal .ant-modal-content {
  border-radius: 20px !important;
  overflow: hidden !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1) !important;
}

.custom-notification-modal .ant-modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #ff9a9e 100%) !important;
  border-bottom: none !important;
  border-radius: 20px 20px 0 0 !important;
  padding: 20px 24px !important;
}

.custom-notification-modal .ant-modal-body {
  padding: 30px 24px !important;
  background: rgba(255, 255, 255, 0.98) !important;
}

.custom-notification-modal .ant-modal-footer {
  border-top: none !important;
  padding: 16px 24px 24px 24px !important;
  background: rgba(255, 255, 255, 0.98) !important;
  border-radius: 0 0 20px 20px !important;
}

.custom-notification-modal .ant-btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 12px !important;
  font-weight: 600 !important;
  height: 44px !important;
  min-width: 100px !important;
  transition: all 0.3s ease !important;
}

.custom-notification-modal .ant-btn-primary:hover {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
}

/* 动画关键帧 */
@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.1;
  }
  25% {
    opacity: 0.2;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.05;
  }
  75% {
    opacity: 0.15;
  }
}

@keyframes gentleFall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 0.2;
  }
  50% {
    opacity: 0.1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg);
    opacity: 0;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    padding: 15px !important;
  }
  
  .login-content {
    max-width: 100%;
    padding: 0;
  }
  
  .login-card {
    padding: 30px 25px !important;
    border-radius: 16px !important;
  }
  
  .login-title {
    font-size: 22px !important;
  }
  
  .login-subtitle {
    font-size: 13px !important;
  }
  
  .login-logo-circle {
    width: 75px;
    height: 75px;
  }
  
  .login-input,
  .login-input.ant-input-affix-wrapper {
    height: 44px !important;
    font-size: 14px !important;
    padding: 0 16px !important;
  }
  
  .login-button {
    height: 46px !important;
    font-size: 15px !important;
  }
  
  .twofa-input {
    width: 180px !important;
    height: 50px !important;
    font-size: 18px !important;
    letter-spacing: 4px !important;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 10px !important;
  }
  
  .login-card {
    padding: 25px 20px !important;
    margin: 5px;
  }
  
  .login-title {
    font-size: 20px !important;
    letter-spacing: 0.3px !important;
  }
  
  .login-logo-circle {
    width: 65px;
    height: 65px;
  }
  
  .heart {
    font-size: 16px;
  }
  
  .petal {
    font-size: 12px;
  }
  
  .login-footer span {
    font-size: 12px;
    padding: 5px 12px;
  }
}

/* 微交互优化 */
.login-card *,
.twofa-modal * {
  transition: all 0.25s cubic-bezier(0.25, 0.8, 0.25, 1);
}

/* 无障碍焦点样式 */
.login-input:focus,
.login-button:focus,
.twofa-input:focus,
.twofa-button:focus {
  outline: 2px solid rgba(102, 126, 234, 0.4);
  outline-offset: 2px;
}

/* 加载状态优化 */
.login-button.ant-btn-loading {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%) !important;
}

/* 表单验证错误状态 */
.ant-form-item-has-error .login-input,
.ant-form-item-has-error .login-input.ant-input-affix-wrapper {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.15) !important;
}

.ant-form-item-has-error .login-input:focus,
.ant-form-item-has-error .login-input.ant-input-affix-wrapper:focus {
  border-color: #ff4d4f !important;
  box-shadow: 0 0 0 3px rgba(255, 77, 79, 0.2) !important;
}

/* 通知层级优化 */
.ant-notification,
.ant-message {
  z-index: 99999 !important;
}

.ant-notification-notice,
.ant-message-notice {
  border-radius: 12px !important;
  backdrop-filter: blur(8px) !important;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1) !important;
}
