import React, { useState } from 'react';
import { 
  Card, 
  Typography, 
  Table, 
  Tag, 
  Divider, 
  Anchor, 
  Row, 
  Col,
  Alert,
  Button,
  Space
} from 'antd';
import NotificationHelper from '../components/NotificationHelper';
import {
  ApiOutlined,
  CopyOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ExperimentOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { Link } = Anchor;

const ApiDocs = () => {
  const [copiedText, setCopiedText] = useState('');

  const copyToClipboard = (text, label) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopiedText(label);
      NotificationHelper.operationSuccess('复制成功', `${label} 已复制到剪贴板`);
      setTimeout(() => setCopiedText(''), 2000);
    });
  };

  // 客户端类型数据
  const clientTypes = [
    { type: 'QQ', port: 9511, description: 'QQ客户端' },
    { type: 'qidian', port: 9513, description: '企点客户端' },
    { type: 'qqlite', port: 9512, description: 'QQ轻聊版' },
    { type: 'tim', port: 9514, description: 'TIM客户端' }
  ];

  const clientColumns = [
    { title: 'Type 参数', dataIndex: 'type', key: 'type', render: text => <Tag color="blue">{text}</Tag> },
    { title: '端口', dataIndex: 'port', key: 'port', render: text => <Text code>{text}</Text> },
    { title: '说明', dataIndex: 'description', key: 'description' }
  ];

  // sign接口参数
  const signParams = [
    { param: 'cmd', type: 'string', required: '是', description: '命令类型' },
    { param: 'uin', type: 'string', required: '是', description: '用户QQ号' },
    { param: 'data', type: 'string', required: '是', description: '请求数据' },
    { param: 'seq', type: 'string', required: '否', description: '序列号' },
    { param: 'android_id', type: 'string', required: '否', description: '安卓设备ID' }
  ];

  // Energy接口参数
  const energyParams = [
    { param: 'Type', type: 'string', required: '是', description: '客户端类型 (QQ/qidian/qqlite/tim)' },
    { param: 'data', type: 'string', required: '是', description: '命令数据，格式: CMD_PARAM' },
    { param: 'guid', type: 'string', required: '是', description: '用户GUID' },
    { param: 'version', type: 'string', required: '是', description: '版本信息' }
  ];

  // Get0553接口参数
  const get0553Params = [
    { param: 'Type', type: 'string', required: '是', description: '客户端类型' },
    { param: 'cmd', type: 'string', required: '是', description: '命令类型' },
    { param: 'uin', type: 'string', required: '是', description: '用户QQ号' },
    { param: 'data', type: 'string', required: '是', description: '请求数据' },
    { param: 'ssoseq', type: 'string', required: '是', description: 'SSO序列号' }
  ];

  // QQ身份验证步骤1参数
  const qqVerifyStep1Params = [
    { param: 'version', type: 'string', required: '是', description: '版本信息' },
    { param: 'sig', type: 'string', required: '是', description: '签名信息' },
    { param: 'ticket', type: 'string', required: '是', description: '验证票据' },
    { param: 'randstr', type: 'string', required: '是', description: '随机字符串' },
    { param: 'guid', type: 'string', required: '是', description: '设备GUID' },
    { param: 'qimei', type: 'string', required: '是', description: '设备QIMEI' },
    { param: 'appid', type: 'string', required: '是', description: '应用ID' },
    { param: 'brand', type: 'string', required: '是', description: '设备品牌' },
    { param: 'model', type: 'string', required: '是', description: '设备型号' }
  ];

  // QQ身份验证步骤2参数
  const qqVerifyStep2Params = [
    { param: 'version', type: 'string', required: '是', description: '版本信息' },
    { param: 'sig', type: 'string', required: '是', description: '签名信息' },
    { param: 'randstr', type: 'string', required: '是', description: '随机字符串' },
    { param: 'areaCode', type: 'string', required: '是', description: '区号（从步骤1获得）' },
    { param: 'phoneNum', type: 'string', required: '是', description: '手机号（从步骤1获得）' },
    { param: 'mobile', type: 'string', required: '是', description: '完整手机号（包含区号）' }
  ];

  // QQ身份验证步骤3参数
  const qqVerifyStep3Params = [
    { param: 'version', type: 'string', required: '是', description: '版本信息' },
    { param: 'guid', type: 'string', required: '是', description: '设备GUID' },
    { param: 'qimei', type: 'string', required: '是', description: '设备QIMEI' },
    { param: 'appid', type: 'string', required: '是', description: '应用ID' },
    { param: 'brand', type: 'string', required: '是', description: '设备品牌' },
    { param: 'model', type: 'string', required: '是', description: '设备型号' },
    { param: 'sig', type: 'string', required: '是', description: '签名信息' },
    { param: 'randstr', type: 'string', required: '是', description: '随机字符串' },
    { param: 'areaCode', type: 'string', required: '是', description: '区号' },
    { param: 'phoneNum', type: 'string', required: '是', description: '手机号' },
    { param: 'smsCode', type: 'string', required: '是', description: '短信验证码' }
  ];

  const paramColumns = [
    { title: '参数名', dataIndex: 'param', key: 'param', render: text => <Text code>{text}</Text> },
    { title: '类型', dataIndex: 'type', key: 'type' },
    { title: '必填', dataIndex: 'required', key: 'required', render: text => 
      <Tag color={text === '是' ? 'red' : 'default'}>{text}</Tag> 
    },
    { title: '说明', dataIndex: 'description', key: 'description' }
  ];

  // 响应状态码
  const statusCodes = [
    { code: 0, description: '成功', color: 'success' },
    { code: 1, description: '无效的Type参数', color: 'warning' },
    { code: 3, description: '服务器内部错误', color: 'error' }
  ];

  const statusColumns = [
    { title: 'Code', dataIndex: 'code', key: 'code', render: text => <Text code>{text}</Text> },
    { title: '说明', dataIndex: 'description', key: 'description' },
    { title: '类型', dataIndex: 'color', key: 'color', render: color => 
      <Tag color={color}>{color === 'success' ? '成功' : color === 'warning' ? '警告' : '错误'}</Tag>
    }
  ];

  const CodeBlock = ({ children, language = 'bash', copyLabel }) => (
    <div style={{ position: 'relative' }}>
      <pre style={{ 
        background: '#f6f8fa', 
        padding: '16px', 
        borderRadius: '6px', 
        overflow: 'auto',
        border: '1px solid #e1e4e8'
      }}>
        <code>{children}</code>
      </pre>
      {copyLabel && (
        <Button
          size="small"
          icon={copiedText === copyLabel ? <CheckCircleOutlined /> : <CopyOutlined />}
          style={{ position: 'absolute', top: 8, right: 8 }}
          onClick={() => copyToClipboard(children, copyLabel)}
        >
          {copiedText === copyLabel ? '已复制' : '复制'}
        </Button>
      )}
    </div>
  );

  return (
    <div>
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={18}>
          <div id="overview">
            <Card>
              <Title level={2}>
                <ApiOutlined style={{ marginRight: 8, color: '#1890ff' }} />
                sign服务 API 接口文档
              </Title>
              
              <Alert
                message="API 文档概述"
                description="本文档描述了sign服务器提供的核心接口：/sign、/energy、/Get0553 和 QQ身份验证接口组。这些接口用于处理QQ相关的sign、energy、xwdebugid获取以及QQ账号身份验证。"
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />

              <Title level={3}>基础信息</Title>
              <ul>
                <li><strong>服务器地址</strong>: <Text code>http://localhost:12041</Text></li>
                <li><strong>认证方式</strong>: 使用JWT Token认证，请求头需要包含 <Text code>Authorization: Bearer YOUR_JWT_TOKEN</Text></li>
                <li><strong>内容类型</strong>: <Text code>application/x-www-form-urlencoded</Text></li>
                <li><strong>Token获取</strong>: 通过登录接口获取JWT Token，Token会自动刷新</li>
              </ul>
            </Card>
          </div>

          <div id="client-types" style={{ marginTop: 24 }}>
            <Card title="支持的客户端类型">
              <Table 
                dataSource={clientTypes} 
                columns={clientColumns} 
                pagination={false}
                size="small"
                rowKey="type"
              />
            </Card>
          </div>

          <div id="sign-api" style={{ marginTop: 24 }}>
            <Card title="1. sign接口 /sign">
              <Title level={4}>接口描述</Title>
              <Paragraph>用于获取QQ协议sign，支持各种QQ客户端类型。</Paragraph>
              
              <Title level={4}>请求信息</Title>
              <ul>
                <li><strong>URL</strong>: <Text code>/api/sign</Text></li>
                <li><strong>方法</strong>: <Tag color="orange">POST</Tag></li>
                <li><strong>查询参数</strong>: <Text code>?Type=CLIENT_TYPE</Text></li>
              </ul>

              <Title level={4}>请求头</Title>
              <CodeBlock copyLabel="sign接口请求头">
{`Content-Type: application/x-www-form-urlencoded
Authorization: Bearer YOUR_JWT_TOKEN`}
              </CodeBlock>

              <Title level={4}>请求体参数</Title>
              <Table 
                dataSource={signParams} 
                columns={paramColumns} 
                pagination={false}
                size="small"
                rowKey="param"
              />

              <Title level={4}>请求示例</Title>
              <CodeBlock copyLabel="sign接口请求示例">
{`curl -X POST "http://localhost:12041/api/sign?Type=QQ" \\
  -H "Content-Type: application/x-www-form-urlencoded" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d "cmd=wtlogin.login&uin=123456789&data=1145141919810&seq=123"`}
              </CodeBlock>

              <Title level={4}>响应格式</Title>
              <CodeBlock language="json">
{`{
  "code": 0,
  "msg": "当前sign服务端: QQ",
  "data": {
    "sign": "sign结果",
    "token": "令牌",
    "extra": "额外数据"
  }
}`}
              </CodeBlock>
            </Card>
          </div>

          <div id="energy-api" style={{ marginTop: 24 }}>
            <Card title="2. energy接口 /energy">
              <Title level={4}>接口描述</Title>
              <Paragraph>用于获取QQenergy相关数据。</Paragraph>
              
              <Title level={4}>请求信息</Title>
              <ul>
                <li><strong>URL</strong>: <Text code>/api/energy</Text></li>
                <li><strong>方法</strong>: <Tag color="orange">POST</Tag></li>
                <li><strong>查询参数</strong>: <Text code>?Type=CLIENT_TYPE</Text></li>
              </ul>

              <Title level={4}>请求体参数</Title>
              <Table 
                dataSource={energyParams} 
                columns={paramColumns} 
                pagination={false}
                size="small"
                rowKey="param"
              />

              <Title level={4}>请求示例</Title>
              <CodeBlock copyLabel="energy接口请求示例">
{`curl -X POST "http://localhost:12041/api/energy?Type=QQ" \\
  -H "Content-Type: application/x-www-form-urlencoded" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d "data=810_9&guid=1145141919810&version=6.0.254"`}
              </CodeBlock>

              <Title level={4}>数据处理逻辑</Title>
              <Paragraph>服务器会将请求参数转换为以下格式：</Paragraph>
              <CodeBlock>
{`cmd=DATA&data=000000000010GUID000AVERSION_HEX0000000PARAM00000000&type=energy`}
              </CodeBlock>
            </Card>
          </div>

          <div id="get0553-api" style={{ marginTop: 24 }}>
            <Card title="3. xwdebugid接口 /Get0553">
              <Title level={4}>接口描述</Title>
              <Paragraph>用于获取QQxwdebugid (XwDebugID)。</Paragraph>
              
              <Title level={4}>请求信息</Title>
              <ul>
                <li><strong>URL</strong>: <Text code>/api/Get0553</Text></li>
                <li><strong>方法</strong>: <Tag color="orange">POST</Tag></li>
                <li><strong>查询参数</strong>: <Text code>?Type=CLIENT_TYPE</Text></li>
              </ul>

              <Title level={4}>请求体参数</Title>
              <Table 
                dataSource={get0553Params} 
                columns={paramColumns} 
                pagination={false}
                size="small"
                rowKey="param"
              />

              <Title level={4}>请求示例</Title>
              <CodeBlock copyLabel="xwdebugid接口请求示例">
{`curl -X POST "http://localhost:12041/api/Get0553?Type=QQ" \\
  -H "Content-Type: application/x-www-form-urlencoded" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d "uin=114514&ssoseq=114514&data=114514&cmd=114514"`}
              </CodeBlock>
            </Card>
          </div>

          <div id="qimei-api" style={{ marginTop: 24 }}>
            <Card title="4. QIMEI生成接口组">
              <Alert
                message="QIMEI生成功能"
                description="提供QQ设备标识符(QIMEI)的生成服务，支持单个生成、批量生成和自定义参数"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
              
              <Title level={4}>接口概述</Title>
              <ul>
                <li><strong>生成QIMEI</strong>: 生成单个QIMEI设备标识符</li>
                <li><strong>批量生成</strong>: 一次性生成多个QIMEI</li>
                <li><strong>参数模板</strong>: 获取可配置的参数模板</li>
              </ul>

              <Divider />

              <Title level={4}>4.1 生成QIMEI - /api/qimei/generate</Title>
              <ul>
                <li><strong>URL</strong>: <Text code>/api/qimei/generate</Text></li>
                <li><strong>方法</strong>: <Tag color="orange">POST</Tag></li>
                <li><strong>内容类型</strong>: <Text code>application/json</Text></li>
              </ul>

              <Title level={5}>请求体参数</Title>
              <Table 
                dataSource={[
                  { param: 'params', type: 'object', required: '否', description: '自定义参数对象，可覆盖默认参数' }
                ]} 
                columns={paramColumns} 
                pagination={false}
                size="small"
                rowKey="param"
              />

              <Title level={5}>请求示例</Title>
              <CodeBlock copyLabel="QIMEI生成请求示例">
{`curl -X POST "http://localhost:12041/api/qimei/generate" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d '{
    "params": {
      "brand": "OPPO",
      "model": "PCRT00",
      "androidId": "your_android_id"
    }
  }'`}
              </CodeBlock>

              <Title level={5}>响应示例</Title>
              <CodeBlock copyLabel="QIMEI生成响应示例">
{`{
  "success": true,
  "data": {
    "qimei": "202306010056xxx",
    "qimei36": "e7b3c4d5-a2f1-4e6b-9c8d-7a5b4c3e2f1a",
    "raw": {
      "qimei": "202306010056xxx",
      "qimei36": "e7b3c4d5-a2f1-4e6b-9c8d-7a5b4c3e2f1a",
      "q36": "e7b3c4d5a2f14e6b9c8d7a5b4c3e2f1a",
      "reserved": "{...}"
    }
  },
  "timestamp": "2025-06-09T12:00:00.000Z"
}`}
              </CodeBlock>

              <Divider />

              <Title level={4}>4.2 批量生成QIMEI - /api/qimei/batch</Title>
              <ul>
                <li><strong>URL</strong>: <Text code>/api/qimei/batch</Text></li>
                <li><strong>方法</strong>: <Tag color="orange">POST</Tag></li>
                <li><strong>内容类型</strong>: <Text code>application/json</Text></li>
              </ul>

              <Title level={5}>请求体参数</Title>
              <Table 
                dataSource={[
                  { param: 'count', type: 'number', required: '否', description: '生成数量，默认1，最大10' },
                  { param: 'params', type: 'object', required: '否', description: '自定义参数对象' }
                ]} 
                columns={paramColumns} 
                pagination={false}
                size="small"
                rowKey="param"
              />

              <Title level={5}>请求示例</Title>
              <CodeBlock copyLabel="批量QIMEI生成请求示例">
{`curl -X POST "http://localhost:12041/api/qimei/batch" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \\
  -d '{
    "count": 3,
    "params": {
      "brand": "Xiaomi",
      "model": "MI12"
    }
  }'`}
              </CodeBlock>

              <Divider />

              <Title level={4}>4.3 获取参数模板 - /api/qimei/template</Title>
              <ul>
                <li><strong>URL</strong>: <Text code>/api/qimei/template</Text></li>
                <li><strong>方法</strong>: <Tag color="green">GET</Tag></li>
              </ul>

              <Title level={5}>请求示例</Title>
              <CodeBlock copyLabel="参数模板请求示例">
{`curl "http://localhost:12041/api/qimei/template" \\
  -H "Authorization: Bearer YOUR_JWT_TOKEN"`}
              </CodeBlock>

              <Title level={5}>响应示例</Title>
              <CodeBlock copyLabel="参数模板响应示例">
{`{
  "success": true,
  "data": {
    "androidId": "",
    "platformId": 1,
    "appKey": "0S200MNJT807V3GE",
    "appVersion": "8.9.28.3700",
    "brand": "OPPO",
    "model": "",
    "networkType": "unknown",
    "osVersion": "Android 7.1.2,level 25",
    "packageId": "com.tencent.mobileqq",
    "deviceType": "Pad",
    "userId": "{}"
  },
  "description": "qimei生成参数模板，可以修改这些参数来生成不同的qimei",
  "timestamp": "2025-06-09T12:00:00.000Z"
}`}
              </CodeBlock>
            </Card>
          </div>

          <div id="qq-verify-api" style={{ marginTop: 24 }}>
            <Card title="5. QQ身份验证接口组">
              <Alert
                message="QQ身份验证功能"
                description="提供完整的QQ账号身份验证流程，通过三个步骤实现：初始验证 → 短信发送 → 验证确认"
                type="success"
                showIcon
                style={{ marginBottom: 16 }}
              />
              
              <Title level={4}>验证流程概述</Title>
              <ol>
                <li><strong>步骤1</strong>: 使用验证码票据进行初始验证，获取绑定手机号</li>
                <li><strong>步骤2</strong>: 向获取到的手机号发送短信验证码</li>
                <li><strong>步骤3</strong>: 使用收到的短信验证码完成最终身份验证</li>
              </ol>

              <Divider />

              <Title level={4}>5.1 步骤1 - 初始验证</Title>
              <ul>
                <li><strong>URL</strong>: <Text code>/api/qq-verify/step1</Text></li>
                <li><strong>方法</strong>: <Tag color="orange">POST</Tag></li>
                <li><strong>内容类型</strong>: <Text code>application/json</Text></li>
              </ul>

              <Title level={5}>请求体参数</Title>
              <Table 
                dataSource={qqVerifyStep1Params} 
                columns={paramColumns} 
                pagination={false}
                size="small"
                rowKey="param"
              />

              <Title level={5}>请求示例</Title>
              <CodeBlock copyLabel="QQ验证步骤1请求示例">
{`curl -X POST http://localhost:12041/api/qq-verify/step1 \\
  -H "Content-Type: application/json" \\
  -d '{
    "version": "9.0.80",
    "sig": "你的签名",
    "ticket": "验证码票据",
    "randstr": "随机字符串",
    "guid": "设备GUID",
    "qimei": "设备QIMEI",
    "appid": "2090581062",
    "brand": "Samsung",
    "model": "SM-G975F"
  }'`}
              </CodeBlock>

              <Title level={5}>响应格式</Title>
              <CodeBlock language="json">
{`{
  "code": 0,
  "msg": "success",
  "data": {
    "areaCode": "+86",
    "phoneNum": "138****8888"
  }
}`}
              </CodeBlock>

              <Divider />

              <Title level={4}>5.2 步骤2 - 发送短信验证码</Title>
              <ul>
                <li><strong>URL</strong>: <Text code>/api/qq-verify/step2</Text></li>
                <li><strong>方法</strong>: <Tag color="orange">POST</Tag></li>
                <li><strong>内容类型</strong>: <Text code>application/json</Text></li>
              </ul>

              <Title level={5}>请求体参数</Title>
              <Table 
                dataSource={qqVerifyStep2Params} 
                columns={paramColumns} 
                pagination={false}
                size="small"
                rowKey="param"
              />

              <Title level={5}>请求示例</Title>
              <CodeBlock copyLabel="QQ验证步骤2请求示例">
{`curl -X POST http://localhost:12041/api/qq-verify/step2 \\
  -H "Content-Type: application/json" \\
  -d '{
    "version": "9.0.80",
    "sig": "你的签名",
    "randstr": "随机字符串",
    "areaCode": "+86",
    "phoneNum": "13888888888",
    "mobile": "+861388888888"
  }'`}
              </CodeBlock>

              <Title level={5}>响应格式</Title>
              <CodeBlock language="json">
{`{
  "code": 0,
  "msg": "success",
  "data": {
    "sms": "验证码信息",
    "sendTo": "138****8888"
  }
}`}
              </CodeBlock>

              <Divider />

              <Title level={4}>5.3 步骤3 - 验证短信码</Title>
              <ul>
                <li><strong>URL</strong>: <Text code>/api/qq-verify/step3</Text></li>
                <li><strong>方法</strong>: <Tag color="orange">POST</Tag></li>
                <li><strong>内容类型</strong>: <Text code>application/json</Text></li>
              </ul>

              <Title level={5}>请求体参数</Title>
              <Table 
                dataSource={qqVerifyStep3Params} 
                columns={paramColumns} 
                pagination={false}
                size="small"
                rowKey="param"
              />

              <Title level={5}>请求示例</Title>
              <CodeBlock copyLabel="QQ验证步骤3请求示例">
{`curl -X POST http://localhost:12041/api/qq-verify/step3 \\
  -H "Content-Type: application/json" \\
  -d '{
    "version": "9.0.80",
    "guid": "设备GUID",
    "qimei": "设备QIMEI",
    "appid": "2090581062",
    "brand": "Samsung",
    "model": "SM-G975F",
    "sig": "你的签名",
    "randstr": "随机字符串",
    "areaCode": "+86",
    "phoneNum": "13888888888",
    "smsCode": "123456"
  }'`}
              </CodeBlock>

              <Title level={5}>响应格式</Title>
              <CodeBlock language="json">
{`{
  "code": 0,
  "msg": "success",
  "data": {
    "msg": "身份验证成功",
    "authToken": "verification_completed"
  }
}`}
              </CodeBlock>

              <Alert
                message="重要提醒"
                description={
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    <li>三个步骤必须按顺序执行，不能跳过</li>
                    <li>前一步的结果作为后一步的输入参数</li>
                    <li>需要提供真实的验证码票据和设备信息</li>
                    <li>验证码有时效限制，请及时完成验证流程</li>
                  </ul>
                }
                type="warning"
                showIcon
                style={{ marginTop: 16 }}
              />
            </Card>
          </div>

          <div id="error-handling" style={{ marginTop: 24 }}>
            <Card title="错误处理">
              <Title level={4}>响应状态码</Title>
              <Table 
                dataSource={statusCodes} 
                columns={statusColumns} 
                pagination={false}
                size="small"
                rowKey="code"
              />

              <Title level={4}>通用错误响应</Title>
              <CodeBlock language="json">
{`{
  "code": 1,
  "msg": "错误描述"
}`}
              </CodeBlock>

              <Title level={4}>常见错误</Title>
              <ul>
                <li><strong>认证失败</strong>: 缺少或无效的 <Text code>Authorization: Bearer JWT_TOKEN</Text> 请求头</li>
                <li><strong>Token过期</strong>: JWT Token已过期，需要重新登录</li>
                <li><strong>无效Type</strong>: 不支持的客户端类型</li>
                <li><strong>参数缺失</strong>: 必填参数未提供</li>
                <li><strong>服务不可用</strong>: 对应端口的sign服务未启动</li>
              </ul>
            </Card>
          </div>

          <div id="notes" style={{ marginTop: 24 }}>
            <Card title="注意事项">
              <Alert
                message="重要提醒"
                description={
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    <li>所有请求必须在请求头中包含有效的 <Text code>Authorization: Bearer JWT_TOKEN</Text></li>
                    <li>JWT Token会自动刷新，无需手动管理</li>
                    <li>确保对应Type的本地sign服务正在运行</li>
                    <li>请求数据会经过特定的处理和转换</li>
                    <li>所有请求都会被记录用于统计和监控</li>
                    <li>服务器会记录请求IP，可能有频率限制</li>
                  </ul>
                }
                type="warning"
                showIcon
              />

              <Divider />

              <Title level={4}>服务状态检查</Title>
              <Paragraph>可以通过以下接口检查服务状态：</Paragraph>
              <CodeBlock copyLabel="健康检查">
{`curl http://localhost:12041/health`}
              </CodeBlock>
            </Card>
          </div>
        </Col>

        <Col xs={24} lg={6}>
          <div style={{ position: 'sticky', top: 24 }}>
            <Card title="目录导航" size="small">
              <Anchor
                offsetTop={80}
                items={[
                  { key: 'overview', href: '#overview', title: '概述' },
                  { key: 'client-types', href: '#client-types', title: '客户端类型' },
                  { key: 'sign-api', href: '#sign-api', title: 'sign接口' },
                  { key: 'energy-api', href: '#energy-api', title: 'energy接口' },
                  { key: 'get0553-api', href: '#get0553-api', title: 'xwdebugid接口' },
                  { key: 'qimei-api', href: '#qimei-api', title: 'QIMEI生成' },
                  { key: 'qq-verify-api', href: '#qq-verify-api', title: 'QQ身份验证' },
                  { key: 'error-handling', href: '#error-handling', title: '错误处理' },
                  { key: 'notes', href: '#notes', title: '注意事项' }
                ]}
              />
            </Card>

            <Card title="快速链接" size="small" style={{ marginTop: 16 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button 
                  type="link" 
                  icon={<InfoCircleOutlined />}
                  onClick={() => window.open('http://localhost:12041/health', '_blank')}
                  block
                >
                  服务状态检查
                </Button>
                <Button 
                  type="link" 
                  icon={<ExclamationCircleOutlined />}
                  onClick={() => NotificationHelper.info('提示', '请确保本地sign服务正在运行')}
                  block
                >
                  服务依赖检查
                </Button>
                <Button 
                  type="link" 
                  icon={<ExperimentOutlined />}
                  onClick={() => {
                    window.location.href = '#/qimeiGenerator';
                  }}
                  block
                >
                  QIMEI生成测试
                </Button>
                <Button 
                  type="link" 
                  icon={<ApiOutlined />}
                  onClick={() => NotificationHelper.info('测试提示', '可使用 node test-qq-verify.js 测试QQ身份验证接口')}
                  block
                >
                  QQ验证测试
                </Button>
              </Space>
            </Card>
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default ApiDocs;
