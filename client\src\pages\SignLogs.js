import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Input, 
  Select, 
  DatePicker, 
  Button, 
  Space, 
  Tag, 
  Modal, 
  Descriptions,
  Row,
  Col,
  Statistic,
  Typography,
  Badge,
  Tooltip,
  Divider,
  Progress,
  Spin
} from 'antd';
import { 
  SearchOutlined, 
  ReloadOutlined, 
  EyeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SafetyCertificateOutlined,
  ExperimentOutlined,
  ThunderboltOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  WarningOutlined,
  TrophyOutlined,
  FireOutlined
} from '@ant-design/icons';
import { signLogsAPI } from '../services/api';
import NotificationHelper from '../components/NotificationHelper';
import { normalizeResponse, isResponseSuccess, getResponseData } from '../utils/responseHelper';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Title, Text } = Typography;

const SignLogs = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    success: 0,
    failed: 0,
    successRate: 0,
    todayTotal: 0,
    popularType: '-',
    activeUsers: 0
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [filters, setFilters] = useState({
    ip: '',
    uin: '',
    type: '',
    success: '',
    dateRange: null,
  });
  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState(null);

  useEffect(() => {
    console.log('🚀 SignLogs 组件加载，开始获取数据...');
    fetchData();
    fetchStats();
  }, [pagination.current, pagination.pageSize]);

  // 添加调试信息
  useEffect(() => {
    console.log('🔄 SignLogs - data 状态更新 - 数量:', data?.length);
  }, [data]);

  const fetchStats = async () => {
    try {
      // 获取统计数据
      const response = await signLogsAPI.getSignLogs({ page: 1, limit: 1000 });
      
      // 使用统一的响应格式处理
      const normalizedResponse = normalizeResponse(response);
      
      if (isResponseSuccess(normalizedResponse)) {
        const responseData = getResponseData(normalizedResponse);
        const logs = responseData.logs || [];
        const total = logs.length;
        const success = logs.filter(log => log.success).length;
        const failed = total - success;
        const successRate = total > 0 ? ((success / total) * 100).toFixed(1) : 0;
        
        // 今日数据
        const today = new Date().toISOString().split('T')[0];
        const todayLogs = logs.filter(log => log.timestamp?.startsWith(today));
        
        // 最受欢迎的类型
        const typeCount = logs.reduce((acc, log) => {
          acc[log.type] = (acc[log.type] || 0) + 1;
          return acc;
        }, {});
        const popularType = Object.keys(typeCount).reduce((a, b) => 
          typeCount[a] > typeCount[b] ? a : b, '-'
        );
        
        // 活跃用户数
        const uniqueUins = new Set(logs.filter(log => log.uin).map(log => log.uin));
        
        setStats({
          total,
          success,
          failed,
          successRate: parseFloat(successRate),
          todayTotal: todayLogs.length,
          popularType,
          activeUsers: uniqueUins.size
        });
        
        console.log('📊 SignLogs - 统计数据已更新:', { total, success, failed, successRate: parseFloat(successRate) });
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  const fetchData = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters,
      };

      // 处理日期范围
      if (filters.dateRange && filters.dateRange.length === 2) {
        params.startDate = filters.dateRange[0].format('YYYY-MM-DD');
        params.endDate = filters.dateRange[1].format('YYYY-MM-DD');
      }

      console.log('🔍 SignLogs - 请求参数:', params);
      const response = await signLogsAPI.getSignLogs(params);
      console.log('📨 SignLogs - API响应状态:', response.status);
      
      // 使用统一的响应格式处理
      const normalizedResponse = normalizeResponse(response);
      console.log('📊 SignLogs - 标准化响应检查:', {
        success: normalizedResponse.success,
        hasData: !!normalizedResponse.data,
        dataType: typeof normalizedResponse.data
      });
      
      if (isResponseSuccess(normalizedResponse)) {
        const responseData = getResponseData(normalizedResponse);
        const logs = responseData.logs || [];
        console.log('✅ SignLogs - 成功获取 ' + logs.length + ' 条数据');
        console.log('📋 SignLogs - 设置表格数据:', logs);
        
        setData(logs);
        setPagination(prev => ({
          ...prev,
          total: responseData.pagination?.total || 0,
        }));
      } else {
        console.error('❌ SignLogs - API返回失败:', normalizedResponse.message);
        NotificationHelper.error('获取失败', normalizedResponse.message);
      }
    } catch (error) {
      console.error('❌ SignLogs - 请求异常:', error);
      NotificationHelper.networkError(error.response?.status || 500, '获取签名日志失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, current: 1 }));
    fetchData();
    fetchStats();
  };

  const handleReset = () => {
    setFilters({
      ip: '',
      uin: '',
      type: '',
      success: '',
      dateRange: null,
    });
    setPagination(prev => ({ ...prev, current: 1 }));
    setTimeout(() => {
      fetchData();
      fetchStats();
    }, 100);
  };

  const handleTableChange = (paginationConfig) => {
    setPagination(paginationConfig);
  };

  const showDetail = async (record) => {
    try {
      const response = await signLogsAPI.getSignLogDetail(record.id);
      
      // 使用统一的响应格式处理
      const normalizedResponse = normalizeResponse(response);
      
      if (isResponseSuccess(normalizedResponse)) {
        const responseData = getResponseData(normalizedResponse);
        setSelectedRecord(responseData);
        setDetailVisible(true);
      } else {
        console.error('❌ SignLogs - 获取详情失败:', normalizedResponse);
        NotificationHelper.error('获取失败', normalizedResponse.message || '获取日志详情失败');
      }
    } catch (error) {
      console.error('❌ SignLogs - 获取详情异常:', error);
      NotificationHelper.networkError(error.response?.status || 500, '获取日志详情失败');
    }
  };

  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      render: (text) => (
        <Badge count={text} style={{ backgroundColor: '#52c41a' }} />
      ),
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 140,
      render: (text) => (
        <Text code copyable={{ text }}>{text}</Text>
      ),
    },
    {
      title: 'UIN',
      dataIndex: 'uin',
      key: 'uin',
      width: 120,
      render: (text) => text ? (
        <Tooltip title="用户标识">
          <Text code style={{ color: '#1890ff' }}>{text}</Text>
        </Tooltip>
      ) : (
        <Text type="secondary">-</Text>
      ),
    },
    {
      title: 'CMD',
      dataIndex: 'cmd',
      key: 'cmd',
      width: 100,
      render: (text) => text ? (
        <Tag color="purple" style={{ fontFamily: 'monospace' }}>
          {text}
        </Tag>
      ) : (
        <Text type="secondary">-</Text>
      ),
    },
    {
      title: '签名类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (text) => {
        const typeConfig = {
          'QQ': { color: '#1890ff', icon: '🐧' },
          'qidian': { color: '#52c41a', icon: '📚' },
          'qqlite': { color: '#fa8c16', icon: '🎯' },
          'tim': { color: '#722ed1', icon: '💼' }
        };
        const config = typeConfig[text] || { color: 'default', icon: '❓' };
        
        return (
          <Tag 
            color={config.color} 
            style={{ 
              fontWeight: 'bold',
              border: `2px solid ${config.color}`,
              borderRadius: '8px'
            }}
          >
            {config.icon} {text || '-'}
          </Tag>
        );
      },
    },
    {
      title: '端点',
      dataIndex: 'endpoint',
      key: 'endpoint',
      width: 120,
      render: (text) => (
        <Text style={{ fontSize: '12px', color: '#666' }}>
          {text}
        </Text>
      ),
    },
    {
      title: '签名状态',
      dataIndex: 'success',
      key: 'success',
      width: 100,
      render: (success) => (
        <Tag 
          icon={success ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
          color={success ? 'success' : 'error'}
          style={{ 
            fontWeight: 'bold',
            padding: '4px 8px',
            borderRadius: '6px'
          }}
        >
          {success ? '✅ 成功' : '❌ 失败'}
        </Tag>
      ),
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: 160,
      render: (text) => (
        <Tooltip title={text}>
          <div style={{ fontSize: '12px' }}>
            <ClockCircleOutlined style={{ marginRight: 4, color: '#1890ff' }} />
            {text}
          </div>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          type="primary"
          ghost
          size="small"
          icon={<EyeOutlined />}
          onClick={() => showDetail(record)}
          style={{ borderRadius: '6px' }}
        >
          详情
        </Button>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px', background: '#f0f2f5', minHeight: '100vh' }}>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0, color: '#1890ff' }}>
          <SafetyCertificateOutlined style={{ marginRight: '8px' }} />
          签名服务日志
        </Title>
        <Text type="secondary">监控和分析签名服务的运行状态</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={12} md={6}>
          <Card 
            style={{ 
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              borderRadius: '12px',
              color: 'white'
            }}
          >
            <Statistic
              title={<Text style={{ color: 'rgba(255,255,255,0.8)' }}>总签名次数</Text>}
              value={stats.total}
              prefix={<ThunderboltOutlined style={{ color: '#ffd700' }} />}
              valueStyle={{ color: 'white', fontSize: '28px', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card 
            style={{ 
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
              border: 'none',
              borderRadius: '12px',
              color: 'white'
            }}
          >
            <Statistic
              title={<Text style={{ color: 'rgba(255,255,255,0.8)' }}>成功率</Text>}
              value={stats.successRate}
              suffix="%"
              prefix={<TrophyOutlined style={{ color: '#ffd700' }} />}
              valueStyle={{ color: 'white', fontSize: '28px', fontWeight: 'bold' }}
            />
            <Progress 
              percent={stats.successRate} 
              showInfo={false} 
              strokeColor="rgba(255,255,255,0.6)"
              trailColor="rgba(255,255,255,0.2)"
              style={{ marginTop: '8px' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card 
            style={{ 
              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
              border: 'none',
              borderRadius: '12px',
              color: 'white'
            }}
          >
            <Statistic
              title={<Text style={{ color: 'rgba(255,255,255,0.8)' }}>今日签名</Text>}
              value={stats.todayTotal}
              prefix={<FireOutlined style={{ color: '#ffd700' }} />}
              valueStyle={{ color: 'white', fontSize: '28px', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card 
            style={{ 
              background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
              border: 'none',
              borderRadius: '12px',
              color: 'white'
            }}
          >
            <Statistic
              title={<Text style={{ color: 'rgba(255,255,255,0.8)' }}>活跃用户</Text>}
              value={stats.activeUsers}
              prefix={<TeamOutlined style={{ color: '#ffd700' }} />}
              valueStyle={{ color: 'white', fontSize: '28px', fontWeight: 'bold' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 详细统计 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} md={8}>
          <Card
            style={{ borderRadius: '12px', border: '1px solid #e8f4fd' }}
            bodyStyle={{ padding: '20px' }}
          >
            <div style={{ textAlign: 'center' }}>
              <CheckCircleOutlined style={{ fontSize: '32px', color: '#52c41a', marginBottom: '8px' }} />
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>{stats.success}</div>
              <div style={{ color: '#666', fontSize: '14px' }}>成功签名</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card
            style={{ borderRadius: '12px', border: '1px solid #fff2e8' }}
            bodyStyle={{ padding: '20px' }}
          >
            <div style={{ textAlign: 'center' }}>
              <CloseCircleOutlined style={{ fontSize: '32px', color: '#ff4d4f', marginBottom: '8px' }} />
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ff4d4f' }}>{stats.failed}</div>
              <div style={{ color: '#666', fontSize: '14px' }}>失败签名</div>
            </div>
          </Card>
        </Col>
        <Col xs={24} md={8}>
          <Card
            style={{ borderRadius: '12px', border: '1px solid #f6ffed' }}
            bodyStyle={{ padding: '20px' }}
          >
            <div style={{ textAlign: 'center' }}>
              <ExperimentOutlined style={{ fontSize: '32px', color: '#722ed1', marginBottom: '8px' }} />
              <div style={{ fontSize: '18px', fontWeight: 'bold', color: '#722ed1' }}>{stats.popularType}</div>
              <div style={{ color: '#666', fontSize: '14px' }}>最受欢迎类型</div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 搜索过滤器 */}
      <Card 
        style={{ 
          marginBottom: '24px', 
          borderRadius: '12px',
          background: 'linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%)',
          border: 'none'
        }}
        bodyStyle={{ padding: '20px' }}
      >
        <Title level={4} style={{ margin: '0 0 16px 0', color: '#2d3436' }}>
          <SearchOutlined style={{ marginRight: '8px' }} />
          搜索过滤器
        </Title>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={6}>
            <div style={{ marginBottom: '8px' }}>
              <Text strong style={{ color: '#2d3436' }}>IP地址</Text>
            </div>
            <Input
              placeholder="输入IP地址"
              value={filters.ip}
              onChange={(e) => setFilters(prev => ({ ...prev, ip: e.target.value }))}
              allowClear
              style={{ borderRadius: '8px' }}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <div style={{ marginBottom: '8px' }}>
              <Text strong style={{ color: '#2d3436' }}>用户UIN</Text>
            </div>
            <Input
              placeholder="输入UIN"
              value={filters.uin}
              onChange={(e) => setFilters(prev => ({ ...prev, uin: e.target.value }))}
              allowClear
              style={{ borderRadius: '8px' }}
            />
          </Col>
          <Col xs={24} sm={12} md={6}>
            <div style={{ marginBottom: '8px' }}>
              <Text strong style={{ color: '#2d3436' }}>签名类型</Text>
            </div>
            <Select
              placeholder="选择类型"
              value={filters.type}
              onChange={(value) => setFilters(prev => ({ ...prev, type: value }))}
              allowClear
              style={{ width: '100%', borderRadius: '8px' }}
            >
              <Option value="QQ">🐧 QQ</Option>
              <Option value="qidian">📚 qidian</Option>
              <Option value="qqlite">🎯 qqlite</Option>
              <Option value="tim">💼 tim</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <div style={{ marginBottom: '8px' }}>
              <Text strong style={{ color: '#2d3436' }}>签名状态</Text>
            </div>
            <Select
              placeholder="选择状态"
              value={filters.success}
              onChange={(value) => setFilters(prev => ({ ...prev, success: value }))}
              allowClear
              style={{ width: '100%', borderRadius: '8px' }}
            >
              <Option value="true">✅ 成功</Option>
              <Option value="false">❌ 失败</Option>
            </Select>
          </Col>
          <Col xs={24} md={12}>
            <div style={{ marginBottom: '8px' }}>
              <Text strong style={{ color: '#2d3436' }}>时间范围</Text>
            </div>
            <RangePicker
              value={filters.dateRange}
              onChange={(dates) => setFilters(prev => ({ ...prev, dateRange: dates }))}
              style={{ width: '100%', borderRadius: '8px' }}
            />
          </Col>
          <Col xs={24} md={12}>
            <div style={{ marginBottom: '8px' }}>
              <Text strong style={{ color: '#2d3436' }}>操作</Text>
            </div>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={handleSearch}
                style={{ borderRadius: '8px', background: '#0984e3', borderColor: '#0984e3' }}
              >
                搜索
              </Button>
              <Button 
                onClick={handleReset}
                style={{ borderRadius: '8px' }}
              >
                重置
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => { fetchData(); fetchStats(); }}
                style={{ borderRadius: '8px' }}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 数据表格 */}
      <Card 
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <SafetyCertificateOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
            <span>签名日志记录</span>
            <Badge 
              count={pagination.total} 
              style={{ 
                backgroundColor: '#52c41a', 
                marginLeft: '8px' 
              }} 
            />
          </div>
        }
        style={{ borderRadius: '12px', border: '1px solid #e8f4fd' }}
        bodyStyle={{ padding: '0' }}
      >
        <Table
          columns={columns}
          dataSource={data}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            style: { padding: '16px' }
          }}
          onChange={handleTableChange}
          scroll={{ x: 1200 }}
          rowClassName={(record, index) => 
            index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
          }
          locale={{
            emptyText: data.length === 0 ? (
              <div style={{ padding: '20px', textAlign: 'center' }}>
                <div>📝 暂无签名日志数据</div>
                <div style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
                  数据状态: {data ? `数组长度 ${data.length}` : '未加载'}
                </div>
              </div>
            ) : undefined
          }}
        />
      </Card>

      <Modal
        title={
          <div style={{ 
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            margin: '-24px -24px 24px -24px',
            padding: '20px 24px',
            color: 'white',
            borderRadius: '6px 6px 0 0'
          }}>
            <EyeOutlined style={{ marginRight: '8px' }} />
            签名日志详情
            {selectedRecord && (
              <Tag 
                style={{ marginLeft: '12px' }}
                color={selectedRecord.success ? 'success' : 'error'}
              >
                {selectedRecord.success ? '✅ 成功' : '❌ 失败'}
              </Tag>
            )}
          </div>
        }
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button 
            key="close" 
            onClick={() => setDetailVisible(false)}
            style={{ borderRadius: '8px' }}
          >
            关闭
          </Button>
        ]}
        width={900}
        style={{ top: 20 }}
      >
        {selectedRecord && (
          <div style={{ padding: '20px 0' }}>
            <Descriptions 
              column={2} 
              bordered
              labelStyle={{ 
                background: '#f8f9fa',
                fontWeight: 'bold',
                color: '#2c3e50'
              }}
              contentStyle={{ 
                background: '#fff',
                color: '#34495e'
              }}
            >
              <Descriptions.Item label="🆔 记录ID" span={1}>
                <Badge count={selectedRecord.id} style={{ backgroundColor: '#52c41a' }} />
              </Descriptions.Item>
              <Descriptions.Item label="🌐 IP地址" span={1}>
                <Text code copyable={{ text: selectedRecord.ip }}>
                  {selectedRecord.ip}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="👤 用户UIN" span={1}>
                {selectedRecord.uin ? (
                  <Text code style={{ color: '#1890ff' }}>{selectedRecord.uin}</Text>
                ) : (
                  <Text type="secondary">未提供</Text>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="⚡ 命令CMD" span={1}>
                {selectedRecord.cmd ? (
                  <Tag color="purple" style={{ fontFamily: 'monospace' }}>
                    {selectedRecord.cmd}
                  </Tag>
                ) : (
                  <Text type="secondary">无</Text>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="🎯 签名类型" span={1}>
                {(() => {
                  const typeConfig = {
                    'QQ': { color: '#1890ff', icon: '🐧' },
                    'qidian': { color: '#52c41a', icon: '📚' },
                    'qqlite': { color: '#fa8c16', icon: '🎯' },
                    'tim': { color: '#722ed1', icon: '💼' }
                  };
                  const config = typeConfig[selectedRecord.type] || { color: 'default', icon: '❓' };
                  
                  return (
                    <Tag 
                      color={config.color} 
                      style={{ 
                        fontWeight: 'bold',
                        border: `2px solid ${config.color}`,
                        borderRadius: '8px',
                        padding: '4px 8px'
                      }}
                    >
                      {config.icon} {selectedRecord.type || '-'}
                    </Tag>
                  );
                })()}
              </Descriptions.Item>
              <Descriptions.Item label="📡 服务端点" span={1}>
                <Text style={{ fontSize: '12px', color: '#666' }}>
                  {selectedRecord.endpoint}
                </Text>
              </Descriptions.Item>
              <Descriptions.Item label="📊 执行状态" span={2}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <Tag 
                    icon={selectedRecord.success ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
                    color={selectedRecord.success ? 'success' : 'error'}
                    style={{ 
                      fontWeight: 'bold',
                      padding: '6px 12px',
                      borderRadius: '8px',
                      fontSize: '14px'
                    }}
                  >
                    {selectedRecord.success ? '✅ 签名成功' : '❌ 签名失败'}
                  </Tag>
                  {!selectedRecord.success && (
                    <Tooltip title="查看错误详情">
                      <WarningOutlined style={{ color: '#ff4d4f', fontSize: '16px' }} />
                    </Tooltip>
                  )}
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="🕒 执行时间" span={2}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <ClockCircleOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                  <Text strong>{selectedRecord.timestamp}</Text>
                </div>
              </Descriptions.Item>
              {selectedRecord.error_msg && (
                <Descriptions.Item label="❌ 错误信息" span={2}>
                  <Text type="danger" style={{ 
                    background: '#fff2f0', 
                    padding: '8px', 
                    borderRadius: '4px',
                    display: 'block',
                    border: '1px solid #ffccc7'
                  }}>
                    {selectedRecord.error_msg}
                  </Text>
                </Descriptions.Item>
              )}
            </Descriptions>

            <Divider orientation="left" style={{ marginTop: '24px' }}>
              <Text strong style={{ color: '#1890ff' }}>📋 技术详情</Text>
            </Divider>

            <Row gutter={[16, 16]}>
              <Col xs={24} lg={12}>
                <Card 
                  title={
                    <div style={{ color: '#52c41a' }}>
                      📤 请求数据
                    </div>
                  }
                  size="small" 
                  style={{ borderColor: '#b7eb8f' }}
                >
                  <pre style={{ 
                    whiteSpace: 'pre-wrap', 
                    maxHeight: 300, 
                    overflow: 'auto',
                    background: '#f6ffed',
                    padding: '12px',
                    borderRadius: '6px',
                    fontSize: '12px',
                    lineHeight: '1.4',
                    margin: 0
                  }}>
                    {selectedRecord.request_data || '无请求数据'}
                  </pre>
                </Card>
              </Col>
              <Col xs={24} lg={12}>
                <Card 
                  title={
                    <div style={{ color: '#1890ff' }}>
                      📥 响应数据
                    </div>
                  }
                  size="small" 
                  style={{ borderColor: '#91d5ff' }}
                >
                  <pre style={{ 
                    whiteSpace: 'pre-wrap', 
                    maxHeight: 300, 
                    overflow: 'auto',
                    background: '#e6f7ff',
                    padding: '12px',
                    borderRadius: '6px',
                    fontSize: '12px',
                    lineHeight: '1.4',
                    margin: 0
                  }}>
                    {selectedRecord.response_data || '无响应数据'}
                  </pre>
                </Card>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SignLogs;
