-- 风控系统配置表
CREATE TABLE IF NOT EXISTS `risk_control_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL COMMENT '配置键名',
  `setting_value` text COMMENT '配置值',
  `setting_type` enum('boolean','string','number','json') DEFAULT 'string' COMMENT '配置类型',
  `description` varchar(255) DEFAULT NULL COMMENT '配置描述',
  `category` varchar(50) DEFAULT 'general' COMMENT '配置分类',
  `is_enabled` tinyint(1) DEFAULT 1 COMMENT '是否启用',
  `created_time` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_setting_key` (`setting_key`),
  KEY `idx_category` (`category`),
  KEY `idx_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='风控系统配置表';

-- 插入默认配置
INSERT INTO `risk_control_settings` (`setting_key`, `setting_value`, `setting_type`, `description`, `category`) VALUES
('enable_risk_detection', 'true', 'boolean', '是否启用风控检测', 'detection'),
('enable_keyword_filter', 'true', 'boolean', '是否启用关键词过滤', 'detection'),
('enable_ip_blacklist', 'true', 'boolean', '是否启用IP黑名单', 'blacklist'),
('enable_user_blacklist', 'true', 'boolean', '是否启用用户黑名单', 'blacklist'),
('enable_auto_block', 'true', 'boolean', '是否启用自动拦截', 'action'),
('enable_whitelist_bypass', 'true', 'boolean', '是否启用白名单绕过', 'whitelist'),
('max_risk_score', '80', 'number', '最大风险评分阈值', 'threshold'),
('auto_block_threshold', '70', 'number', '自动拦截阈值', 'threshold'),
('batch_operation_limit', '100', 'number', '批量操作限制数量', 'batch'),
('enable_batch_review', 'true', 'boolean', '是否启用批量审核', 'batch'),
('enable_batch_delete', 'true', 'boolean', '是否启用批量删除', 'batch'),
('enable_batch_blacklist', 'true', 'boolean', '是否启用批量加入黑名单', 'batch');
