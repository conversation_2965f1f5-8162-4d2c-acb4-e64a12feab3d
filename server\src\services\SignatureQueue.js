const EventEmitter = require('events');
const colors = require('colors');

/**
 * 高性能签名队列管理器
 * 支持优先级队列、并发控制、重试机制、负载均衡
 */
class SignatureQueue extends EventEmitter {
    constructor(options = {}) {
        super();
        
        // 配置参数
        this.maxConcurrency = options.maxConcurrency || 50; // 最大并发数
        this.maxQueueSize = options.maxQueueSize || 1000; // 最大队列长度
        this.requestTimeout = options.requestTimeout || 30000; // 请求超时时间
        this.retryAttempts = options.retryAttempts || 3; // 重试次数
        this.retryDelay = options.retryDelay || 1000; // 重试延迟
        
        // 队列状态
        this.queues = {
            high: [], // 高优先级队列
            normal: [], // 普通优先级队列
            low: [] // 低优先级队列
        };
        
        this.processing = new Map(); // 正在处理的请求
        this.completed = new Map(); // 已完成的请求（用于去重）
        this.failed = new Map(); // 失败的请求
        
        // 统计信息
        this.stats = {
            totalRequests: 0,
            completedRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            currentConcurrency: 0,
            queueLength: 0
        };
        
        // 启动队列处理器
        this.startProcessor();
        
        // 定期清理过期数据
        this.startCleanup();
        
        console.log(`🚀 签名队列管理器启动 - 最大并发: ${this.maxConcurrency}`.green);
    }
    
    /**
     * 添加签名请求到队列
     */
    async addRequest(requestData, priority = 'normal') {
        return new Promise((resolve, reject) => {
            // 检查队列长度
            const totalQueueLength = this.getTotalQueueLength();
            if (totalQueueLength >= this.maxQueueSize) {
                return reject(new Error('队列已满，请稍后重试'));
            }
            
            // 生成唯一请求ID（包含时间戳确保唯一性）
            const requestId = this.generateRequestId(requestData);

            // 对于签名服务，允许相同的请求多次执行，不进行重复检查
            
            // 创建请求对象
            const request = {
                id: requestId,
                data: requestData,
                priority,
                timestamp: Date.now(),
                attempts: 0,
                resolve,
                reject,
                timeout: null
            };
            
            // 设置超时
            request.timeout = setTimeout(() => {
                this.handleTimeout(request);
            }, this.requestTimeout);
            
            // 添加到对应优先级队列
            this.queues[priority].push(request);
            this.stats.totalRequests++;
            this.stats.queueLength = this.getTotalQueueLength();
            
            this.emit('requestAdded', { requestId, priority, queueLength: this.stats.queueLength });
            
            // 触发处理
            this.processNext();
        });
    }
    
    /**
     * 启动队列处理器
     */
    startProcessor() {
        setInterval(() => {
            this.processNext();
        }, 10); // 每10ms检查一次队列
    }
    
    /**
     * 处理下一个请求
     */
    async processNext() {
        // 检查并发限制
        if (this.stats.currentConcurrency >= this.maxConcurrency) {
            return;
        }
        
        // 按优先级获取下一个请求
        const request = this.getNextRequest();
        if (!request) {
            return;
        }
        
        // 开始处理
        this.stats.currentConcurrency++;
        this.processing.set(request.id, request);
        this.stats.queueLength = this.getTotalQueueLength();
        
        try {
            const startTime = Date.now();
            const result = await this.executeRequest(request);
            const responseTime = Date.now() - startTime;
            
            // 更新统计信息
            this.updateStats(responseTime, true);
            
            // 清理并返回结果
            this.completeRequest(request, result);
            
        } catch (error) {
            await this.handleRequestError(request, error);
        }
    }
    
    /**
     * 按优先级获取下一个请求
     */
    getNextRequest() {
        // 高优先级优先
        if (this.queues.high.length > 0) {
            return this.queues.high.shift();
        }
        
        // 普通优先级
        if (this.queues.normal.length > 0) {
            return this.queues.normal.shift();
        }
        
        // 低优先级
        if (this.queues.low.length > 0) {
            return this.queues.low.shift();
        }
        
        return null;
    }
    
    /**
     * 执行签名请求
     */
    async executeRequest(request) {
        // 这个方法将被子类重写或通过依赖注入提供
        throw new Error('executeRequest method must be implemented');
    }
    
    /**
     * 处理请求错误
     */
    async handleRequestError(request, error) {
        request.attempts++;
        
        // 检查是否需要重试
        if (request.attempts < this.retryAttempts) {
            console.log(`⚠️  请求 ${request.id} 失败，准备重试 (${request.attempts}/${this.retryAttempts})`.yellow);
            
            // 延迟后重新加入队列
            setTimeout(() => {
                this.queues[request.priority].unshift(request);
            }, this.retryDelay * request.attempts);
            
        } else {
            // 重试次数用完，标记为失败
            console.log(`❌ 请求 ${request.id} 最终失败: ${error.message}`.red);
            this.updateStats(0, false);
            this.failRequest(request, error);
        }
        
        // 清理处理状态
        this.processing.delete(request.id);
        this.stats.currentConcurrency--;
    }
    
    /**
     * 完成请求
     */
    completeRequest(request, result) {
        clearTimeout(request.timeout);
        this.processing.delete(request.id);
        this.completed.set(request.id, { result, timestamp: Date.now() });
        this.stats.currentConcurrency--;
        this.stats.completedRequests++;
        
        request.resolve(result);
        this.emit('requestCompleted', { requestId: request.id, result });
    }
    
    /**
     * 失败请求
     */
    failRequest(request, error) {
        clearTimeout(request.timeout);
        this.processing.delete(request.id);
        this.failed.set(request.id, { error, timestamp: Date.now() });
        this.stats.currentConcurrency--;
        this.stats.failedRequests++;
        
        request.reject(error);
        this.emit('requestFailed', { requestId: request.id, error });
    }
    
    /**
     * 处理超时
     */
    handleTimeout(request) {
        if (this.processing.has(request.id)) {
            this.handleRequestError(request, new Error('请求超时'));
        }
    }
    
    /**
     * 生成请求ID - 包含时间戳和随机数确保唯一性
     */
    generateRequestId(requestData) {
        const crypto = require('crypto');
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2);
        const content = JSON.stringify(requestData) + timestamp + random;
        return crypto.createHash('md5').update(content).digest('hex');
    }
    
    /**
     * 获取总队列长度
     */
    getTotalQueueLength() {
        return this.queues.high.length + this.queues.normal.length + this.queues.low.length;
    }
    
    /**
     * 更新统计信息
     */
    updateStats(responseTime, success) {
        if (success) {
            // 计算平均响应时间
            const totalTime = this.stats.averageResponseTime * this.stats.completedRequests + responseTime;
            this.stats.averageResponseTime = Math.round(totalTime / (this.stats.completedRequests + 1));
        }
    }
    
    /**
     * 启动清理任务
     */
    startCleanup() {
        setInterval(() => {
            this.cleanup();
        }, 60000); // 每分钟清理一次
    }
    
    /**
     * 清理过期数据
     */
    cleanup() {
        const now = Date.now();
        const expireTime = 5 * 60 * 1000; // 5分钟过期
        
        // 清理已完成的请求
        for (const [id, data] of this.completed.entries()) {
            if (now - data.timestamp > expireTime) {
                this.completed.delete(id);
            }
        }
        
        // 清理失败的请求
        for (const [id, data] of this.failed.entries()) {
            if (now - data.timestamp > expireTime) {
                this.failed.delete(id);
            }
        }
    }
    
    /**
     * 获取队列状态
     */
    getStatus() {
        return {
            ...this.stats,
            queueLength: this.getTotalQueueLength(),
            queues: {
                high: this.queues.high.length,
                normal: this.queues.normal.length,
                low: this.queues.low.length
            },
            processing: this.processing.size
        };
    }
    
    /**
     * 清空队列
     */
    clear() {
        // 拒绝所有等待中的请求
        Object.values(this.queues).forEach(queue => {
            queue.forEach(request => {
                clearTimeout(request.timeout);
                request.reject(new Error('队列已清空'));
            });
            queue.length = 0;
        });
        
        // 清理处理中的请求
        for (const request of this.processing.values()) {
            clearTimeout(request.timeout);
            request.reject(new Error('队列已清空'));
        }
        this.processing.clear();
        
        // 重置统计
        this.stats.queueLength = 0;
        this.stats.currentConcurrency = 0;
    }
}

module.exports = SignatureQueue;
