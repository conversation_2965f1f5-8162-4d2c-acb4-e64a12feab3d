const cron = require('node-cron');
const colors = require('colors');

/**
 * 数据库清理工具
 * 负责定时清理数据库中的旧数据
 */
class DatabaseCleaner {
    constructor(dataModel) {
        this.dataModel = dataModel;
        this.isRunning = false;
        this.cleanupJob = null;
        this.cleanupDays = 7; // 默认保留7天
    }

    /**
     * 启动定时清理任务
     */
    start() {
        try {
            // 每天凌晨3点执行清理任务
            this.cleanupJob = cron.schedule('0 3 * * *', async () => {
                await this.performCleanup();
            }, {
                scheduled: false,
                timezone: 'Asia/Shanghai'
            });

            this.cleanupJob.start();
            console.log('🕐 数据库定时清理任务已启动 (每天凌晨3点执行)'.green);
            
            // 启动时执行一次清理检查
            this.checkAndCleanup();
            
        } catch (error) {
            console.error('启动数据库清理任务失败:'.red, error.message);
        }
    }

    /**
     * 停止定时清理任务
     */
    stop() {
        if (this.cleanupJob) {
            this.cleanupJob.stop();
            this.cleanupJob = null;
            console.log('🛑 数据库定时清理任务已停止'.yellow);
        }
    }

    /**
     * 设置清理天数
     */
    setCleanupDays(days) {
        if (days > 0) {
            this.cleanupDays = days;
            console.log(`📅 数据库清理天数已设置为: ${days} 天`.cyan);
        }
    }

    /**
     * 检查并执行清理
     */
    async checkAndCleanup() {
        try {
            // 获取自动清理设置
            const autoClean = await this.dataModel.getSetting('auto_clean_logs', 'true');
            const cleanDays = await this.dataModel.getSetting('clean_logs_days', '7');
            
            if (autoClean === 'true') {
                this.cleanupDays = parseInt(cleanDays) || 7;
                console.log(`🔍 检查到自动清理已启用，保留天数: ${this.cleanupDays} 天`.cyan);
                
                // 执行清理
                await this.performCleanup();
            } else {
                console.log('⏸️  自动清理已禁用'.gray);
            }
        } catch (error) {
            console.error('检查清理设置失败:'.red, error.message);
        }
    }

    /**
     * 执行清理操作
     */
    async performCleanup() {
        if (this.isRunning) {
            console.log('⚠️  清理任务正在运行中，跳过本次执行'.yellow);
            return;
        }

        this.isRunning = true;
        const startTime = Date.now();

        try {
            console.log('🧹 开始执行数据库清理任务...'.cyan);
            
            // 执行清理
            const result = await this.dataModel.cleanOldLogs(this.cleanupDays);
            
            if (result.success) {
                const duration = Date.now() - startTime;
                console.log(`✅ 数据库清理完成，耗时: ${duration}ms`.green);
                console.log(`📊 清理统计:`.cyan);
                console.log(`   - 签名日志: ${result.details.signLogs} 条`.white);
                console.log(`   - 风控记录: ${result.details.riskRecords} 条`.white);
                console.log(`   - 未授权日志: ${result.details.unauthorizedLogs} 条`.white);
                console.log(`   - 消息日志: ${result.details.messageLogs} 条`.white);
                console.log(`   - 总计: ${result.totalCleaned} 条`.green);
                
                // 记录清理日志到系统设置
                await this.dataModel.setSetting('last_cleanup_time', new Date().toISOString(), 'string', '最后清理时间');
                await this.dataModel.setSetting('last_cleanup_count', result.totalCleaned.toString(), 'string', '最后清理数量');
                
            } else {
                console.error('❌ 数据库清理失败:'.red, result.error);
            }
            
        } catch (error) {
            console.error('执行数据库清理时出错:'.red, error.message);
        } finally {
            this.isRunning = false;
        }
    }

    /**
     * 手动执行清理
     */
    async manualCleanup(days = null) {
        const cleanupDays = days || this.cleanupDays;
        
        console.log(`🔧 手动执行数据库清理 (${cleanupDays} 天前的数据)...`.yellow);
        
        try {
            const result = await this.dataModel.cleanOldLogs(cleanupDays);
            
            if (result.success) {
                console.log(`✅ 手动清理完成，共清理 ${result.totalCleaned} 条记录`.green);
                return result;
            } else {
                console.error('❌ 手动清理失败:'.red, result.error);
                return result;
            }
        } catch (error) {
            console.error('手动清理时出错:'.red, error.message);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取清理状态
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            isScheduled: this.cleanupJob !== null,
            cleanupDays: this.cleanupDays,
            nextRun: this.cleanupJob ? '每天凌晨3点' : '未安排'
        };
    }

    /**
     * 获取清理统计信息
     */
    async getCleanupStats() {
        try {
            const lastCleanupTime = await this.dataModel.getSetting('last_cleanup_time', null);
            const lastCleanupCount = await this.dataModel.getSetting('last_cleanup_count', '0');
            const autoClean = await this.dataModel.getSetting('auto_clean_logs', 'true');
            const cleanDays = await this.dataModel.getSetting('clean_logs_days', '7');
            
            return {
                lastCleanupTime,
                lastCleanupCount: parseInt(lastCleanupCount) || 0,
                autoCleanEnabled: autoClean === 'true',
                cleanupDays: parseInt(cleanDays) || 7,
                status: this.getStatus()
            };
        } catch (error) {
            console.error('获取清理统计失败:'.red, error.message);
            return {
                lastCleanupTime: null,
                lastCleanupCount: 0,
                autoCleanEnabled: false,
                cleanupDays: 7,
                status: this.getStatus()
            };
        }
    }

    /**
     * 预览将要清理的数据量
     */
    async previewCleanup(days = null) {
        const cleanupDays = days || this.cleanupDays;
        
        try {
            console.log(`🔍 预览 ${cleanupDays} 天前的数据量...`.cyan);
            
            // 查询各表的数据量
            const signLogsCount = await this.dataModel.db.query(
                'SELECT COUNT(*) as count FROM sign_logs WHERE request_time < DATE_SUB(NOW(), INTERVAL ? DAY)',
                [cleanupDays]
            );
            
            const riskRecordsCount = await this.dataModel.db.query(
                'SELECT COUNT(*) as count FROM risk_records WHERE created_time < DATE_SUB(NOW(), INTERVAL ? DAY)',
                [cleanupDays]
            );
            
            const unauthorizedLogsCount = await this.dataModel.db.query(
                'SELECT COUNT(*) as count FROM unauthorized_logs WHERE attempt_time < DATE_SUB(NOW(), INTERVAL ? DAY)',
                [cleanupDays]
            );
            
            const messageLogsCount = await this.dataModel.db.query(
                'SELECT COUNT(*) as count FROM message_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)',
                [cleanupDays]
            );
            
            const preview = {
                signLogs: signLogsCount[0].count,
                riskRecords: riskRecordsCount[0].count,
                unauthorizedLogs: unauthorizedLogsCount[0].count,
                messageLogs: messageLogsCount[0].count
            };
            
            preview.total = preview.signLogs + preview.riskRecords + 
                           preview.unauthorizedLogs + preview.messageLogs;
            
            console.log(`📊 预览结果 (${cleanupDays} 天前):`.cyan);
            console.log(`   - 签名日志: ${preview.signLogs} 条`.white);
            console.log(`   - 风控记录: ${preview.riskRecords} 条`.white);
            console.log(`   - 未授权日志: ${preview.unauthorizedLogs} 条`.white);
            console.log(`   - 消息日志: ${preview.messageLogs} 条`.white);
            console.log(`   - 总计: ${preview.total} 条`.yellow);
            
            return preview;
            
        } catch (error) {
            console.error('预览清理数据失败:'.red, error.message);
            return {
                signLogs: 0,
                riskRecords: 0,
                unauthorizedLogs: 0,
                messageLogs: 0,
                total: 0,
                error: error.message
            };
        }
    }
}

module.exports = DatabaseCleaner;
