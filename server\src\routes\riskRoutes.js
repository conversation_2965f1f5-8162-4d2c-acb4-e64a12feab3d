const express = require('express');
const router = express.Router();
const mysqlModel = require('../models/mysql-real-model');
const multer = require('multer');
const colors = require('colors');
const webSocketService = require('../services/websocket-service');

// 配置文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB限制
  },
  fileFilter: (req, file, cb) => {
    // 只允许文本文件和CSV文件
    if (file.mimetype === 'text/plain' || 
        file.mimetype === 'text/csv' || 
        file.originalname.endsWith('.txt') || 
        file.originalname.endsWith('.csv')) {
      cb(null, true);
    } else {
      cb(new Error('只支持txt和csv文件格式'));
    }
  }
});

// ==================== 风控关键词管理 ====================

/**
 * 获取风控关键词列表
 */
router.get('/keywords', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      keyword,
      keyword_type,
      risk_level,
      is_active
    } = req.query;

    const filters = {};
    if (keyword) filters.keyword = keyword;
    if (keyword_type) filters.keyword_type = keyword_type;
    if (risk_level) filters.risk_level = risk_level;
    if (is_active !== undefined) filters.is_active = is_active === 'true';

    const result = await mysqlModel.getRiskKeywords(filters, {
      page: parseInt(page),
      limit: parseInt(limit)
    });

    res.success(result, '获取成功');
  } catch (error) {
    console.error('获取风控关键词失败:', error);
    res.error('获取失败', 500, { error: error.message });
  }
});

/**
 * 添加风控关键词
 */
router.post('/keywords', async (req, res) => {
  try {
    const { keyword, keyword_type, risk_level, description, is_active } = req.body;

    if (!keyword) {
      return res.error('关键词不能为空', 400);
    }

    const keywordId = await mysqlModel.addRiskKeyword({
      keyword,
      keyword_type: keyword_type || 'strict',
      risk_level: risk_level || 'medium',
      description: description || '',
      is_active: is_active !== undefined ? is_active : true
    });

    res.success({ id: keywordId }, '添加成功');

    // 广播敏感词库更新
    webSocketService.broadcastSensitiveWordsUpdate();

    // console.log(`✅ 添加风控关键词: ${keyword}`.green);
  } catch (error) {
    console.error('添加风控关键词失败:', error);
    res.error('添加失败', 500, { error: error.message });
  }
});

/**
 * 删除风控关键词
 */
router.delete('/keywords/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const success = await mysqlModel.deleteRiskKeyword(id);
      if (success) {
      res.success(null, '删除成功');
      
      // 广播敏感词库更新
      webSocketService.broadcastSensitiveWordsUpdate();
      
      // console.log(`🗑️ 删除风控关键词: ${id}`.yellow);
    } else {
      res.error('关键词不存在', 404);
    }
  } catch (error) {
    console.error('删除风控关键词失败:', error);
    res.error('删除失败', 500, { error: error.message });
  }
});

/**
 * 批量导入风控关键词
 */
router.post('/keywords/import', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.error('请上传文件', 400);
    }

    const fileContent = req.file.buffer.toString('utf-8');
    const lines = fileContent.split('\n').filter(line => line.trim());
    
    const keywords = [];
    
    // 检查是否为CSV格式
    const isCSV = req.file.originalname.endsWith('.csv') || 
                  lines[0].includes(',') || 
                  lines[0].includes('\t');

    if (isCSV) {
      // CSV格式解析: keyword,type,level,description
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line || line.startsWith('#')) continue; // 跳过空行和注释

        const parts = line.split(',').map(part => part.trim().replace(/['"]/g, ''));
        
        if (parts.length >= 1) {
          keywords.push({
            keyword: parts[0],
            keyword_type: parts[1] || 'strict',
            risk_level: parts[2] || 'medium',
            description: parts[3] || '',
            is_active: true
          });
        }
      }
    } else {
      // 纯文本格式，每行一个关键词
      for (const line of lines) {
        const keyword = line.trim();
        if (keyword && !keyword.startsWith('#')) {
          keywords.push({
            keyword: keyword,
            keyword_type: 'strict',
            risk_level: 'medium',
            description: '',
            is_active: true
          });
        }
      }
    }

    if (keywords.length === 0) {
      return res.error('文件中没有有效的关键词', 400);
    }    const result = await mysqlModel.batchImportRiskKeywords(keywords);

    res.success({
        totalCount: keywords.length,
        successCount: result.successCount,
        errorCount: result.errorCount,
        errors: result.errors
    }, '导入完成');

    // 广播敏感词库更新
    webSocketService.broadcastSensitiveWordsUpdate();

    // console.log(`📥 批量导入风控关键词: 成功${result.successCount}个，失败${result.errorCount}个`.green);
  } catch (error) {
    console.error('批量导入失败:', error);
    res.error('导入失败', 500, { error: error.message });
  }
});

/**
 * 导出风控关键词
 */
router.get('/keywords/export', async (req, res) => {
  try {
    const { format = 'csv' } = req.query;
    
    const result = await mysqlModel.getRiskKeywords({}, { limit: 10000 });
    const keywords = result.keywords;

    let content = '';
    let contentType = 'text/plain';
    let filename = '';

    if (format === 'csv') {
      contentType = 'text/csv';
      filename = `risk_keywords_${new Date().toISOString().split('T')[0]}.csv`;
      
      // CSV头部
      content = 'keyword,keyword_type,risk_level,description,is_active,created_time\n';
      
      // CSV内容
      for (const keyword of keywords) {
        content += `"${keyword.keyword}","${keyword.keyword_type}","${keyword.risk_level}","${keyword.description || ''}","${keyword.is_active}","${keyword.created_time}"\n`;
      }
    } else {
      contentType = 'text/plain';
      filename = `risk_keywords_${new Date().toISOString().split('T')[0]}.txt`;
      
      // 纯文本格式
      content = keywords.map(k => k.keyword).join('\n');
    }

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.send(content);

    // console.log(`📤 导出风控关键词: ${keywords.length}个，格式: ${format}`.blue);
  } catch (error) {
    console.error('导出风控关键词失败:', error);
    res.error('导出失败', 500, { error: error.message });
  }
});

// ==================== 风控记录管理 ====================

/**
 * 获取风控记录列表
 */
router.get('/records', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      status,
      risk_level,
      ip_address,
      uin,
      startDate,
      endDate
    } = req.query;

    const filters = {};
    if (status) filters.status = status;
    if (risk_level) filters.risk_level = risk_level;
    if (ip_address) filters.ip_address = ip_address;
    if (uin) filters.uin = uin;
    if (startDate) filters.startDate = startDate;
    if (endDate) filters.endDate = endDate;

    const result = await mysqlModel.getRiskRecords(filters, {
      page: parseInt(page),
      limit: parseInt(limit)
    });

    res.success(result, '获取成功');
  } catch (error) {
    console.error('获取风控记录失败:', error);
    res.error('获取失败', 500, { error: error.message });
  }
});

/**
 * 获取单个风控记录详情
 */
router.get('/records/:id', async (req, res) => {
  try {
    const { id } = req.params;

    if (!id || isNaN(parseInt(id))) {
      return res.error('无效的记录ID', 400);
    }

    const record = await mysqlModel.getRiskRecordById(parseInt(id));

    if (!record) {
      return res.error('记录不存在', 404);
    }

    res.success(record, '获取成功');
  } catch (error) {
    console.error('获取风控记录详情失败:', error);
    res.error('获取失败', 500, { error: error.message });
  }
});

/**
 * 审核风控记录
 */
router.put('/records/:id/review', async (req, res) => {
  try {
    const { id } = req.params;
    const { status, review_reason, ban_type, ban_duration } = req.body;

    if (!status || !['approved', 'rejected', 'ignored'].includes(status)) {
      return res.error('无效的审核状态', 400);
    }

    const success = await mysqlModel.reviewRiskRecord(id, {
      status,
      reviewer_id: req.user?.id || null, // 如果有用户认证系统
      review_reason: review_reason || '',
      ban_type: ban_type || 'permanent',
      ban_duration: ban_duration || null
    });

    if (success) {
      res.success(null, '审核成功');

      // 通知WebSocket客户端
      webSocketService.broadcastToAuthenticated({
        type: 'risk_record_reviewed',
        data: {
          recordId: id,
          status: status,
          timestamp: new Date().toISOString()
        }
      });

      // console.log(`🔍 风控记录审核: ${id} -> ${status}`.cyan);
    } else {
      res.error('审核失败', 500);
    }
  } catch (error) {
    console.error('审核风控记录失败:', error);
    res.error('审核失败', 500, { error: error.message });
  }
});

// ==================== 黑名单管理 ====================

/**
 * 获取黑名单列表
 */
router.get('/blacklist', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      username,
      device_id,
      ip_address,
      uin,
      ban_type,
      is_active
    } = req.query;

    const filters = {};
    if (username) filters.username = username;
    if (device_id) filters.device_id = device_id;
    if (ip_address) filters.ip_address = ip_address;
    if (uin) filters.uin = uin;
    if (ban_type) filters.ban_type = ban_type;
    if (is_active !== undefined) filters.is_active = is_active === 'true';

    const result = await mysqlModel.getBlacklist(filters, {
      page: parseInt(page),
      limit: parseInt(limit)
    });

    res.success(result, '获取成功');
  } catch (error) {
    console.error('获取黑名单失败:', error);
    res.error('获取失败', 500, { error: error.message });
  }
});

/**
 * 添加到黑名单
 */
router.post('/blacklist', async (req, res) => {
  try {
    const data = { ...req.body };
    // 可选兜底
    data.ban_type = data.ban_type || 'permanent';
    data.ban_reason = data.ban_reason || '';
    const result = await mysqlModel.addToBlacklist(data);
    if (result && result.success) {
      res.success(null, '添加到黑名单成功');
    } else {
      res.error('添加到黑名单失败', 500);
    }
  } catch (error) {
    console.error('添加到黑名单失败:', error);
    res.error('添加到黑名单失败', 500);
  }
});

/**
 * 从黑名单移除
 */
router.delete('/blacklist/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const success = await mysqlModel.removeFromBlacklist(id);
    
    if (success) {
      res.success(null, '移除成功');

      // 通知WebSocket客户端
      webSocketService.broadcastToAuthenticated({
        type: 'blacklist_updated',
        data: {
          action: 'remove',
          id: id,
          timestamp: new Date().toISOString()
        }
      });

      // console.log(`✅ 移除黑名单: ${id}`.green);
    } else {
      res.error('记录不存在', 404);
    }
  } catch (error) {
    console.error('移除黑名单失败:', error);
    res.error('移除失败', 500, { error: error.message });
  }
});

// 获取单条黑名单详情
router.get('/blacklist/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const result = await mysqlModel.getBlacklist({ id }, { page: 1, limit: 1 });
    const record = result.blacklist && result.blacklist.length > 0 ? result.blacklist[0] : null;
    if (record) {
      res.success(record, '获取成功');
    } else {
      res.error('记录不存在', 404);
    }
  } catch (error) {
    console.error('获取黑名单详情失败:', error);
    res.error('获取失败', 500, { error: error.message });
  }
});

// 更新黑名单记录
router.put('/blacklist/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const data = { ...req.body };

    const result = await mysqlModel.updateBlacklist(id, data);

    if (result.success) {
      res.success(null, '更新成功');

      // 通知WebSocket客户端
      webSocketService.broadcastToAuthenticated({
        type: 'blacklist_updated',
        data: {
          action: 'update',
          id: id,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      res.error('记录不存在或更新失败', 404);
    }
  } catch (error) {
    console.error('更新黑名单失败:', error);
    res.error('更新失败', 500, { error: error.message });
  }
});

// ==================== 白名单管理 ====================

/**
 * 获取白名单列表
 */
router.get('/whitelist', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 50,
      username,
      device_id,
      ip_address,
      uin,
      whitelist_type,
      is_active
    } = req.query;

    const filters = {};
    if (username) filters.username = username;
    if (device_id) filters.device_id = device_id;
    if (ip_address) filters.ip_address = ip_address;
    if (uin) filters.uin = uin;
    if (whitelist_type) filters.whitelist_type = whitelist_type;
    if (is_active !== undefined) filters.is_active = is_active === 'true';

    const result = await mysqlModel.getWhitelist(filters, {
      page: parseInt(page),
      limit: parseInt(limit)
    });

    res.success(result, '获取成功');
  } catch (error) {
    console.error('获取白名单失败:', error);
    res.error('获取失败', 500, { error: error.message });
  }
});

/**
 * 添加到白名单
 */
router.post('/whitelist', async (req, res) => {
  try {
    const { username, device_id, ip_address, uin, whitelist_type, reason } = req.body;
    // 字段兜底
    const data = {
      username: username || null,
      device_id: device_id || null,
      ip_address: ip_address || null,
      uin: uin || null,
      whitelist_type: whitelist_type || 'manual',
      reason: reason || '',
      creator_id: null
    };
    const result = await mysqlModel.addToWhitelist(data);
    if (result && result.success) {
      res.success(null, '添加到白名单成功');
    } else {
      res.error('添加到白名单失败', 500);
    }
  } catch (error) {
    console.error('添加到白名单失败:', error);
    res.error('添加到白名单失败', 500);
  }
});

/**
 * 更新白名单记录
 */
router.put('/whitelist/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const data = { ...req.body };

    const result = await mysqlModel.updateWhitelist(id, data);

    if (result.success) {
      res.success(null, '更新成功');

      // 通知WebSocket客户端
      webSocketService.broadcastToAuthenticated({
        type: 'whitelist_updated',
        data: {
          action: 'update',
          id: id,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      res.error('记录不存在或更新失败', 404);
    }
  } catch (error) {
    console.error('更新白名单失败:', error);
    res.error('更新失败', 500, { error: error.message });
  }
});

/**
 * 从白名单移除
 */
router.delete('/whitelist/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const success = await mysqlModel.removeFromWhitelist(id);

    if (success) {
      res.success(null, '移除成功');

      // 通知WebSocket客户端
      webSocketService.broadcastToAuthenticated({
        type: 'whitelist_updated',
        data: {
          action: 'remove',
          id: id,
          timestamp: new Date().toISOString()
        }
      });

      // console.log(`🗑️ 移除白名单: ${id}`.yellow);
    } else {
      res.error('记录不存在', 404);
    }
  } catch (error) {
    console.error('移除白名单失败:', error);
    res.error('移除失败', 500, { error: error.message });
  }
});

// ==================== 风控统计 ====================

/**
 * 获取风控统计数据
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = await mysqlModel.getRiskControlStats();
    
    res.success(stats, '获取成功');
  } catch (error) {
    console.error('获取风控统计失败:', error);
    res.error('获取失败', 500, { error: error.message });
  }
});

// ==================== WebSocket管理 ====================

/**
 * 获取WebSocket服务状态
 */
router.get('/websocket/status', (req, res) => {
  try {
    const status = webSocketService.getStatus();
    
    res.success(status, '获取成功');
  } catch (error) {
    console.error('获取WebSocket状态失败:', error);
    res.error('获取失败', 500, { error: error.message });
  }
});

/**
 * 获取WebSocket客户端列表
 */
router.get('/websocket/clients', (req, res) => {
  try {
    const clients = webSocketService.getClients();
    
    res.success(clients, '获取成功');
  } catch (error) {
    console.error('获取WebSocket客户端失败:', error);
    res.error('获取失败', 500, { error: error.message });
  }
});

/**
 * 踢出WebSocket客户端
 */
router.post('/websocket/clients/:clientId/kick', (req, res) => {
  try {
    const { clientId } = req.params;
    const { reason = '管理员操作', timestamp } = req.body;
    
    // console.log(`📥 收到踢出请求 - 客户端ID: ${clientId}, 原因: ${reason}`.cyan);
    
    if (!clientId) {
      // console.log('❌ 客户端ID为空'.red);
      return res.error('客户端ID不能为空', 400);
    }
    
    // 检查WebSocket服务是否运行
    if (!webSocketService.isRunning) {
      // console.log('❌ WebSocket服务未运行'.red);
      return res.error('WebSocket服务未运行', 503);
    }
    
    const success = webSocketService.kickClient(clientId, reason);
    
    if (success) {
      // console.log(`✅ 成功踢出WebSocket客户端: ${clientId}`.green);
      res.success({
          clientId: clientId,
          reason: reason,
          timestamp: new Date().toISOString()
      }, '踢出成功');
    } else {
      // console.log(`❌ 客户端不存在或已断开: ${clientId}`.yellow);
      res.error('客户端不存在或已断开连接', 404);
    }
  } catch (error) {
    console.error('💥 踢出WebSocket客户端失败:', error);
    res.error('踢出失败', 500, { error: error.message });
  }
});

// ==================== 风控检测API ====================

// ==================== 风控系统配置管理 ====================

/**
 * 获取风控系统配置
 */
router.get('/settings', async (req, res) => {
  try {
    const { category } = req.query;
    const settings = await mysqlModel.getRiskControlSettings(category);

    res.success(settings, '获取配置成功');
  } catch (error) {
    console.error('获取风控配置失败:', error);
    res.error('获取配置失败', 500, { error: error.message });
  }
});

/**
 * 更新风控系统配置
 */
router.put('/settings/:key', async (req, res) => {
  try {
    const { key } = req.params;
    const { value, type = 'string' } = req.body;

    if (value === undefined) {
      return res.error('配置值不能为空', 400);
    }

    await mysqlModel.updateRiskControlSetting(key, value, type);

    res.success(null, '配置更新成功');

    // 广播配置更新
    webSocketService.broadcastToAuthenticated({
      type: 'risk_settings_updated',
      data: {
        key,
        value,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('更新风控配置失败:', error);
    res.error('更新配置失败', 500, { error: error.message });
  }
});

// ==================== 批量操作接口 ====================

/**
 * 批量审核风控记录
 */
router.post('/records/batch-review', async (req, res) => {
  try {
    const { record_ids, status, review_reason } = req.body;

    if (!Array.isArray(record_ids) || record_ids.length === 0) {
      return res.error('记录ID列表不能为空', 400);
    }

    if (!status || !['approved', 'rejected', 'ignored'].includes(status)) {
      return res.error('无效的审核状态', 400);
    }

    // 检查批量操作限制
    const settings = await mysqlModel.getRiskControlSettings('batch');
    const batchLimit = settings.batch_operation_limit?.value || 100;

    if (record_ids.length > batchLimit) {
      return res.error(`批量操作数量不能超过 ${batchLimit} 条`, 400);
    }

    // 检查是否启用批量审核
    if (!settings.enable_batch_review?.value) {
      return res.error('批量审核功能已禁用', 403);
    }

    const result = await mysqlModel.batchReviewRiskRecords(record_ids, {
      status,
      reviewer_id: req.user?.id || null,
      review_reason: review_reason || ''
    });

    res.success(result, `批量审核完成，共处理 ${result.affectedRows} 条记录`);

    // 广播批量审核事件
    webSocketService.broadcastToAuthenticated({
      type: 'batch_review_completed',
      data: {
        recordIds: record_ids,
        status,
        count: result.affectedRows,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('批量审核失败:', error);
    res.error('批量审核失败', 500, { error: error.message });
  }
});

/**
 * 批量删除风控记录
 */
router.delete('/records/batch-delete', async (req, res) => {
  try {
    const { record_ids } = req.body;

    if (!Array.isArray(record_ids) || record_ids.length === 0) {
      return res.error('记录ID列表不能为空', 400);
    }

    // 检查批量操作限制
    const settings = await mysqlModel.getRiskControlSettings('batch');
    const batchLimit = settings.batch_operation_limit?.value || 100;

    if (record_ids.length > batchLimit) {
      return res.error(`批量操作数量不能超过 ${batchLimit} 条`, 400);
    }

    // 检查是否启用批量删除
    if (!settings.enable_batch_delete?.value) {
      return res.error('批量删除功能已禁用', 403);
    }

    const result = await mysqlModel.batchDeleteRiskRecords(record_ids);

    res.success(result, `批量删除完成，共删除 ${result.deletedCount} 条记录`);

    // 广播批量删除事件
    webSocketService.broadcastToAuthenticated({
      type: 'batch_delete_completed',
      data: {
        recordIds: record_ids,
        count: result.deletedCount,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('批量删除失败:', error);
    res.error('批量删除失败', 500, { error: error.message });
  }
});

/**
 * 批量加入黑名单
 */
router.post('/records/batch-blacklist', async (req, res) => {
  try {
    const { record_ids, ban_reason, ban_type = 'permanent', ban_duration } = req.body;

    if (!Array.isArray(record_ids) || record_ids.length === 0) {
      return res.error('记录ID列表不能为空', 400);
    }

    // 检查批量操作限制
    const settings = await mysqlModel.getRiskControlSettings('batch');
    const batchLimit = settings.batch_operation_limit?.value || 100;

    if (record_ids.length > batchLimit) {
      return res.error(`批量操作数量不能超过 ${batchLimit} 条`, 400);
    }

    // 检查是否启用批量加入黑名单
    if (!settings.enable_batch_blacklist?.value) {
      return res.error('批量加入黑名单功能已禁用', 403);
    }

    // 获取记录详情
    const records = [];
    for (const id of record_ids) {
      const record = await mysqlModel.getRiskRecordById(id);
      if (record) {
        records.push(record);
      }
    }

    if (records.length === 0) {
      return res.error('未找到有效的记录', 404);
    }

    const result = await mysqlModel.batchAddToBlacklist(records, {
      ban_reason: ban_reason || '批量风控处理',
      ban_type,
      ban_duration,
      creator_id: req.user?.id || null
    });

    res.success(result, `批量加入黑名单完成，成功 ${result.successCount} 条，失败 ${result.failCount} 条`);

    // 广播批量加入黑名单事件
    webSocketService.broadcastToAuthenticated({
      type: 'batch_blacklist_completed',
      data: {
        recordIds: record_ids,
        successCount: result.successCount,
        failCount: result.failCount,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('批量加入黑名单失败:', error);
    res.error('批量加入黑名单失败', 500, { error: error.message });
  }
});

/**
 * 手动风控检测
 */
router.post('/check', async (req, res) => {
  try {
    const { content, clientInfo = {} } = req.body;

    if (!content) {
      return res.error('检测内容不能为空', 400);
    }

    // 检查白名单
    const isWhitelisted = await mysqlModel.isWhitelisted(clientInfo);
    if (isWhitelisted) {
      return res.success({
          isRisk: false,
          isWhitelisted: true,
          action: 'allow',
          message: '白名单用户，允许通过'
      }, '检测完成');
    }

    // 检查黑名单
    const blacklistResult = await mysqlModel.isBlacklisted(clientInfo);
    if (blacklistResult.isBlacklisted) {
      return res.success({
          isRisk: true,
          isBlacklisted: true,
          action: 'block',
          message: '黑名单用户，禁止访问',
          banInfo: blacklistResult.record
      }, '检测完成');
    }

    // 风控内容检测
    const riskResult = await mysqlModel.checkRiskContent(content, clientInfo);
    
    // 记录风控数据
    if (riskResult.isRisk) {
      await mysqlModel.recordRiskData({
        username: clientInfo.username,
        device_id: clientInfo.device_id,
        ip_address: clientInfo.ip_address || req.ip,
        uin: clientInfo.uin,
        risk_content: content,
        matched_keywords: riskResult.matchedKeywords,
        risk_score: riskResult.riskScore,
        risk_level: riskResult.riskLevel,
        auto_blocked: riskResult.autoBlock,
        client_info: clientInfo,
        request_data: JSON.stringify(req.body)
      });
    }

    res.success({
        isRisk: riskResult.isRisk,
        riskScore: riskResult.riskScore,
        riskLevel: riskResult.riskLevel,
        matchedKeywords: riskResult.matchedKeywords,
        action: riskResult.autoBlock ? 'block' : 'allow',
        message: riskResult.autoBlock ? '检测到高风险内容，已自动拦截' : 
                 riskResult.isRisk ? '检测到风险内容，请人工审核' : '内容安全'
    }, '检测完成');

  } catch (error) {
    console.error('风控检测失败:', error);
    res.error('检测失败', 500, { error: error.message });
  }
});

module.exports = router;
