const got = require('got');
const colors = require('colors');

// 响应工具函数
function sendSuccessResponse(res, code = 0, data = null) {
    const response = {
        code,
        msg: 'success',
        data
    };
    res.json(response);
}

function sendErrorResponse(res, code, msg) {
    const response = {
        code,
        msg: msg || 'error'
    };
    res.status(400).json(response);
}

// QQ身份验证1 - 初始验证和获取手机号
async function qqLoginVerify1(req, res) {
    try {
        const { version, sig, ticket, randstr, guid, qimei, appid, brand, model } = req.body;
        
        console.log('[QQ验证1] 开始身份验证流程'.cyan, {
            version, guid, appid, brand, model
        });

        const headers = {
            "qname-service": "1935233:65536",
            "qname-space": "Production",
            "Cookie": "uin=; p_uin=; p_uid=; login_key_set_failed=AlreadyLogout",
            "User-Agent": "Mozilla/5.0 (Linux; Android 12; SM-G975F) AppleWebKit/537.36"
        };

        let results;

        // 1. 验证验证码
        console.log('[QQ验证1] 步骤1: 验证验证码'.yellow);
        results = await got.post("https://accounts.qq.com/login/limit/proxy/domain/qq110.qq.com/v3/chkcaptcha?uin=0&bkn=", {
            headers,
            json: {
                "com": {
                    "src": 1,
                    "scene": 100607,
                    "platform": 2,
                    "version": version,
                    "unlgn": {
                        "uin": 0,
                        "sig": sig,
                        "sigType": 1,
                        "randstr": randstr,
                    }
                },
                "ticket": ticket,
                "randStr": randstr,
                "appid": **********
            }
        }).json();
        
        console.log('[QQ验证1] chkcaptcha 结果:'.green, results);

        if (results.retcode !== 0) {
            console.log('[QQ验证1] 验证码验证失败:'.red, results.retmsg);
            return sendErrorResponse(res, results.retcode, results.retmsg);
        }
        
        // 2. 检查风险
        console.log('[QQ验证1] 步骤2: 检查风险'.yellow);
        results = await got.post("https://accounts.qq.com/login/limit/proxy/domain/qq110.qq.com/v3/chkrisk?uin=0&bkn=", {
            headers,
            json: {
                "com": {
                    "src": 1,
                    "scene": 101107,
                    "platform": 2,
                    "version": version,
                    "unlgn": {
                        "uin": 0,
                        "sig": sig,
                        "sigType": 1,
                        "randstr": randstr,
                    },
                    "device": {
                        "guid": guid,
                        "qimei": qimei,
                        "qimei36": qimei,
                        "subappid": appid,
                        "platform": "Android",
                        "brand": brand,
                        "model": model,
                        "bssid": "",
                        "devInfo": ""
                    }
                },
            }
        }).json();
        
        console.log('[QQ验证1] chkrisk 结果:'.green, results);

        if (results.retcode !== 0) {
            console.log('[QQ验证1] 风险检查失败:'.red, results.retmsg);
            return sendErrorResponse(res, results.retcode, results.retmsg);
        }

        // 3. 查询登录验证方法
        console.log('[QQ验证1] 步骤3: 查询登录验证方法'.yellow);
        results = await got.post("https://accounts.qq.com/login/limit/proxy/domain/qq110.qq.com/v3/queryloginverifymethod?uin=0&bkn=", {
            headers,
            json: {
                "com": {
                    "src": 1,
                    "scene": 102805,
                    "platform": 2,
                    "version": version,
                    "unlgn": {
                        "uin": 0,
                        "sig": sig,
                        "sigType": 1,
                        "randstr": randstr
                    }
                }
            }
        }).json();
        
        console.log('[QQ验证1] queryloginverifymethod 结果:'.green, results);

        if (results.retcode !== 0) {
            console.log('[QQ验证1] 查询验证方法失败:'.red, results.retmsg);
            return sendErrorResponse(res, results.retcode, results.retmsg);
        }

        // 4. 查询绑定手机号
        console.log('[QQ验证1] 步骤4: 查询绑定手机号'.yellow);
        results = await got.post("https://accounts.qq.com/login/limit/proxy/domain/qq110.qq.com/v3/query_bound_phone?uin=0&bkn=", {
            headers,
            json: {
                "com": {
                    "src": 1,
                    "scene": 103402,
                    "platform": 2,
                    "version": version,
                    "unlgn": {
                        "uin": 0,
                        "sig": sig,
                        "sigType": 1,
                        "randstr": randstr,
                    }
                }
            }
        }).json();
        
        console.log('[QQ验证1] query_bound_phone 结果:'.green, results);

        if (results.retcode !== 0) {
            console.log('[QQ验证1] 查询绑定手机号失败:'.red, results.retmsg);
            return sendErrorResponse(res, results.retcode, results.retmsg);
        }

        const { areaCode, phoneNum } = results;
        
        console.log('[QQ验证1] 验证成功，返回手机号信息'.green, { areaCode, phoneNum });

        return sendSuccessResponse(res, 0, {
            areaCode,
            phoneNum
        });

    } catch (error) {
        console.error('[QQ验证1] 处理错误:'.red, error.message);
        return sendErrorResponse(res, -1, `验证过程出错: ${error.message}`);
    }
}

// QQ身份验证2 - 发送短信验证码
async function qqLoginVerify2(req, res) {
    try {
        const { version, sig, randstr, areaCode, phoneNum, mobile } = req.body;
        
        console.log('[QQ验证2] 开始短信验证流程'.cyan, {
            areaCode, phoneNum, mobile, clientIP: req.ip
        });

        const headers = {
            "qname-service": "1935233:65536",
            "qname-space": "Production",
            "Cookie": "uin=; p_uin=; p_uid=; login_key_set_failed=AlreadyLogout",
            "User-Agent": "Mozilla/5.0 (Linux; Android 12; SM-G975F) AppleWebKit/537.36"
        };

        let results;

        // 1. 验证手机号
        console.log('[QQ验证2] 步骤1: 验证手机号'.yellow);
        results = await got.post("https://accounts.qq.com/login/limit/proxy/domain/qq110.qq.com/v3/verifymbphone?uin=0&bkn=", {
            headers,
            json: {
                "com": {
                    "src": 1,
                    "scene": 100908,
                    "platform": 2,
                    "version": version,
                    "unlgn": {
                        "uin": 0,
                        "sig": sig,
                        "sigType": 1,
                        "randstr": randstr,
                    }
                },
                "mobile": mobile,
                "areaCode": areaCode
            }
        }).json();
        
        console.log('[QQ验证2] verifymbphone 结果:'.green, results);

        if (results.retcode !== 0) {
            console.log('[QQ验证2] 手机号验证失败:'.red, results.retmsg);
            return sendErrorResponse(res, results.retcode, results.retmsg);
        }

        // 2. 获取短信验证码
        console.log('[QQ验证2] 步骤2: 发送短信验证码'.yellow);
        results = await got.post("https://accounts.qq.com/login/limit/proxy/domain/qq110.qq.com/v3/getsms?uin=0&bkn=", {
            headers,
            json: {
                "com": {
                    "src": 1,
                    "scene": 100331,
                    "platform": 2,
                    "version": version,
                    "unlgn": {
                        "uin": 0,
                        "sig": sig,
                        "sigType": 1,
                        "randstr": randstr,
                    }
                },
                "way": 4,
                "mobile": phoneNum,
                "areaCode": areaCode
            }
        }).json();
        
        console.log('[QQ验证2] getsms 结果:'.green, results);

        if (results.retcode !== 0) {
            console.log('[QQ验证2] 获取短信验证码失败:'.red, results.retmsg);
            return sendErrorResponse(res, results.retcode, results.retmsg);
        }
        
        const { sms, sendTo } = results;
        
        console.log('[QQ验证2] 短信发送成功'.green, { sms, sendTo });

        return sendSuccessResponse(res, 0, {
            sms,
            sendTo
        });

    } catch (error) {
        console.error('[QQ验证2] 处理错误:'.red, error.message);
        return sendErrorResponse(res, -1, `短信验证过程出错: ${error.message}`);
    }
}

// QQ身份验证3 - 验证短信码并完成认证
async function qqLoginVerify3(req, res) {
    try {
        const { version, guid, qimei, appid, brand, model, sig, randstr, areaCode, phoneNum, smsCode } = req.body;
        
        console.log('[QQ验证3] 开始短信验证确认流程'.cyan, {
            areaCode, phoneNum, guid, appid, brand, model
        });

        const headers = {
            "qname-service": "1935233:65536",
            "qname-space": "Production",
            "Cookie": "uin=; p_uin=; p_uid=; login_key_set_failed=AlreadyLogout",
            "User-Agent": "Mozilla/5.0 (Linux; Android 12; SM-G975F) AppleWebKit/537.36"
        };

        let results;

        // 1. 验证短信验证码
        console.log('[QQ验证3] 步骤1: 验证短信验证码'.yellow);
        results = await got.post("https://accounts.qq.com/login/limit/proxy/domain/qq110.qq.com/v3/chksms?uin=0&bkn=", {
            headers,
            json: {
                "com": {
                    "src": 1,
                    "scene": 100331,
                    "platform": 2,
                    "version": version,
                    "unlgn": {
                        "uin": 0,
                        "sig": sig,
                        "sigType": 1,
                        "randstr": randstr,
                    }
                },
                "way": 4,
                "mobile": phoneNum,
                "areaCode": areaCode,
                "smsCode": smsCode // 添加短信验证码参数
            }
        }).json();
        
        console.log('[QQ验证3] chksms 结果:'.green, results);

        if (results.retcode !== 0) {
            console.log('[QQ验证3] 短信验证码验证失败:'.red, results.retmsg);
            return sendErrorResponse(res, results.retcode, results.retmsg);
        }

        const { key } = results;

        // 2. 完成身份验证
        console.log('[QQ验证3] 步骤2: 完成身份验证'.yellow);
        results = await got.post("https://accounts.qq.com/login/limit/proxy/domain/qq110.qq.com/v3/auth_diff_password?uin=0&bkn=", {
            headers,
            json: {
                "com": {
                    "src": 1,
                    "scene": 103301,
                    "platform": 2,
                    "version": version,
                    "unlgn": {
                        "uin": 0,
                        "sig": sig,
                        "sigType": 1,
                        "randstr": randstr,
                    },
                    "device": {
                        "guid": guid.toLowerCase(),
                        "qimei": qimei,
                        "qimei36": qimei,
                        "subappid": appid,
                        "platform": "Android",
                        "brand": brand,
                        "model": model
                    }
                },
                "token": sig,
                "type": 0,
                "ticket": {
                    "ticket0": {
                        "way": 4,
                        "keyType": 40,
                        "key": key,
                        "mobile": phoneNum
                    }
                }
            }
        }).json();
        
        console.log('[QQ验证3] auth_diff_password 结果:'.green, results);
        
        if (results.retcode !== 0) {
            console.log('[QQ验证3] 身份验证失败:'.red, results.retmsg);
            return sendErrorResponse(res, results.retcode, results.retmsg);
        }

        console.log('[QQ验证3] 身份验证完成'.green);

        return sendSuccessResponse(res, 0, {
            msg: '身份验证成功',
            authToken: results.authToken || 'verification_completed'
        });

    } catch (error) {
        console.error('[QQ验证3] 处理错误:'.red, error.message);
        return sendErrorResponse(res, -1, `最终验证过程出错: ${error.message}`);
    }
}

module.exports = {
    qqLoginVerify1,
    qqLoginVerify2,
    qqLoginVerify3
};
